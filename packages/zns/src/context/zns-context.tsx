import React, { createContext, useContext, useState, useCallback } from 'react';
import { initZnsEventHandler } from '../lib/event-handler';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';

interface ZnsContextType {
  isInitialized: boolean;
  initialize: (supabase: ReturnType<typeof createClient<Database>>, teamAccountId?: string) => void;
  shutdown: () => void;
}

const ZnsContext = createContext<ZnsContextType | undefined>(undefined);

/**
 * ZNS Provider - Cung cấp context cho việc quản lý ZNS Event Handler
 * 
 * Sử dụng:
 * ```tsx
 * <ZnsProvider>
 *   <YourComponent />
 * </ZnsProvider>
 * ```
 */
export const ZnsProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [handlerId, setHandlerId] = useState<string | null>(null);

  /**
   * Khởi tạo ZNS Event Handler
   * @param supabase Supabase client
   * @param teamAccountId ID của team account (optional)
   */
  const initialize = useCallback((
    supabase: ReturnType<typeof createClient<Database>>, 
    teamAccountId?: string
  ) => {
    if (isInitialized) return;
    
    // Khởi tạo Event Handler với ID duy nhất
    const id = `zns_handler_${Date.now()}`;
    initZnsEventHandler(supabase, id);
    setHandlerId(id);
    setIsInitialized(true);
    
    console.log('ZNS Event Handler initialized for team account:', teamAccountId);
  }, [isInitialized]);

  /**
   * Shutdown ZNS Event Handler
   */
  const shutdown = useCallback(() => {
    if (!isInitialized || !handlerId) return;
    
    // Logic để shutdown Event Handler
    // Trong tương lai có thể thêm hàm cleanup trong event-handler.ts
    
    setIsInitialized(false);
    setHandlerId(null);
    console.log('ZNS Event Handler shutdown');
  }, [isInitialized, handlerId]);

  return (
    <ZnsContext.Provider value={{ isInitialized, initialize, shutdown }}>
      {children}
    </ZnsContext.Provider>
  );
};

/**
 * Hook để sử dụng ZNS Context
 * 
 * Sử dụng:
 * ```tsx
 * const { initialize, isInitialized } = useZns();
 * ```
 */
export const useZns = () => {
  const context = useContext(ZnsContext);
  if (context === undefined) {
    throw new Error('useZns must be used within a ZnsProvider');
  }
  return context;
};
