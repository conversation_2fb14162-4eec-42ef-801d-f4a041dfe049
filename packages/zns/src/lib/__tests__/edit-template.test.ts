import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { editZnsTemplate, getZnsTemplateById } from '../edit-template';
import { ZnsTemplateTag, ZnsTemplateType } from '../create-template';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock refreshZnsToken
jest.mock('../utils', () => ({
  refreshZnsToken: jest.fn().mockResolvedValue('mocked_refreshed_token'),
}));

describe('editZnsTemplate', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  } as unknown as ReturnType<typeof createClient>;

  const mockOaConfig = {
    id: 'test-oa-config-id',
    app_id: 'test-app-id',
    secret_key: 'test-secret-key',
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    token_expires_at: new Date(Date.now() + 3600 * 1000).toISOString(), // Token còn hạn
    oa_metadata: {},
  };

  const mockTemplateData = {
    template_id: '12345',
    template_name: 'Test Template Updated',
    template_type: ZnsTemplateType.CUSTOM,
    tag: ZnsTemplateTag.TRANSACTION,
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: 'Test Title Updated',
            },
          },
          {
            PARAGRAPH: {
              value: 'Test paragraph content updated',
            },
          },
        ],
      },
    },
    tracking_id: 'test-tracking-id',
  };

  const mockResponse = {
    data: {
      error: 0,
      message: 'Success',
      data: {
        template_id: '12345',
        template_name: 'Test Template Updated',
        template_type: 1,
        status: 'PENDING_REVIEW',
        tag: 1,
        app_id: 'test-app-id',
        oa_id: 'test-oa-id',
        price: '200',
        timeout: 7200000,
        preview_url: 'https://account.zalo.cloud/znspreview/test',
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.single.mockResolvedValue({ data: mockOaConfig, error: null });
    mockedAxios.post.mockResolvedValue(mockResponse);
  });

  it('should edit a ZNS template successfully', async () => {
    const result = await editZnsTemplate(
      mockSupabase as any,
      'test-oa-config-id',
      mockTemplateData
    );

    // Kiểm tra supabase được gọi đúng cách
    expect(mockSupabase.from).toHaveBeenCalledWith('oa_configurations');
    expect(mockSupabase.select).toHaveBeenCalledWith('*');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'test-oa-config-id');
    expect(mockSupabase.single).toHaveBeenCalled();

    // Kiểm tra axios được gọi đúng cách
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'https://business.openapi.zalo.me/template/edit',
      mockTemplateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': 'test-access-token',
        },
      }
    );

    // Kiểm tra kết quả
    expect(result).toEqual(mockResponse.data.data);
  });

  it('should throw an error if OA config is not found', async () => {
    mockSupabase.single.mockResolvedValue({ data: null, error: { message: 'Not found' } });

    await expect(
      editZnsTemplate(mockSupabase as any, 'test-oa-config-id', mockTemplateData)
    ).rejects.toThrow('Failed to fetch OA config: Not found');
  });

  it('should throw an error if API returns an error', async () => {
    mockedAxios.post.mockResolvedValue({
      data: {
        error: 1,
        message: 'API Error',
      },
    });

    await expect(
      editZnsTemplate(mockSupabase as any, 'test-oa-config-id', mockTemplateData)
    ).rejects.toThrow('Failed to edit ZNS template: API Error');
  });
});

describe('getZnsTemplateById', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  } as unknown as ReturnType<typeof createClient>;

  const mockOaConfig = {
    id: 'test-oa-config-id',
    app_id: 'test-app-id',
    secret_key: 'test-secret-key',
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    token_expires_at: new Date(Date.now() + 3600 * 1000).toISOString(), // Token còn hạn
    oa_metadata: {},
  };

  const mockTemplateInfo = {
    template_id: '12345',
    template_name: 'Test Template',
    template_type: 1,
    status: 'REJECT',
    tag: 1,
    app_id: 'test-app-id',
    oa_id: 'test-oa-id',
    price: '200',
    timeout: 7200000,
    preview_url: 'https://account.zalo.cloud/znspreview/test',
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: 'Test Title',
            },
          },
        ],
      },
    },
  };

  const mockResponse = {
    data: {
      error: 0,
      message: 'Success',
      data: mockTemplateInfo,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.single.mockResolvedValue({ data: mockOaConfig, error: null });
    mockedAxios.get.mockResolvedValue(mockResponse);
  });

  it('should get ZNS template info successfully', async () => {
    const result = await getZnsTemplateById(
      mockSupabase as any,
      'test-oa-config-id',
      '12345'
    );

    // Kiểm tra supabase được gọi đúng cách
    expect(mockSupabase.from).toHaveBeenCalledWith('oa_configurations');
    expect(mockSupabase.select).toHaveBeenCalledWith('*');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'test-oa-config-id');
    expect(mockSupabase.single).toHaveBeenCalled();

    // Kiểm tra axios được gọi đúng cách
    expect(mockedAxios.get).toHaveBeenCalledWith(
      'https://openapi.zalo.me/v2.0/oa/template/info',
      {
        params: {
          template_id: '12345',
        },
        headers: {
          'access_token': 'test-access-token',
        },
      }
    );

    // Kiểm tra kết quả
    expect(result).toEqual(mockTemplateInfo);
  });

  it('should throw an error if API returns an error', async () => {
    mockedAxios.get.mockResolvedValue({
      data: {
        error: 1,
        message: 'API Error',
      },
    });

    await expect(
      getZnsTemplateById(mockSupabase as any, 'test-oa-config-id', '12345')
    ).rejects.toThrow('Failed to get ZNS template info: API Error');
  });
});
