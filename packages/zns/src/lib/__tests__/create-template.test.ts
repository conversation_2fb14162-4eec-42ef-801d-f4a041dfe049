import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { createZnsTemplate, ZnsTemplateTag, ZnsTemplateType } from '../create-template';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock refreshZnsToken
jest.mock('../utils', () => ({
  refreshZnsToken: jest.fn().mockResolvedValue('mocked_refreshed_token'),
}));

describe('createZnsTemplate', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  } as unknown as ReturnType<typeof createClient>;

  const mockOaConfig = {
    id: 'test-oa-config-id',
    app_id: 'test-app-id',
    secret_key: 'test-secret-key',
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    token_expires_at: new Date(Date.now() + 3600 * 1000).toISOString(), // Token còn hạn
    oa_metadata: {},
  };

  const mockTemplateData = {
    template_name: 'Test Template',
    template_type: ZnsTemplateType.CUSTOM,
    tag: ZnsTemplateTag.TRANSACTION,
    layout: {
      body: {
        components: [
          {
            TITLE: {
              value: 'Test Title',
            },
          },
          {
            PARAGRAPH: {
              value: 'Test paragraph content',
            },
          },
        ],
      },
    },
    tracking_id: 'test-tracking-id',
  };

  const mockResponse = {
    data: {
      error: 0,
      message: 'Success',
      data: {
        template_id: '12345',
        template_name: 'Test Template',
        template_type: 1,
        status: 'PENDING_REVIEW',
        tag: 1,
        app_id: 'test-app-id',
        oa_id: 'test-oa-id',
        price: '200',
        timeout: 7200000,
        preview_url: 'https://account.zalo.cloud/znspreview/test',
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.single.mockResolvedValue({ data: mockOaConfig, error: null });
    mockedAxios.post.mockResolvedValue(mockResponse);
  });

  it('should create a ZNS template successfully', async () => {
    const result = await createZnsTemplate(
      mockSupabase as any,
      'test-oa-config-id',
      mockTemplateData
    );

    // Kiểm tra supabase được gọi đúng cách
    expect(mockSupabase.from).toHaveBeenCalledWith('oa_configurations');
    expect(mockSupabase.select).toHaveBeenCalledWith('*');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'test-oa-config-id');
    expect(mockSupabase.single).toHaveBeenCalled();

    // Kiểm tra axios được gọi đúng cách
    expect(mockedAxios.post).toHaveBeenCalledWith(
      'https://business.openapi.zalo.me/template/create',
      mockTemplateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': 'test-access-token',
        },
      }
    );

    // Kiểm tra kết quả
    expect(result).toEqual(mockResponse.data.data);
  });

  it('should throw an error if OA config is not found', async () => {
    mockSupabase.single.mockResolvedValue({ data: null, error: { message: 'Not found' } });

    await expect(
      createZnsTemplate(mockSupabase as any, 'test-oa-config-id', mockTemplateData)
    ).rejects.toThrow('Failed to fetch OA config: Not found');
  });

  it('should throw an error if API returns an error', async () => {
    mockedAxios.post.mockResolvedValue({
      data: {
        error: 1,
        message: 'API Error',
      },
    });

    await expect(
      createZnsTemplate(mockSupabase as any, 'test-oa-config-id', mockTemplateData)
    ).rejects.toThrow('Failed to create ZNS template: API Error');
  });

  it('should throw an error if axios request fails', async () => {
    mockedAxios.post.mockRejectedValue({
      response: {
        data: {
          message: 'Network Error',
        },
      },
    });

    await expect(
      createZnsTemplate(mockSupabase as any, 'test-oa-config-id', mockTemplateData)
    ).rejects.toThrow('Failed to create ZNS template: Network Error');
  });
});
