import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { Database } from '@kit/supabase/database';
import { getValidZnsToken } from './utils';

/**
 * Enum for ZNS template status
 */
export enum ZnsTemplateStatus {
  PENDING_REVIEW = 1,
  APPROVED = 2,
  REJECTED = 3,
  DELETED = 4,
}

/**
 * Interface for ZNS message status response
 */
export interface ZnsMessageStatusResponse {
  delivery_time: string;
  status: number;
  message: string;
}

/**
 * Interface for ZNS quota response
 */
export interface ZnsQuotaResponse {
  dailyQuota: string;
  remainingQuota: string;
  dailyQuotaPromotion: string | null;
  remainingQuotaPromotion: string | null;
  monthlyPromotionQuota: number;
  remainingMonthlyPromotionQuota: number;
  estimatedNextMonthPromotionQuota: number;
}

/**
 * Interface for ZNS rating response
 */
export interface ZnsRatingResponse {
  ratings: Array<{
    user_id: string;
    rating: number;
    comment: string;
    created_time: number;
  }>;
  total: number;
}

/**
 * Interface for ZNS quality response
 */
export interface ZnsQualityResponse {
  quality: number;
  qualityStatus: string;
  qualityDescription: string;
}

/**
 * Interface for ZNS template list response
 */
export interface ZnsTemplateListResponse {
  data: Array<{
    templateId: string;
    templateName: string;
    templateType: number;
    status: string;
    tag: number;
    appId: string;
    oaId: string;
    price: string;
    timeout: number;
    previewUrl: string;
    createdTime: number;
    updatedTime: number;
  }>;
  metadata: {
    total: number;
  };
}

/**
 * Interface for ZNS template detail response
 */
export interface ZnsTemplateDetailResponse {
  templateId: string;
  templateName: string;
  templateType: number;
  status: string;
  tag: number;
  appId: string;
  oaId: string;
  price: string;
  timeout: number;
  previewUrl: string;
  createdTime: number;
  updatedTime: number;
  layout?: any;
  params?: Array<{
    name: string;
    type: string;
    sample_value: string;
  }>;
  listParams?: Array<{
    name: string;
    require: boolean;
    type: string;
    maxLength: number;
    minLength: number;
    acceptNull: boolean;
  }>;
  listButtons?: Array<{
    type: number;
    title: string;
    content: string;
  }>;
  reason?: string;
  templateTag?: string;
  templateQuality?: string;
}

/**
 * Interface for ZNS template sample data response
 */
export interface ZnsTemplateSampleDataResponse {
  html: string;
  data: any;
  parameters?: Array<{
    name: string;
    value: string;
  }>;
}

/**
 * Class representing a ZNS API error
 */
export class ZnsApiError extends Error {
  code: number;
  
  constructor(message: string, code: number = -1) {
    super(message);
    this.name = 'ZnsApiError';
    this.code = code;
  }
}

/**
 * Lấy thông tin trạng thái ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param messageId ID của thông báo ZNS
 * @returns Thông tin trạng thái ZNS
 */
export async function getZnsMessageStatus(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  messageId: string
): Promise<ZnsMessageStatusResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin trạng thái ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/status',
      {
        params: {
          message_id: messageId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS message status: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin trạng thái ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS message status: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy thông tin quota ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin quota ZNS
 */
export async function getZnsQuota(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<ZnsQuotaResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin quota ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/quota',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS quota: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin quota ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS quota: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy thông tin loại nội dung ZNS được phép gửi
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin loại nội dung ZNS được phép gửi
 */
export async function getZnsTemplateTag(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<string[]> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin loại nội dung ZNS được phép gửi
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/template-tag',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS template tag: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin loại nội dung ZNS được phép gửi
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS template tag: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy danh sách template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param offset Thứ tự của template đầu tiên trong danh sách trả về
 * @param limit Số lượng template muốn lấy
 * @param status Trạng thái của template muốn lấy
 * @returns Danh sách template ZNS
 */
export async function getZnsTemplateList(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  offset: number = 0,
  limit: number = 100,
  status?: ZnsTemplateStatus
): Promise<ZnsTemplateListResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Tạo params
    const params: Record<string, any> = {
      offset,
      limit
    };

    // Thêm status nếu được cung cấp
    if (status !== undefined) {
      params.status = status;
    }

    // Gọi API lấy danh sách template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/all',
      {
        params,
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS template list: ${response.data.message}`, response.data.error);
    }

    // Trả về danh sách template ZNS
    return {
      data: response.data.data.templates || [],
      metadata: {
        total: response.data.data.total || 0
      }
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS template list: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy thông tin chi tiết template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @returns Thông tin chi tiết template ZNS
 */
export async function getZnsTemplateDetail(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<ZnsTemplateDetailResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin chi tiết template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/info/v2',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS template detail: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin chi tiết template ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS template detail: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy dữ liệu mẫu của template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @returns Dữ liệu mẫu của template ZNS
 * @throws ZnsApiError với code -124 nếu template chưa được phê duyệt
 */
export async function getZnsTemplateSampleData(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<ZnsTemplateSampleDataResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy dữ liệu mẫu của template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/sample-data',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      // Nếu lỗi là "ZNS template not approved", đặt mã lỗi cụ thể
      if (response.data.message && response.data.message.includes('not approved')) {
        throw new ZnsApiError('ZNS template not approved', -124);
      }
      
      throw new ZnsApiError(`Failed to get ZNS template sample data: ${response.data.message}`, response.data.error);
    }

    // Trả về dữ liệu mẫu của template ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      // Nếu lỗi là "ZNS template not approved", đặt mã lỗi cụ thể
      if (error.response.data?.message && error.response.data.message.includes('not approved')) {
        throw new ZnsApiError('ZNS template not approved', -124);
      }
      
      throw new ZnsApiError(`Failed to get ZNS template sample data: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy thông tin đánh giá của khách hàng
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @param fromTime Thời điểm bắt đầu của khoảng thời gian cần lấy dữ liệu
 * @param toTime Thời điểm kết thúc của khoảng thời gian cần lấy dữ liệu
 * @param offset Vị trí thứ tự của đánh giá đầu tiên được trả về
 * @param limit Số lượng đánh giá tối đa được trả về
 * @returns Thông tin đánh giá của khách hàng
 */
export async function getZnsRating(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
  fromTime: number,
  toTime: number,
  offset: number = 0,
  limit: number = 10
): Promise<ZnsRatingResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin đánh giá của khách hàng
    const response = await axios.get(
      'https://business.openapi.zalo.me/rating/get',
      {
        params: {
          template_id: templateId,
          from_time: fromTime,
          to_time: toTime,
          offset,
          limit
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS rating: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin đánh giá của khách hàng
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS rating: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

/**
 * Lấy thông tin chất lượng gửi ZNS hiện tại của OA
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin chất lượng gửi ZNS hiện tại của OA
 */
export async function getZnsQuality(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<ZnsQualityResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin chất lượng gửi ZNS hiện tại của OA
    const response = await axios.get(
      'https://business.openapi.zalo.me/quality',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new ZnsApiError(`Failed to get ZNS quality: ${response.data.message}`, response.data.error);
    }

    // Trả về thông tin chất lượng gửi ZNS hiện tại của OA
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error instanceof ZnsApiError) {
      throw error;
    }
    
    if (error.response) {
      throw new ZnsApiError(`Failed to get ZNS quality: ${error.response.data?.message || error.message}`, error.response.data?.error || -1);
    }
    
    throw error;
  }
}

// Re-export các hàm từ các file khác để duy trì tính tương thích ngược
export * from './create-template';
export * from './edit-template';
export * from './send-message';
export * from './upload-image';
