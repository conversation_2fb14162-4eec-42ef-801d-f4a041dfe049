import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { getValidZnsToken } from './utils';
import { Database } from "@kit/supabase/database";
import {
  ZnsTemplateType,
  ZnsTemplateTag,
  ZnsTemplateParam,
  ZnsTemplateLayout,
  CreateZnsTemplateResponse
} from './create-template';

/**
 * Interface cho request chỉnh sửa template ZNS
 */
export interface EditZnsTemplateRequest {
  template_id: string;
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
}

/**
 * Chỉnh sửa template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateData Dữ liệu template cần chỉnh sửa
 * @returns Thông tin template đã chỉnh sửa
 */
export async function editZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: EditZnsTemplateRequest
): Promise<CreateZnsTemplateResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API chỉnh sửa template
    const response = await axios.post(
      'https://business.openapi.zalo.me/template/edit',
      templateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to edit ZNS template: ${response.data.message}`);
    }

    // Trả về thông tin template đã chỉnh sửa
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to edit ZNS template: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Chỉnh sửa template ZNS tùy chỉnh
 * Hàm helper để chỉnh sửa template ZNS tùy chỉnh dễ dàng hơn
 */
export async function editCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string
): Promise<CreateZnsTemplateResponse> {
  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `edit_template_${Date.now()}`;

  // Tạo request data
  const templateData: EditZnsTemplateRequest = {
    template_id: templateId,
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId
  };

  // Gọi hàm chỉnh sửa template
  return editZnsTemplate(supabase, oaConfigId, templateData);
}

/**
 * Lấy thông tin template ZNS theo ID
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template cần lấy thông tin
 * @returns Thông tin chi tiết của template
 */
export async function getZnsTemplateById(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<any> {
  // Lấy thông tin OA configuration
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Kiểm tra và refresh token nếu cần
  let accessToken = oaConfig.access_token;
  if (new Date(oaConfig.token_expires_at) < new Date()) {
    accessToken = await refreshZnsToken(supabase, oaConfig);
  }

  try {
    // Gọi API lấy thông tin template
    const response = await axios.get(
      'https://openapi.zalo.me/v2.0/oa/template/info',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template info: ${response.data.message}`);
    }

    // Trả về thông tin template
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template info: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}
