import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { getValidZnsToken } from './utils';
import { Database } from "@kit/supabase/database";
import {
  ZnsTemplateType,
  ZnsTemplateTag,
  ZnsTemplateParam,
  ZnsTemplateLayout,
  CreateZnsTemplateResponse
} from './create-template';
import { validateTemplate, ZnsTemplateError } from './validate-template';

/**
 * Interface cho request chỉnh sửa template ZNS
 */
export interface EditZnsTemplateRequest {
  template_id: string;
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
  team_account_id?: string; // Thêm team_account_id để lưu vào database
}

/**
 * Chỉnh sửa template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateData Dữ liệu template cần chỉnh sửa
 * @returns Thông tin template đã chỉnh sửa
 */
export async function editZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: EditZnsTemplateRequest,
  teamAccountId?: string
): Promise<CreateZnsTemplateResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  // Validate template
  const validationResult = validateTemplate(
    templateData.template_type,
    templateData.layout,
    templateData.params,
  );

  // Return error if validation failed
  if (!validationResult.valid) {
    return {
      success: false,
      error: validationResult.error
    };
  }

  try {
    // Gọi API chỉnh sửa template
    const response = await axios.post(
      'https://business.openapi.zalo.me/template/edit',
      templateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      // Trả về lỗi chi tiết thay vì ném ra lỗi
      return {
        success: false,
        error: {
          error: response.data.error,
          message: response.data.message,
          details: getDetailedErrorMessage(response.data.error),
          code: `ZNS_ERROR_${response.data.error}`
        }
      };
    }

    // Lưu template vào database sau khi gọi API thành công
    const templateResult = response.data.data;

    // Cập nhật bản ghi trong bảng zns_templates
    try {
      // Kiểm tra xem template đã tồn tại trong database chưa
      const { data: existingTemplate, error: findError } = await supabase
        .from('zns_templates')
        .select('id')
        .eq('template_id', templateData.template_id)
        .eq('oa_config_id', oaConfigId)
        .single();

      if (findError && findError.code !== 'PGRST116') { // PGRST116 là lỗi "not found"
        console.error('Error finding template in database:', findError);
      }

      // Nếu template đã tồn tại, cập nhật nó
      if (existingTemplate) {
        const { error: updateError } = await supabase
          .from('zns_templates')
          .update({
            template_name: templateData.template_name,
            status: templateResult.status,
            tag: String(templateData.tag),
            preview_url: templateResult.preview_url,
            team_account_id: teamAccountId || templateData.team_account_id,
            metadata: {
              template_type: templateData.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,
              params: templateData.params,
              layout: templateData.layout,
              note: templateData.note,
              tracking_id: templateData.tracking_id,
              updated_at: new Date().toISOString(),
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', existingTemplate.id);

        if (updateError) {
          console.error('Error updating template in database:', updateError);
        }
      } else {
        // Nếu template chưa tồn tại, tạo mới nó
        const { error: insertError } = await supabase
          .from('zns_templates')
          .insert({
            account_id: oaConfig.account_id,
            oa_config_id: oaConfigId,
            template_id: templateData.template_id,
            template_name: templateData.template_name,
            event_type: 'order_created', // Default event type
            enabled: false, // Default to disabled
            status: templateResult.status,
            tag: String(templateData.tag),
            preview_url: templateResult.preview_url,
            team_account_id: teamAccountId || templateData.team_account_id,
            metadata: {
              template_type: templateData.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,
              params: templateData.params,
              layout: templateData.layout,
              note: templateData.note,
              tracking_id: templateData.tracking_id,
              created_at: new Date().toISOString(),
            },
          });

        if (insertError) {
          console.error('Error inserting template to database:', insertError);
        }
      }
    } catch (dbError) {
      console.error('Error saving template to database:', dbError);
      // Không throw error ở đây vì API Zalo đã thành công
      // Chỉ log lỗi để debug
    }

    // Trả về kết quả thành công
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response && error.response.data) {
      // Trả về lỗi chi tiết từ API
      return {
        success: false,
        error: {
          error: error.response.data.error || -1,
          message: error.response.data.message || 'Unknown error',
          details: getDetailedErrorMessage(error.response.data.error || -1),
          code: `ZNS_ERROR_${error.response.data.error || 'UNKNOWN'}`
        }
      };
    }

    // Trả về lỗi chung
    return {
      success: false,
      error: {
        error: -1,
        message: error.message || 'Unknown error',
        details: 'Lỗi không xác định khi chỉnh sửa template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_EDIT_FAILED'
      }
    };
  }
}

/**
 * Chỉnh sửa template ZNS tùy chỉnh
 * Hàm helper để chỉnh sửa template ZNS tùy chỉnh dễ dàng hơn
 */
export async function editCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
  teamAccountId?: string
): Promise<CreateZnsTemplateResponse> {
  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `edit_template_${Date.now()}`;

  // Tạo request data
  const templateData: EditZnsTemplateRequest = {
    template_id: templateId,
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId
  };

  // Gọi hàm chỉnh sửa template
  return editZnsTemplate(supabase, oaConfigId, templateData, teamAccountId);
}

/**
 * Hàm lấy thông báo lỗi chi tiết dựa trên mã lỗi
 * @param errorCode Mã lỗi từ Zalo API
 * @returns Thông báo lỗi chi tiết
 */
function getDetailedErrorMessage(errorCode: number): string {
  switch (errorCode) {
    case -1131:
      return 'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
    case -1132:
      return 'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
    case -1133:
      return 'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.';
    case -1134:
      return 'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.';
    case -1135:
      return 'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.';
    case -1136:
      return 'Cấu trúc template không hợp lệ. Template phải có ít nhất một tiêu đề và một đoạn văn bản.';
    case -1137:
      return 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
    case -1138:
      return 'Số lượng nút trong template vượt quá giới hạn. Mỗi template chỉ được có tối đa 3 nút.';
    case -1139:
      return 'Tiêu đề nút không hợp lệ. Tiêu đề nút phải có độ dài từ 1-20 ký tự.';
    case -1140:
      return 'Loại nút không hợp lệ. Loại nút phải là số từ 1-5.';
    case -1141:
      return 'Số lượng tham số vượt quá giới hạn. Mỗi template chỉ được có tối đa 20 tham số.';
    case -1142:
      return 'Số lượng hàng trong bảng vượt quá giới hạn. Mỗi bảng chỉ được có tối đa 4 hàng.';
    case -1143:
      return 'Tiêu đề hàng trong bảng không hợp lệ. Tiêu đề hàng phải có độ dài từ 1-20 ký tự.';
    case -1144:
      return 'Giá trị hàng trong bảng không hợp lệ. Giá trị hàng không được để trống.';
    case -1145:
      return 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.';
    case -1146:
      return 'Media ID không hợp lệ. Media ID phải được tạo trước bằng API upload ảnh.';
    case -1147:
      return 'Loại media không hợp lệ. Loại media phải là "IMAGE".';
    case -1148:
      return 'Tag template không hợp lệ. Tag phải là số từ 1-1000.';
    case -1149:
      return 'Loại template không hợp lệ. Loại template phải là số từ 1-3.';
    case -1150:
      return 'Ghi chú template không hợp lệ. Ghi chú không được vượt quá 200 ký tự.';
    case -1151:
      return 'Tracking ID không hợp lệ. Tracking ID không được vượt quá 50 ký tự.';
    case -124:
      return 'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
    default:
      return `Lỗi không xác định từ Zalo API (mã lỗi: ${errorCode}). Vui lòng thử lại sau hoặc liên hệ hỗ trợ.`;
  }

}

/**
 * Lấy thông tin template ZNS theo ID
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template cần lấy thông tin
 * @returns Thông tin chi tiết của template
 */
export async function getZnsTemplateById(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<any> {
  // Lấy thông tin OA configuration
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Kiểm tra và refresh token nếu cần
  let accessToken = oaConfig.access_token;
  if (new Date(oaConfig.token_expires_at) < new Date()) {
    accessToken = await refreshZnsToken(supabase, oaConfig);
  }

  try {
    // Gọi API lấy thông tin template
    const response = await axios.get(
      'https://openapi.zalo.me/v2.0/oa/template/info',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      return {
        success: false,
        error: {
          error: response.data.error,
          message: response.data.message,
          details: getDetailedErrorMessage(response.data.error),
          code: `ZNS_ERROR_${response.data.error}`
        }
      };
    }

    // Trả về thông tin template
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response && error.response.data) {
      // Trả về lỗi chi tiết từ API
      return {
        success: false,
        error: {
          error: error.response.data.error || -1,
          message: error.response.data.message || 'Unknown error',
          details: getDetailedErrorMessage(error.response.data.error || -1),
          code: `ZNS_ERROR_${error.response.data.error || 'UNKNOWN'}`
        }
      };
    }

    // Trả về lỗi chung
    return {
      success: false,
      error: {
        error: -1,
        message: error.message || 'Unknown error',
        details: 'Lỗi không xác định khi lấy thông tin template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_GET_TEMPLATE_FAILED'
      }
    };
  }
}
