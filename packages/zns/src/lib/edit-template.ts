import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { getValidZnsToken } from './utils';
import { Database } from "@kit/supabase/database";
import {
  ZnsTemplateType,
  ZnsTemplateTag,
  ZnsTemplateParam,
  ZnsTemplateLayout,
  CreateZnsTemplateResponse
} from './create-template';

/**
 * Interface cho request chỉnh sửa template ZNS
 */
export interface EditZnsTemplateRequest {
  template_id: string;
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
  team_account_id?: string; // Thêm team_account_id để lưu vào database
}

/**
 * Chỉnh sửa template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateData Dữ liệu template cần chỉnh sửa
 * @returns Thông tin template đã chỉnh sửa
 */
export async function editZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: EditZnsTemplateRequest,
  teamAccountId?: string
): Promise<CreateZnsTemplateResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API chỉnh sửa template
    const response = await axios.post(
      'https://business.openapi.zalo.me/template/edit',
      templateData,
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to edit ZNS template: ${response.data.message}`);
    }

    // Lưu template vào database sau khi gọi API thành công
    const templateResult = response.data.data;

    // Cập nhật bản ghi trong bảng zns_templates
    try {
      // Kiểm tra xem template đã tồn tại trong database chưa
      const { data: existingTemplate, error: findError } = await supabase
        .from('zns_templates')
        .select('id')
        .eq('template_id', templateData.template_id)
        .eq('oa_config_id', oaConfigId)
        .single();

      if (findError && findError.code !== 'PGRST116') { // PGRST116 là lỗi "not found"
        console.error('Error finding template in database:', findError);
      }

      // Nếu template đã tồn tại, cập nhật nó
      if (existingTemplate) {
        const { error: updateError } = await supabase
          .from('zns_templates')
          .update({
            template_name: templateData.template_name,
            status: templateResult.status,
            tag: String(templateData.tag),
            preview_url: templateResult.preview_url,
            team_account_id: teamAccountId || templateData.team_account_id,
            metadata: {
              template_type: templateData.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,
              params: templateData.params,
              layout: templateData.layout,
              note: templateData.note,
              tracking_id: templateData.tracking_id,
              updated_at: new Date().toISOString(),
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', existingTemplate.id);

        if (updateError) {
          console.error('Error updating template in database:', updateError);
        }
      } else {
        // Nếu template chưa tồn tại, tạo mới nó
        const { error: insertError } = await supabase
          .from('zns_templates')
          .insert({
            account_id: oaConfig.account_id,
            oa_config_id: oaConfigId,
            template_id: templateData.template_id,
            template_name: templateData.template_name,
            event_type: 'order_created', // Default event type
            enabled: false, // Default to disabled
            status: templateResult.status,
            tag: String(templateData.tag),
            preview_url: templateResult.preview_url,
            team_account_id: teamAccountId || templateData.team_account_id,
            metadata: {
              template_type: templateData.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,
              params: templateData.params,
              layout: templateData.layout,
              note: templateData.note,
              tracking_id: templateData.tracking_id,
              created_at: new Date().toISOString(),
            },
          });

        if (insertError) {
          console.error('Error inserting template to database:', insertError);
        }
      }
    } catch (dbError) {
      console.error('Error saving template to database:', dbError);
      // Không throw error ở đây vì API Zalo đã thành công
      // Chỉ log lỗi để debug
    }

    // Trả về thông tin template đã chỉnh sửa
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to edit ZNS template: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Chỉnh sửa template ZNS tùy chỉnh
 * Hàm helper để chỉnh sửa template ZNS tùy chỉnh dễ dàng hơn
 */
export async function editCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
  teamAccountId?: string
): Promise<CreateZnsTemplateResponse> {
  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `edit_template_${Date.now()}`;

  // Tạo request data
  const templateData: EditZnsTemplateRequest = {
    template_id: templateId,
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId
  };

  // Gọi hàm chỉnh sửa template
  return editZnsTemplate(supabase, oaConfigId, templateData, teamAccountId);
}

/**
 * Lấy thông tin template ZNS theo ID
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template cần lấy thông tin
 * @returns Thông tin chi tiết của template
 */
export async function getZnsTemplateById(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<any> {
  // Lấy thông tin OA configuration
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Kiểm tra và refresh token nếu cần
  let accessToken = oaConfig.access_token;
  if (new Date(oaConfig.token_expires_at) < new Date()) {
    accessToken = await refreshZnsToken(supabase, oaConfig);
  }

  try {
    // Gọi API lấy thông tin template
    const response = await axios.get(
      'https://openapi.zalo.me/v2.0/oa/template/info',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template info: ${response.data.message}`);
    }

    // Trả về thông tin template
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template info: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}
