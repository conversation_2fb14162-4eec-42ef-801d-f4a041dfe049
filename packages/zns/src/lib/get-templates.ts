import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { Database } from '@kit/supabase/database';
import { getValidZnsToken } from './utils';
import { ZnsApiError } from './get-zns-info';

/**
 * Enum for ZNS template status
 */
export enum ZnsTemplateStatus {
  PENDING_REVIEW = 1,
  APPROVED = 2,
  REJECTED = 3,
  DELETED = 4,
}

/**
 * Interface for ZNS template list response
 */
export interface ZnsTemplateListResponse {
  data: Array<{
    templateId: string;
    templateName: string;
    templateType: number;
    status: string;
    tag: number;
    appId: string;
    oaId: string;
    price: string;
    timeout: number;
    previewUrl: string;
    createdTime: number;
    updatedTime: number;
  }>;
  metadata: {
    total: number;
  };
}

/**
 * Interface for ZNS message status response
 */
export interface ZnsMessageStatusResponse {
  messageId: string;
  status: string;
  statusDetail: string;
  statusCode: number;
  statusTime: number;
  deliveryTime: number;
  readTime: number;
  phone: string;
  templateId: string;
  templateName: string;
  templateType: number;
  tag: number;
  price: string;
  appId: string;
  oaId: string;
}

/**
 * Interface for ZNS quota response
 */
export interface ZnsQuotaResponse {
  quota: number;
  usedQuota: number;
  remainingQuota: number;
  quotaExpiredTime: number;
}

/**
 * Interface for ZNS template detail response
 */
export interface ZnsTemplateDetailResponse {
  templateId: string;
  templateName: string;
  templateType: number;
  status: string;
  tag: number;
  appId: string;
  oaId: string;
  price: string;
  timeout: number;
  previewUrl: string;
  createdTime: number;
  updatedTime: number;
  layout: any; // Using any for flexibility, can be typed more specifically if needed
  params: Array<{
    name: string;
    type: string;
    sample_value: string;
  }>;
}

/**
 * Interface for ZNS template sample data response
 */
export interface ZnsTemplateSampleDataResponse {
  html: string;
  data: any; // Using any for flexibility, can be typed more specifically if needed
}

/**
 * Interface for ZNS rating response
 */
export interface ZnsRatingResponse {
  ratings: Array<{
    messageId: string;
    phone: string;
    templateId: string;
    templateName: string;
    rating: number;
    comment: string;
    ratingTime: number;
  }>;
  total: number;
}

/**
 * Interface for ZNS quality response
 */
export interface ZnsQualityResponse {
  quality: number;
  qualityLevel: string;
  qualityDescription: string;
  qualityTime: number;
}

/**
 * Lấy thông tin trạng thái ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param messageId ID của thông báo ZNS
 * @returns Thông tin trạng thái ZNS
 */
export async function getZnsMessageStatus(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  messageId: string
): Promise<ZnsMessageStatusResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin trạng thái ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/status',
      {
        params: {
          message_id: messageId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS message status: ${response.data.message}`);
    }

    // Trả về thông tin trạng thái ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS message status: ${error.response.data?.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin quota ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin quota ZNS
 */
export async function getZnsQuota(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<ZnsQuotaResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin quota ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/quota',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS quota: ${response.data.message}`);
    }

    // Trả về thông tin quota ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS quota: ${error.response.data?.message || error.message}`);
    }
    throw error;
  }
}

// Các hàm đã được chuyển sang get-zns-info.ts
// File này được giữ lại để đảm bảo tính tương thích ngược
