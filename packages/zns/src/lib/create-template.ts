import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken } from './utils';

// Enum definitions
export enum ZnsTemplateType {
  CUSTOM = 1,
  AUTHENTICATION = 2,
  PAYMENT_REQUEST = 3,
  VOUCHER = 4,
  SERVICE_RATING = 5,
}

export enum ZnsTemplateTag {
  TRANSACTION = 1,
  CUSTOMER_CARE = 2,
  PROMOTION = 3,
}

export enum ZnsParamType {
  CUSTOMER_NAME = 1,
  PHONE_NUMBER = 2,
  ADDRESS = 3,
  CODE = 4,
  CUSTOM_LABEL = 5,
  TRANSACTION_STATUS = 6,
  CONTACT_INFO = 7,
  GENDER = 8,
  PRODUCT_NAME = 9,
  QUANTITY_AMOUNT = 10,
  TIME = 11,
  OTP = 12,
  URL = 13,
  CURRENCY = 14,
  BANK_TRANSFER_NOTE = 15,
}

// Interface for ZNS template parameters
export interface ZnsTemplateParam {
  name: string;
  type: ZnsParamType | string;
  sample_value: string;
}

// Interface for a table row (used in table components)
export interface ZnsTableRow {
  key: string;
  value: string;
  params?: string[]; // Dynamic parameters in value
}

// Interface for a single component in the layout
export interface ZnsTemplateComponent {
  type:
    | 'logo'
    | 'image'
    | 'title'
    | 'paragraph'
    | 'table'
    | 'button'
    | 'otp'
    | 'payment'
    | 'voucher'
    | 'rating';
  value?: string; // Text content for title, paragraph, button, otp, payment, voucher, rating
  url?: string; // For logo, image, button, rating
  params?: string[]; // Dynamic parameters in value (e.g., ["name", "otp"])
  rows?: ZnsTableRow[]; // For table components
}

// Interface for the layout of a ZNS template
export interface ZnsTemplateLayout {
  header?: {
    components: ZnsTemplateComponent[];
  };
  body: {
    components: ZnsTemplateComponent[];
  };
  footer?: {
    components: ZnsTemplateComponent[];
  };
}

// Interface for request to create ZNS template
export interface CreateZnsTemplateRequest {
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
}

// Interface for response when creating ZNS template
export interface CreateZnsTemplateResponse {
  template_id: string;
  template_name: string;
  template_type: number;
  status: string;
  tag: number;
  app_id: string;
  oa_id: string;
  price: string;
  timeout: number;
  preview_url: string;
}

// Validate layout based on template type and constraints
function validateLayout(
  templateType: ZnsTemplateType,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
): void {
  if (!layout.body || !layout.body.components.length) {
    throw new Error(
      'Layout body is required and must contain at least one component',
    );
  }

  switch (templateType) {
    case ZnsTemplateType.CUSTOM: {
      // Header: 1 logo or 1 image
      if (layout.header) {
        if (
          layout.header.components.length !== 1 ||
          !(layout.header.components[0].LOGO || layout.header.components[0].IMAGE)
        ) {
          throw new Error(
            'Custom template header must contain exactly 1 logo or image',
          );
        }
      }
      // Body: 1 title, 0-4 paragraphs, 0-1 table
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter(
        (c) => c.TITLE,
      ).length;
      const paragraphCount = bodyComponents.filter(
        (c) => c.PARAGRAPH,
      ).length;
      const tableCount = bodyComponents.filter(
        (c) => c.TABLE,
      ).length;
      if (titleCount !== 1) {
        throw new Error('Custom template body must contain exactly 1 title');
      }
      if (paragraphCount > 4) {
        throw new Error(
          'Custom template body cannot contain more than 4 paragraphs',
        );
      }
      if (tableCount > 1) {
        throw new Error(
          'Custom template body cannot contain more than 1 table',
        );
      }
      // Footer: 0-2 buttons, at least 1 if header has image
      if (layout.footer) {
        const buttonCount = layout.footer.components.filter(
          (c) => c.BUTTONS,
        ).length;
        if (buttonCount > 2) {
          throw new Error(
            'Custom template footer cannot contain more than 2 buttons',
          );
        }
        if (layout.header?.components[0].IMAGE && buttonCount < 1) {
          throw new Error(
            'Custom template with image in header must contain at least 1 button in footer',
          );
        }
      }
      break;
    }
    case ZnsTemplateType.AUTHENTICATION: {
      // Header: 1 logo
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        !layout.header.components[0].LOGO
      ) {
        throw new Error(
          'Authentication template header must contain exactly 1 logo',
        );
      }
      // Body: 1 otp, 1 paragraph
      const bodyComponents = layout.body.components;
      const otpCount = bodyComponents.filter((c) => c.OTP).length;
      const paragraphCount = bodyComponents.filter(
        (c) => c.PARAGRAPH,
      ).length;
      if (otpCount !== 1) {
        throw new Error(
          'Authentication template body must contain exactly 1 otp component',
        );
      }
      if (paragraphCount !== 1) {
        throw new Error(
          'Authentication template body must contain exactly 1 paragraph',
        );
      }
      if (!params?.some((p) => p.type === ZnsParamType.OTP.toString())) {
        throw new Error(
          'Authentication template must include an OTP parameter',
        );
      }
      break;
    }
    case ZnsTemplateType.PAYMENT_REQUEST: {
      // Header: 1 logo
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        layout.header.components[0].type !== 'logo'
      ) {
        throw new Error(
          'Payment Request template header must contain exactly 1 logo',
        );
      }
      // Body: 1 title, 0-4 paragraphs, 0-1 table, 1 payment
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter(
        (c) => c.type === 'title',
      ).length;
      const paragraphCount = bodyComponents.filter(
        (c) => c.type === 'paragraph',
      ).length;
      const tableCount = bodyComponents.filter(
        (c) => c.type === 'table',
      ).length;
      const paymentCount = bodyComponents.filter(
        (c) => c.type === 'payment',
      ).length;
      if (titleCount !== 1) {
        throw new Error(
          'Payment Request template body must contain exactly 1 title',
        );
      }
      if (paragraphCount > 4) {
        throw new Error(
          'Payment Request template body cannot contain more than 4 paragraphs',
        );
      }
      if (tableCount > 1) {
        throw new Error(
          'Payment Request template body cannot contain more than 1 table',
        );
      }
      if (paymentCount !== 1) {
        throw new Error(
          'Payment Request template body must contain exactly 1 payment component',
        );
      }
      // Footer: 0-2 buttons
      if (
        layout.footer &&
        layout.footer.components.filter((c) => c.type === 'button').length > 2
      ) {
        throw new Error(
          'Payment Request template footer cannot contain more than 2 buttons',
        );
      }
      break;
    }
    case ZnsTemplateType.VOUCHER: {
      // Header: 1 logo or 1 image
      if (layout.header) {
        if (
          layout.header.components.length !== 1 ||
          !['logo', 'image'].includes(layout.header.components[0].type)
        ) {
          throw new Error(
            'Voucher template header must contain exactly 1 logo or image',
          );
        }
      }
      // Body: 1 title, 0-4 paragraphs, 0-1 table, 1 voucher
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter(
        (c) => c.type === 'title',
      ).length;
      const paragraphCount = bodyComponents.filter(
        (c) => c.type === 'paragraph',
      ).length;
      const tableCount = bodyComponents.filter(
        (c) => c.type === 'table',
      ).length;
      const voucherCount = bodyComponents.filter(
        (c) => c.type === 'voucher',
      ).length;
      if (titleCount !== 1) {
        throw new Error('Voucher template body must contain exactly 1 title');
      }
      if (paragraphCount > 4) {
        throw new Error(
          'Voucher template body cannot contain more than 4 paragraphs',
        );
      }
      if (tableCount > 1) {
        throw new Error(
          'Voucher template body cannot contain more than 1 table',
        );
      }
      if (voucherCount !== 1) {
        throw new Error(
          'Voucher template body must contain exactly 1 voucher component',
        );
      }
      // Footer: 0-2 buttons, at least 1 if header has image
      if (layout.footer) {
        const buttonCount = layout.footer.components.filter(
          (c) => c.type === 'button',
        ).length;
        if (buttonCount > 2) {
          throw new Error(
            'Voucher template footer cannot contain more than 2 buttons',
          );
        }
        if (layout.header?.components[0].type === 'image' && buttonCount < 1) {
          throw new Error(
            'Voucher template with image in header must contain at least 1 button in footer',
          );
        }
      }
      break;
    }
    case ZnsTemplateType.SERVICE_RATING: {
      // Header: 1 logo
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        layout.header.components[0].type !== 'logo'
      ) {
        throw new Error(
          'Service Rating template header must contain exactly 1 logo',
        );
      }
      // Body: 1 title, 0-1 paragraph, 1 rating
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter(
        (c) => c.type === 'title',
      ).length;
      const paragraphCount = bodyComponents.filter(
        (c) => c.type === 'paragraph',
      ).length;
      const ratingCount = bodyComponents.filter(
        (c) => c.type === 'rating',
      ).length;
      if (titleCount !== 1) {
        throw new Error(
          'Service Rating template body must contain exactly 1 title',
        );
      }
      if (paragraphCount > 1) {
        throw new Error(
          'Service Rating template body cannot contain more than 1 paragraph',
        );
      }
      if (ratingCount !== 1) {
        throw new Error(
          'Service Rating template body must contain exactly 1 rating component',
        );
      }
      // Footer: 0-2 buttons
      if (
        layout.footer &&
        layout.footer.components.filter((c) => c.type === 'button').length > 2
      ) {
        throw new Error(
          'Service Rating template footer cannot contain more than 2 buttons',
        );
      }
      break;
    }
  }
}

// Create a ZNS template
export async function createZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: CreateZnsTemplateRequest,
): Promise<CreateZnsTemplateResponse> {
  // Validate layout
  validateLayout(
    templateData.template_type,
    templateData.layout,
    templateData.params,
  );

  // Get valid token and OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  try {
    // Log the template data for debugging
    console.log('Template data being sent to API:', JSON.stringify(templateData, null, 2));

    // Call API to create template
    const response = await axios.post(
      'https://business.openapi.zalo.me/template/create',
      templateData,
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );

    // Log the response for debugging
    console.log('API response:', JSON.stringify(response.data, null, 2));
    // Check result
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to create ZNS template: ${response.data.message}`,
      );
    }

    // Return template info
    return response.data.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(
        `Failed to create ZNS template: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

// Helper to create a custom ZNS template
export async function createCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
): Promise<CreateZnsTemplateResponse> {
  const finalTrackingId = trackingId || `custom_template_${Date.now()}`;

  const templateData: CreateZnsTemplateRequest = {
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId,
  };

  return createZnsTemplate(supabase, oaConfigId, templateData);
}

// Helper functions to generate layouts for each template type
export function createCustomTemplateLayout(
  title: string,
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: { type: 'logo' | 'image'; url: string },
  buttons: { value: string; url: string }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [{ type: 'title', value: title }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 paragraphs');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        type: 'paragraph',
        value: p.value,
        params: p.params,
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ type: 'table', rows: table.rows });
  }

  // Add header (logo or image)
  if (header) {
    layout.header = {
      components: [{ type: header.type, url: header.url }],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: buttons.map((b) => ({
        type: 'button',
        value: b.value,
        url: b.url,
      })),
    };
  }

  return layout;
}

export function createAuthenticationTemplateLayout(
  otpValue: string,
  otpParams: string[],
  paragraphValue: string = 'Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút.',
  logoUrl: string,
): ZnsTemplateLayout {
  return {
    header: {
      components: [{ type: 'logo', url: logoUrl }],
    },
    body: {
      components: [
        { type: 'otp', value: otpValue, params: otpParams },
        { type: 'paragraph', value: paragraphValue },
      ],
    },
  };
}

export function createPaymentRequestTemplateLayout(
  title: string,
  paymentValue: string,
  paymentParams: string[],
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  logoUrl: string,
  buttons: { value: string; url: string }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [{ type: 'logo', url: logoUrl }],
    },
    body: {
      components: [
        { type: 'title', value: title },
        { type: 'payment', value: paymentValue, params: paymentParams },
      ],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 paragraphs');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        type: 'paragraph',
        value: p.value,
        params: p.params,
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ type: 'table', rows: table.rows });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: buttons.map((b) => ({
        type: 'button',
        value: b.value,
        url: b.url,
      })),
    };
  }

  return layout;
}

export function createVoucherTemplateLayout(
  title: string,
  voucherValue: string,
  voucherParams: string[],
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: { type: 'logo' | 'image'; url: string },
  buttons: { value: string; url: string }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [
        { type: 'title', value: title },
        { type: 'voucher', value: voucherValue, params: voucherParams },
      ],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 paragraphs');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        type: 'paragraph',
        value: p.value,
        params: p.params,
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ type: 'table', rows: table.rows });
  }

  // Add header (logo or image)
  if (header) {
    layout.header = {
      components: [{ type: header.type, url: header.url }],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: buttons.map((b) => ({
        type: 'button',
        value: b.value,
        url: b.url,
      })),
    };
  }

  return layout;
}

export function createServiceRatingTemplateLayout(
  title: string,
  ratingValue: string,
  ratingUrl: string,
  ratingParams: string[] = [],
  paragraph?: { value: string; params?: string[] },
  logoUrl: string,
  buttons: { value: string; url: string }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [{ type: 'logo', url: logoUrl }],
    },
    body: {
      components: [
        { type: 'title', value: title },
        {
          type: 'rating',
          value: ratingValue,
          url: ratingUrl,
          params: ratingParams,
        },
      ],
    },
  };

  // Add paragraph (if provided)
  if (paragraph) {
    layout.body.components.push({
      type: 'paragraph',
      value: paragraph.value,
      params: paragraph.params,
    });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: buttons.map((b) => ({
        type: 'button',
        value: b.value,
        url: b.url,
      })),
    };
  }

  return layout;
}
