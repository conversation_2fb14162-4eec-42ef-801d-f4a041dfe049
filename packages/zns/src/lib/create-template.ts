import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken } from './utils';
import { validateTemplate, ZnsTemplateError } from './validate-template';

// Enum definitions
export enum ZnsTemplateType {
  CUSTOM = 1,
  AUTHENTICATION = 2,
  PAYMENT_REQUEST = 3,
  VOUCHER = 4,
  SERVICE_RATING = 5,
}

export enum ZnsTemplateTag {
  TRANSACTION = 1,
  CUSTOMER_CARE = 2,
  PROMOTION = 3,
}

export enum ZnsParamType {
  CUSTOMER_NAME = '1',
  PHONE_NUMBER = '2',
  ADDRESS = '3',
  CODE = '4',
  CUSTOM_LABEL = '5',
  TRANSACTION_STATUS = '6',
  CONTACT_INFO = '7',
  GENDER = '8',
  PRODUCT_NAME = '9',
  QUANTITY_AMOUNT = '10',
  TIME = '11',
  OTP = '12',
  URL = '13',
  CURRENCY = '14',
  BANK_TRANSFER_NOTE = '15',
  // ORDER_CODE = '11', // Matches sample
  PRICE = '18',
  // STATUS = '14',
  DATE = '19',
  VOUCHER_CODE = '30',
  VOUCHER_TIME = '20',
  VOUCHER_URL = '200',
}

// Interface for ZNS template parameters
export interface ZnsTemplateParam {
  name: string;
  type: ZnsParamType | string;
  sample_value: string;
}

// Interface for a table row
export interface ZnsTableRow {
  title: string;
  value: string;
  row_type?: number; // Optional, as per documentation
}

// Interface for attachment (used in LOGO and IMAGES)
export interface ZnsAttachment {
  type: 'IMAGE';
  media_id: string;
}

// Interface for header components
export interface ZnsHeaderComponent {
  LOGO?: {
    light: ZnsAttachment;
    dark: ZnsAttachment;
  };
  IMAGES?: {
    items: ZnsAttachment[];
  };
}

// Interface for body components
export interface ZnsBodyComponent {
  TITLE?: { value: string };
  PARAGRAPH?: { value: string };
  OTP?: { value: string };
  TABLE?: { rows: ZnsTableRow[] };
  PAYMENT?: {
    bank_code: string;
    account_name: string;
    bank_account: string;
    amount: string;
    note?: string;
  };
  VOUCHER?: {
    name: string;
    condition: string;
    voucher_code: string;
    start_date?: string;
    end_date: string;
    display_code?: number;
  };
  RATING?: {
    items: {
      star: number;
      title: string;
      question?: string;
      answers?: string[];
      thanks: string;
      description: string;
    }[];
  };
}

// Interface for footer components
export interface ZnsFooterComponent {
  BUTTONS?: {
    items: { type: number; title: string; content: string }[];
  };
}

// Interface for the layout of a ZNS template
export interface ZnsTemplateLayout {
  header?: { components: ZnsHeaderComponent[] };
  body: { components: ZnsBodyComponent[] };
  footer?: { components: ZnsFooterComponent[] };
}

// Interface for request to create ZNS template
export interface CreateZnsTemplateRequest {
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
}

// Interface for response when creating ZNS template
export interface CreateZnsTemplateResponse {
  success: boolean;
  data?: {
    template_id: string;
    template_name: string;
    template_type: number;
    status: string;
    tag: number;
    app_id: string;
    oa_id: string;
    price: string;
    timeout: number;
    preview_url: string;
  };
  error?: {
    error: number;
    message: string;
    details?: string;
    code?: string;
  };
}

// Validate layout based on template type and constraints
function validateLayout(
  templateType: ZnsTemplateType,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
): { valid: boolean; error?: ZnsTemplateError } {
  // Không sử dụng try-catch ở đây vì chúng ta đang trả về kết quả validation
  if (!layout.body || !layout.body.components.length) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Layout body is required and must contain at least one component',
        details: 'Cấu trúc template không hợp lệ. Body phải có ít nhất một component.',
        code: 'ZNS_ERROR_INVALID_BODY'
      }
    };
  }

  // Validate text lengths and parameter counts
  const validateTextLength = (
    value: string,
    min: number,
    max: number,
    field: string,
  ): { valid: boolean; error?: ZnsTemplateError } => {
    if (value.length < min || value.length > max) {
      return {
        valid: false,
        error: {
          error: -1132,
          message: `${field} must be between ${min} and ${max} characters`,
          details: `Độ dài của ${field} không hợp lệ. Phải có độ dài từ ${min} đến ${max} ký tự.`,
          code: 'ZNS_ERROR_INVALID_TEXT_LENGTH'
        }
      };
    }
    return { valid: true };
  };

  const validateParamCount = (value: string, max: number, field: string): { valid: boolean; error?: ZnsTemplateError } => {
    const paramCount = (value.match(/<(\w+)>/g) || []).length;
    if (paramCount > max) {
      return {
        valid: false,
        error: {
          error: -1141,
          message: `${field} cannot contain more than ${max} parameters`,
          details: `${field} không được chứa quá ${max} tham số.`,
          code: 'ZNS_ERROR_TOO_MANY_PARAMS'
        }
      };
    }
    return { valid: true };
  };

  // Validate body components
  layout.body.components.forEach((comp) => {
    if (comp.TITLE) {
      validateTextLength(comp.TITLE.value, 1, 36, 'TITLE value'); // Assuming max 36 as per earlier error code
      validateParamCount(comp.TITLE.value, 4, 'TITLE');
    }
    if (comp.PARAGRAPH) {
      validateTextLength(comp.PARAGRAPH.value, 9, 400, 'PARAGRAPH value');
      validateParamCount(comp.PARAGRAPH.value, 10, 'PARAGRAPH');
    }
    if (comp.OTP) {
      validateTextLength(comp.OTP.value, 1, 10, 'OTP value');
      validateParamCount(comp.OTP.value, 1, 'OTP');
      if (
        !params?.some(
          (p) =>
            p.type === ZnsParamType.OTP &&
            comp.OTP?.value.includes(`<${p.name}>`),
        )
      ) {
        throw new Error('OTP component must reference an OTP parameter');
      }
    }
    if (comp.TABLE) {
      comp.TABLE.rows.forEach((row) => {
        validateTextLength(row.title, 1, 20, 'TABLE row title');
        validateTextLength(row.value, 1, 100, 'TABLE row value');
        if (row.row_type && ![1, 2, 3].includes(row.row_type)) {
          return {
            valid: false,
            error: {
              error: -1145,
              message: 'TABLE row_type must be 1, 2, or 3',
              details: 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.',
              code: 'ZNS_ERROR_INVALID_TABLE_ROW_TYPE'
            }
          };
        }
      });
    }
    if (comp.PAYMENT) {
      validateTextLength(
        comp.PAYMENT.account_name,
        1,
        100,
        'PAYMENT account_name',
      );
      validateTextLength(
        comp.PAYMENT.bank_account,
        1,
        100,
        'PAYMENT bank_account',
      );
      if (comp.PAYMENT.note) {
        validateTextLength(comp.PAYMENT.note, 1, 90, 'PAYMENT note');
      }
      const amount = parseInt(
        comp.PAYMENT.amount.replace(/<(\w+)>/g, '2000'),
        10,
      );
      if (
        !comp.PAYMENT.amount.includes('<') &&
        (amount < 2000 || amount > *********)
      ) {
        throw new Error(
          'PAYMENT amount must be between 2,000 and 500,000,000 VND',
        );
      }
    }
    if (comp.VOUCHER) {
      validateTextLength(comp.VOUCHER.name, 1, 30, 'VOUCHER name');
      validateTextLength(comp.VOUCHER.condition, 1, 40, 'VOUCHER condition');
      validateTextLength(
        comp.VOUCHER.voucher_code,
        1,
        25,
        'VOUCHER voucher_code',
      );
      if (
        comp.VOUCHER.display_code &&
        ![1, 2, 3].includes(comp.VOUCHER.display_code)
      ) {
        throw new Error('VOUCHER display_code must be 1, 2, or 3');
      }
    }
    if (comp.RATING) {
      if (comp.RATING.items.length !== 5) {
        throw new Error('RATING component must contain exactly 5 items');
      }
      comp.RATING.items.forEach((item, index) => {
        if (item.star !== index + 1) {
          throw new Error(`RATING item star must be ${index + 1}`);
        }
        validateTextLength(item.title, 1, 50, `RATING item ${item.star} title`);
        if (item.question) {
          validateTextLength(
            item.question,
            1,
            100,
            `RATING item ${item.star} question`,
          );
        }
        if (item.answers) {
          if (item.answers.length < 1 || item.answers.length > 5) {
            throw new Error(
              `RATING item ${item.star} answers must have 1-5 items`,
            );
          }
          item.answers.forEach((ans, i) => {
            validateTextLength(
              ans,
              1,
              50,
              `RATING item ${item.star} answer ${i + 1}`,
            );
          });
        }
        validateTextLength(
          item.thanks,
          1,
          100,
          `RATING item ${item.star} thanks`,
        );
        validateTextLength(
          item.description,
          1,
          200,
          `RATING item ${item.star} description`,
        );
      });
    }
  });

  // Validate footer buttons
  if (layout.footer) {
    layout.footer.components.forEach((comp) => {
      if (comp.BUTTONS) {
        comp.BUTTONS.items.forEach((btn) => {
          validateTextLength(btn.title, 5, 30, 'BUTTON title');
          if (![1, 2, 3, 4, 5, 6, 7, 8, 9].includes(btn.type)) {
            throw new Error('BUTTON type must be 1-9');
          }
          if (btn.type === 2) {
            // Validate phone number format for type 2 (call)
            if (!btn.content.match(/^\+?\d{10,15}$/)) {
              throw new Error(
                'BUTTON content for type 2 must be a valid phone number',
              );
            }
          } else if ([1, 3, 4, 5, 6, 7, 8, 9].includes(btn.type)) {
            // Validate URL format for other types
            if (!btn.content.match(/^https?:\/\//)) {
              throw new Error(
                'BUTTON content must be a valid URL starting with http:// or https://',
              );
            }
          }
        });
      }
    });
  }

  try {
    switch (templateType) {
      case ZnsTemplateType.CUSTOM: {
        // Header: 1 LOGO or 1 IMAGES
        if (layout.header) {
          if (layout.header.components.length !== 1) {
            return {
              valid: false,
              error: {
                error: -1136,
                message: 'Custom template header must contain exactly 1 component (LOGO or IMAGES)',
                details: 'Header của template tùy chỉnh phải chứa đúng 1 thành phần (LOGO hoặc IMAGES).',
                code: 'ZNS_ERROR_INVALID_HEADER'
              }
            };
          }
          const headerComp = layout.header.components[0];
          if (headerComp.IMAGES && headerComp.IMAGES.items.length !== 1) {
            return {
              valid: false,
              error: {
                error: -1137,
                message: 'Custom template IMAGES component must contain exactly 1 image',
                details: 'Thành phần IMAGES trong template tùy chỉnh phải chứa đúng 1 hình ảnh.',
                code: 'ZNS_ERROR_INVALID_IMAGES'
              }
            };
          }
        }
        // Body: 1 TITLE, 0-4 PARAGRAPH, 0-1 TABLE
        const bodyComponents = layout.body.components;
        const titleCount = bodyComponents.filter((c) => c.TITLE).length;
        const paragraphCount = bodyComponents.filter((c) => c.PARAGRAPH).length;
        const tableCount = bodyComponents.filter((c) => c.TABLE).length;
        if (titleCount !== 1) {
          return {
            valid: false,
            error: {
              error: -1136,
              message: 'Custom template body must contain exactly 1 TITLE',
              details: 'Body của template tùy chỉnh phải chứa đúng 1 tiêu đề (TITLE).',
              code: 'ZNS_ERROR_MISSING_TITLE'
            }
          };
        }
        if (paragraphCount > 4) {
          return {
            valid: false,
            error: {
              error: -1136,
              message: 'Custom template body cannot contain more than 4 PARAGRAPH components',
              details: 'Body của template tùy chỉnh không được chứa quá 4 đoạn văn (PARAGRAPH).',
              code: 'ZNS_ERROR_TOO_MANY_PARAGRAPHS'
            }
          };
        }
        if (tableCount > 1) {
          return {
            valid: false,
            error: {
              error: -1136,
              message: 'Custom template body cannot contain more than 1 TABLE',
              details: 'Body của template tùy chỉnh không được chứa quá 1 bảng (TABLE).',
              code: 'ZNS_ERROR_TOO_MANY_TABLES'
            }
          };
        }
        // Footer: 0-2 BUTTONS, at least 1 if header has IMAGES
        if (layout.footer) {
          const buttonCount = layout.footer.components.reduce(
            (count, c) => count + (c.BUTTONS?.items.length || 0),
            0,
          );
          if (buttonCount > 2) {
            return {
              valid: false,
              error: {
                error: -1138,
                message: 'Custom template footer cannot contain more than 2 buttons',
                details: 'Footer của template tùy chỉnh không được chứa quá 2 nút.',
                code: 'ZNS_ERROR_TOO_MANY_BUTTONS'
              }
            };
          }
          if (layout.header?.components[0].IMAGES && buttonCount < 1) {
            return {
              valid: false,
              error: {
                error: -1138,
                message: 'Custom template with IMAGES in header must contain at least 1 button in footer',
                details: 'Template tùy chỉnh có IMAGES trong header phải chứa ít nhất 1 nút trong footer.',
                code: 'ZNS_ERROR_MISSING_BUTTON'
              }
            };
          }
        }
        break;
      }
    case ZnsTemplateType.AUTHENTICATION: {
      // Header: 1 LOGO
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        !layout.header.components[0].LOGO
      ) {
        throw new Error(
          'Authentication template header must contain exactly 1 LOGO',
        );
      }
      // Body: 1 OTP, 1 PARAGRAPH
      const bodyComponents = layout.body.components;
      const otpCount = bodyComponents.filter((c) => c.OTP).length;
      const paragraphCount = bodyComponents.filter((c) => c.PARAGRAPH).length;
      if (otpCount !== 1) {
        throw new Error(
          'Authentication template body must contain exactly 1 OTP component',
        );
      }
      if (paragraphCount !== 1) {
        throw new Error(
          'Authentication template body must contain exactly 1 PARAGRAPH',
        );
      }
      break;
    }
    case ZnsTemplateType.PAYMENT_REQUEST: {
      // Header: 1 LOGO
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        !layout.header.components[0].LOGO
      ) {
        throw new Error(
          'Payment Request template header must contain exactly 1 LOGO',
        );
      }
      // Body: 1 TITLE, 0-4 PARAGRAPH, 0-1 TABLE, 1 PAYMENT
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter((c) => c.TITLE).length;
      const paragraphCount = bodyComponents.filter((c) => c.PARAGRAPH).length;
      const tableCount = bodyComponents.filter((c) => c.TABLE).length;
      const paymentCount = bodyComponents.filter((c) => c.PAYMENT).length;
      if (titleCount !== 1) {
        throw new Error(
          'Payment Request template body must contain exactly 1 TITLE',
        );
      }
      if (paragraphCount > 4) {
        throw new Error(
          'Payment Request template body cannot contain more than 4 PARAGRAPH components',
        );
      }
      if (tableCount > 1) {
        throw new Error(
          'Payment Request template body cannot contain more than 1 TABLE',
        );
      }
      if (paymentCount !== 1) {
        throw new Error(
          'Payment Request template body must contain exactly 1 PAYMENT component',
        );
      }
      // Footer: 0-2 BUTTONS
      if (layout.footer) {
        const buttonCount = layout.footer.components.reduce(
          (count, c) => count + (c.BUTTONS?.items.length || 0),
          0,
        );
        if (buttonCount > 2) {
          throw new Error(
            'Payment Request template footer cannot contain more than 2 buttons',
          );
        }
      }
      break;
    }
    case ZnsTemplateType.VOUCHER: {
      // Header: 1 IMAGES or 1 LOGO
      if (layout.header) {
        if (layout.header.components.length !== 1) {
          throw new Error(
            'Voucher template header must contain exactly 1 component (IMAGES or LOGO)',
          );
        }
        const headerComp = layout.header.components[0];
        if (headerComp.IMAGES && headerComp.IMAGES.items.length !== 1) {
          throw new Error(
            'Voucher template IMAGES component must contain exactly 1 image',
          );
        }
      }
      // Body: 1 TITLE, 0-4 PARAGRAPH, 0-1 TABLE, 1 VOUCHER
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter((c) => c.TITLE).length;
      const paragraphCount = bodyComponents.filter((c) => c.PARAGRAPH).length;
      const tableCount = bodyComponents.filter((c) => c.TABLE).length;
      const voucherCount = bodyComponents.filter((c) => c.VOUCHER).length;
      if (titleCount !== 1) {
        throw new Error('Voucher template body must contain exactly 1 TITLE');
      }
      if (paragraphCount > 4) {
        throw new Error(
          'Voucher template body cannot contain more than 4 PARAGRAPH components',
        );
      }
      if (tableCount > 1) {
        throw new Error(
          'Voucher template body cannot contain more than 1 TABLE',
        );
      }
      if (voucherCount !== 1) {
        throw new Error(
          'Voucher template body must contain exactly 1 VOUCHER component',
        );
      }
      // Footer: 0-2 BUTTONS, at least 1 if header has IMAGES
      if (layout.footer) {
        const buttonCount = layout.footer.components.reduce(
          (count, c) => count + (c.BUTTONS?.items.length || 0),
          0,
        );
        if (buttonCount > 2) {
          throw new Error(
            'Voucher template footer cannot contain more than 2 buttons',
          );
        }
        if (layout.header?.components[0].IMAGES && buttonCount < 1) {
          throw new Error(
            'Voucher template with IMAGES in header must contain at least 1 button in footer',
          );
        }
      }
      break;
    }
    case ZnsTemplateType.SERVICE_RATING: {
      // Header: 1 LOGO
      if (
        !layout.header ||
        layout.header.components.length !== 1 ||
        !layout.header.components[0].LOGO
      ) {
        throw new Error(
          'Service Rating template header must contain exactly 1 LOGO',
        );
      }
      // Body: 1 TITLE, 0-1 PARAGRAPH, 1 RATING
      const bodyComponents = layout.body.components;
      const titleCount = bodyComponents.filter((c) => c.TITLE).length;
      const paragraphCount = bodyComponents.filter((c) => c.PARAGRAPH).length;
      const ratingCount = bodyComponents.filter((c) => c.RATING).length;
      if (titleCount !== 1) {
        throw new Error(
          'Service Rating template body must contain exactly 1 TITLE',
        );
      }
      if (paragraphCount > 1) {
        throw new Error(
          'Service Rating template body cannot contain more than 1 PARAGRAPH',
        );
      }
      if (ratingCount !== 1) {
        throw new Error(
          'Service Rating template body must contain exactly 1 RATING component',
        );
      }
      // Footer: 0-2 BUTTONS
      if (layout.footer) {
        const buttonCount = layout.footer.components.reduce(
          (count, c) => count + (c.BUTTONS?.items.length || 0),
          0,
        );
        if (buttonCount > 2) {
          throw new Error(
            'Service Rating template footer cannot contain more than 2 buttons',
          );
        }
      }
      break;
    }
  }
}

// Create a ZNS template
export async function createZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: CreateZnsTemplateRequest,
  teamAccountId?: string,
): Promise<CreateZnsTemplateResponse> {
  // Validate template
  const validationResult = validateTemplate(
    templateData.template_type,
    templateData.layout,
    templateData.params,
  );

  // Return error if validation failed
  if (!validationResult.valid) {
    return {
      success: false,
      error: validationResult.error
    };
  }

  // Get valid token and OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  try {
    // Log the template data for debugging
    console.log(
      'Template data being sent to API:',
      JSON.stringify(templateData, null, 2),
    );

    // Call API to create template
    const response = await axios.post(
      'https://business.openapi.zalo.me/template/create',
      templateData,
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );

    // Log the response for debugging
    console.log('API response:', JSON.stringify(response.data, null, 2));

    // Check result
    if (response.data.error !== 0) {
      return {
        success: false,
        error: {
          error: response.data.error,
          message: response.data.message,
          details: getDetailedErrorMessage(response.data.error),
          code: `ZNS_ERROR_${response.data.error}`,
        },
      };
    }

    // Save template to database
    const templateResult = response.data.data;
    const accountId = oaConfig.account_id;

    const { data: templateRecord, error: templateError } = await supabase
      .from('zns_templates')
      .insert({
        account_id: accountId,
        oa_config_id: oaConfigId,
        template_id: templateResult.template_id,
        template_name: templateData.template_name,
        event_type: 'order_created',
        enabled: false,
        status: templateResult.status,
        tag: String(templateData.tag),
        preview_url: templateResult.preview_url,
        team_account_id: teamAccountId,
        metadata: {
          template_type: templateData.template_type,
          price: templateResult.price,
          timeout: templateResult.timeout,
          params: templateData.params,
          layout: templateData.layout,
          note: templateData.note,
          tracking_id: templateData.tracking_id,
          created_at: new Date().toISOString(),
        },
      })
      .select()
      .single();

    if (templateError) {
      console.error('Error saving template to database:', templateError);
    }

    return {
      success: true,
      data: templateResult,
    };
  } catch (error: any) {
    if (error.response) {
      throw new Error(
        `Failed to create ZNS template: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

// Helper to create a custom ZNS template
export async function createCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
  teamAccountId?: string,
): Promise<CreateZnsTemplateResponse> {
  const finalTrackingId = trackingId || `custom_template_${Date.now()}`;

  const templateData: CreateZnsTemplateRequest = {
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId,
  };

  return createZnsTemplate(supabase, oaConfigId, templateData, teamAccountId);
}

// Helper functions to generate layouts for each template type
export function createCustomTemplateLayout(
  title: string,
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: {
    type: 'LOGO' | 'IMAGES';
    light_media_id?: string;
    dark_media_id?: string;
    media_id?: string;
  },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [{ TITLE: { value: title } }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add header (LOGO or IMAGES)
  if (header) {
    layout.header = {
      components: [
        header.type === 'IMAGES'
          ? {
              IMAGES: {
                items: [{ type: 'IMAGE', media_id: header.media_id! }],
              },
            }
          : {
              LOGO: {
                light: { type: 'IMAGE', media_id: header.light_media_id! },
                dark: { type: 'IMAGE', media_id: header.dark_media_id! },
              },
            },
      ],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createAuthenticationTemplateLayout(
  otpValue: string,
  paragraphValue: string = 'Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút.',
  logo: { light_media_id: string; dark_media_id: string },
): ZnsTemplateLayout {
  return {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [
        { OTP: { value: otpValue.replace(/{{(\w+)}}/g, '<$1>') } },
        { PARAGRAPH: { value: paragraphValue } },
      ],
    },
  };
}

export function createPaymentRequestTemplateLayout(
  title: string,
  payment: {
    bank_code: string;
    account_name: string;
    bank_account: string;
    amount: string;
    note?: string;
  },
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  logo: { light_media_id: string; dark_media_id: string },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [{ TITLE: { value: title } }, { PAYMENT: payment }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createVoucherTemplateLayout(
  title: string,
  voucher: {
    name: string;
    condition: string;
    voucher_code: string;
    start_date?: string;
    end_date: string;
    display_code?: number;
  },
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: {
    type: 'LOGO' | 'IMAGES';
    light_media_id?: string;
    dark_media_id?: string;
    media_id?: string;
  },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [{ TITLE: { value: title } }, { VOUCHER: voucher }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add header (LOGO or IMAGES)
  if (header) {
    layout.header = {
      components: [
        header.type === 'IMAGES'
          ? {
              IMAGES: {
                items: [{ type: 'IMAGE', media_id: header.media_id! }],
              },
            }
          : {
              LOGO: {
                light: { type: 'IMAGE', media_id: header.light_media_id! },
                dark: { type: 'IMAGE', media_id: header.dark_media_id! },
              },
            },
      ],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createServiceRatingTemplateLayout(
  title: string,
  rating: {
    items: {
      star: number;
      title: string;
      question?: string;
      answers?: string[];
      thanks: string;
      description: string;
    }[];
  },
  paragraph?: { value: string; params?: string[] },
  logo: { light_media_id: string; dark_media_id: string },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [{ TITLE: { value: title } }, { RATING: rating }],
    },
  };

  // Add paragraph (if provided)
  if (paragraph) {
    layout.body.components.push({
      PARAGRAPH: { value: paragraph.value.replace(/{{(\w+)}}/g, '<$1>') },
    });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

// Error message function (unchanged from your code)
function getDetailedErrorMessage(errorCode: number): string {
  switch (errorCode) {
    case -1131:
      return 'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
    case -1132:
      return 'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
    case -1133:
      return 'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.';
    case -1134:
      return 'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.';
    case -1135:
      return 'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.';
    case -1136:
      return 'Cấu trúc template không hợp lệ. Template phải có ít nhất một tiêu đề và một đoạn văn bản.';
    case -1137:
      return 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
    case -1138:
      return 'Số lượng nút trong template vượt quá giới hạn. Mỗi template chỉ được có tối đa 3 nút.';
    case -1139:
      return 'Tiêu đề nút không hợp lệ. Tiêu đề nút phải có độ dài từ 1-20 ký tự.';
    case -1140:
      return 'Loại nút không hợp lệ. Loại nút phải là số từ 1-5.';
    case -1141:
      return 'Số lượng tham số vượt quá giới hạn. Mỗi template chỉ được có tối đa 20 tham số.';
    case -1142:
      return 'Số lượng hàng trong bảng vượt quá giới hạn. Mỗi bảng chỉ được có tối đa 4 hàng.';
    case -1143:
      return 'Tiêu đề hàng trong bảng không hợp lệ. Tiêu đề hàng phải có độ dài từ 1-20 ký tự.';
    case -1144:
      return 'Giá trị hàng trong bảng không hợp lệ. Giá trị hàng không được để trống.';
    case -1145:
      return 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.';
    case -1146:
      return 'Media ID không hợp lệ. Media ID phải được tạo trước bằng API upload ảnh.';
    case -1147:
      return 'Loại media không hợp lệ. Loại media phải là "IMAGE".';
    case -1148:
      return 'Tag template không hợp lệ. Tag phải là số từ 1-1000.';
    case -1149:
      return 'Loại template không hợp lệ. Loại template phải là số từ 1-3.';
    case -1150:
      return 'Ghi chú template không hợp lệ. Ghi chú không được vượt quá 200 ký tự.';
    case -1151:
      return 'Tracking ID không hợp lệ. Tracking ID không được vượt quá 50 ký tự.';
    case -124:
      return 'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
    default:
      return `Lỗi không xác định từ Zalo API (mã lỗi: ${errorCode}). Vui lòng thử lại sau hoặc liên hệ hỗ trợ.`;
  }
}
