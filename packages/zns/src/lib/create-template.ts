import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken } from './utils';
import { validateZnsTemplate } from './validate-template-unified';

// Enum definitions

// Enum definitions
export enum ZnsTemplateType {
  CUSTOM = 1,
  AUTHENTICATION = 2,
  PAYMENT_REQUEST = 3,
  VOUCHER = 4,
  SERVICE_RATING = 5,
}

export enum ZnsTemplateTag {
  TRANSACTION = 1,
  CUSTOMER_CARE = 2,
  PROMOTION = 3,
}

export enum ZnsParamType {
  CUSTOMER_NAME = '1',
  PHONE_NUMBER = '2',
  ADDRESS = '3',
  CODE = '4',
  CUSTOM_LABEL = '5',
  TRANSACTION_STATUS = '6',
  CONTACT_INFO = '7',
  GENDER = '8',
  PRODUCT_NAME = '9',
  QUANTITY_AMOUNT = '10',
  TIME = '11',
  OTP = '12',
  URL = '13',
  CURRENCY = '14',
  BANK_TRANSFER_NOTE = '15',
  // ORDER_CODE = '11', // Matches sample
  // PRICE = '18',
  // STATUS = '14',
  // DATE = '19',
  // VOUCHER_CODE = '30',
  // VOUCHER_TIME = '20',
  // VOUCHER_URL = '200',
}

// Interface for ZNS template parameters
export interface ZnsTemplateParam {
  name: string;
  type: ZnsParamType | string;
  sample_value: string;
}

// Enum for table row types
export enum ZnsTableRowType {
  NONE = 0, // Không có hiệu ứng
  SUCCESS = 1, // Thành công
  UPDATE = 2, // Cập nhật
  NOTICE = 3, // Lưu ý
  ERROR = 4, // Báo lỗi
  BASIC = 5, // Cơ bản
}

// Interface for a table row
export interface ZnsTableRow {
  title: string;
  value: string;
  row_type?: ZnsTableRowType; // Use enum instead of number
}

// Interface for attachment (used in LOGO and IMAGES)
export interface ZnsAttachment {
  type: 'IMAGE';
  media_id: string;
}

// Interface for header components
export interface ZnsHeaderComponent {
  LOGO?: {
    light: ZnsAttachment;
    dark: ZnsAttachment;
  };
  IMAGES?: {
    items: ZnsAttachment[];
  };
}

// Enum for voucher display code
export enum ZnsVoucherDisplayCode {
  QR_CODE = 1, // QR code (default)
  BAR_CODE = 2, // Bar code
  TEXT_ONLY = 3, // Text only
}

// Interface for body components
export interface ZnsBodyComponent {
  TITLE?: { value: string };
  PARAGRAPH?: { value: string };
  OTP?: { value: string };
  TABLE?: { rows: ZnsTableRow[] };
  PAYMENT?: {
    bank_code: string;
    account_name: string;
    bank_account: string;
    amount: string;
    note?: string;
  };
  VOUCHER?: {
    name: string;
    condition: string;
    voucher_code: string;
    start_date?: string;
    end_date: string;
    display_code?: ZnsVoucherDisplayCode;
  };
  RATING?: {
    items: {
      star: number;
      title: string;
      question?: string;
      answers?: string[];
      thanks: string;
      description: string;
    }[];
  };
}

// Enum for button types
export enum ZnsButtonType {
  TO_BUSINESS_PAGE = 1, // Đến trang của doanh nghiệp
  CALL_PHONE = 2, // Gọi điện
  TO_OA_PAGE = 3, // Đến trang thông tin OA
  TO_MINI_APP = 4, // Đến ứng dụng Zalo Mini App của doanh nghiệp
  TO_APP_DOWNLOAD = 5, // Đến trang tải ứng dụng
  TO_PRODUCT_PAGE = 6, // Đến trang phân phối sản phẩm
  TO_OTHER_WEB_MINI_APP = 7, // Đến trang web/Zalo Mini App khác
  TO_OTHER_APP = 8, // Đến ứng dụng khác
  TO_ARTICLE = 9, // Đến bài viết của doanh nghiệp
}

// Interface for footer components
export interface ZnsFooterComponent {
  BUTTONS?: {
    items: {
      type: ZnsButtonType; // Use enum instead of number
      title: string;
      content: string;
    }[];
  };
}

// Interface for the layout of a ZNS template
export interface ZnsTemplateLayout {
  header?: { components: ZnsHeaderComponent[] };
  body: { components: ZnsBodyComponent[] };
  footer?: { components: ZnsFooterComponent[] };
}

// Interface for request to create ZNS template
export interface CreateZnsTemplateRequest {
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
}

// Interface for response when creating ZNS template
export interface CreateZnsTemplateResponse {
  success: boolean;
  data?: {
    template_id: string;
    template_name: string;
    template_type: number;
    status: string;
    tag: number;
    app_id: string;
    oa_id: string;
    price: string;
    timeout: number;
    preview_url: string;
  };
  error?: {
    error: number;
    message: string;
    details?: string;
    code?: string;
  };
}

// Create a ZNS template
export async function createZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: CreateZnsTemplateRequest,
  accountId: string,
): Promise<CreateZnsTemplateResponse> {
  // Validate template
  const validationResult = validateZnsTemplate(
    templateData.template_type,
    templateData.layout,
    templateData.params,
  );

  // Return error if validation failed
  if (!validationResult.valid) {
    return {
      success: false,
      error: validationResult.error,
    };
  }

  // Get valid token and OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Retry configuration
  const maxRetries = 3;
  const baseDelay = 1000; // 1 second
  let lastError = null;
  let response = null;

  // Log the template data for debugging
  console.log(
    'Template data being sent to API:',
    JSON.stringify(templateData, null, 2),
  );

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`=== ZNS API Call Attempt ${attempt}/${maxRetries} ===`);

      // Call API to create template
      response = await axios.post(
        'https://business.openapi.zalo.me/template/create',
        templateData,
        {
          headers: {
            'Content-Type': 'application/json',
            access_token: accessToken,
          },
          timeout: 30000, // 30 seconds timeout
        },
      );

      // Log the response for debugging
      console.log(
        `API response (attempt ${attempt}):`,
        JSON.stringify(response.data, null, 2),
      );

      // Check result
      if (response.data.error !== 0) {
        const errorCode = response.data.error;
        const errorMessage = response.data.message;

        // Check if this is a retryable error
        if (isRetryableError(errorCode) && attempt < maxRetries) {
          console.log(
            `Retryable error ${errorCode}: ${errorMessage}. Retrying in ${baseDelay * attempt}ms...`,
          );
          lastError = {
            error: errorCode,
            message: errorMessage,
            details: getDetailedErrorMessage(errorCode),
            code: `ZNS_ERROR_${errorCode}`,
          };

          // Wait before retry with exponential backoff
          await new Promise((resolve) =>
            setTimeout(resolve, baseDelay * attempt),
          );
          continue;
        }

        // Non-retryable error or max retries reached
        return {
          success: false,
          error: {
            error: errorCode,
            message: errorMessage,
            details: getDetailedErrorMessage(errorCode),
            code: `ZNS_ERROR_${errorCode}`,
          },
        };
      }

      // Success - break out of retry loop
      console.log(`API call successful on attempt ${attempt}`);
      break;
    } catch (networkError: any) {
      console.log(`Network error on attempt ${attempt}:`, networkError.message);
      lastError = {
        error: -1,
        message: networkError.message || 'Network error',
        details:
          'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.',
        code: 'ZNS_ERROR_NETWORK',
      };

      if (attempt < maxRetries) {
        console.log(`Retrying network error in ${baseDelay * attempt}ms...`);
        await new Promise((resolve) =>
          setTimeout(resolve, baseDelay * attempt),
        );
        continue;
      }

      // Max retries reached for network error
      return {
        success: false,
        error: lastError,
      };
    }
  }

  // If we get here and response is null, return the last error
  if (!response) {
    return {
      success: false,
      error: lastError || {
        error: -1,
        message: 'Unknown error',
        details: 'Lỗi không xác định khi gọi API Zalo.',
        code: 'ZNS_ERROR_UNKNOWN',
      },
    };
  }

  // Save template to database
  const templateResult = response.data.data;

  const insertData = {
    account_id: accountId,
    oa_config_id: oaConfigId,
    template_id: String(templateResult.template_id),
    template_name: templateResult.template_name || templateData.template_name,
    // Không set event_type mặc định, để null cho đến khi user mapping
    event_type: null,
    enabled: false, // Mặc định disabled cho đến khi user enable
    status: templateResult.template_status,
    tag: String(templateResult.tag),
    preview_url: templateResult.preview_url,
    metadata: {
      // Dữ liệu từ Zalo API response
      oa_id: templateResult.oa_id,
      app_id: templateResult.app_id,
      template_type: templateResult.template_type,
      price: templateResult.price,
      timeout: templateResult.timeout,

      // Dữ liệu từ request gốc (serialize để tương thích với Json type)
      original_request: {
        template_name: templateData.template_name,
        tag: templateData.tag,
        layout: JSON.parse(JSON.stringify(templateData.layout)), // Serialize layout
        params: templateData.params
          ? JSON.parse(JSON.stringify(templateData.params))
          : null,
        note: templateData.note,
        tracking_id: templateData.tracking_id,
      },

      // Metadata
      created_at: new Date().toISOString(),
      api_response: JSON.parse(JSON.stringify(templateResult)), // Serialize response
    },
  };

  console.log('Insert data:', JSON.stringify(insertData, null, 2));

  const { data: templateRecord, error: templateError } = await supabase
    .from('zns_templates')
    .insert(insertData)
    .select()
    .single();

  if (templateError) {
    console.error('Error saving template to database:', templateError);
    console.log('=== END DEBUG ===');
  } else {
    console.log('Template saved successfully:', templateRecord);
    console.log('=== END DEBUG ===');
  }

  return {
    success: true,
    data: templateResult,
  };
}

// Helper to create a custom ZNS template
export async function createCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  accountId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
): Promise<CreateZnsTemplateResponse> {
  const finalTrackingId = trackingId || `custom_template_${Date.now()}`;

  const templateData: CreateZnsTemplateRequest = {
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId,
  };

  return createZnsTemplate(supabase, oaConfigId, templateData, accountId);
}

// Helper functions to generate layouts for each template type
export function createCustomTemplateLayout(
  title: string,
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: {
    type: 'LOGO' | 'IMAGES';
    light_media_id?: string;
    dark_media_id?: string;
    media_id?: string;
  },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [{ TITLE: { value: title } }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add header (LOGO or IMAGES)
  if (header) {
    layout.header = {
      components: [
        header.type === 'IMAGES'
          ? {
              IMAGES: {
                items: [{ type: 'IMAGE', media_id: header.media_id! }],
              },
            }
          : {
              LOGO: {
                light: { type: 'IMAGE', media_id: header.light_media_id! },
                dark: { type: 'IMAGE', media_id: header.dark_media_id! },
              },
            },
      ],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createAuthenticationTemplateLayout(
  otpValue: string,
  paragraphValue: string = 'Tuyệt đối KHÔNG chia sẻ mã xác thực cho bất kỳ ai dưới bất kỳ hình thức nào. Mã xác thực có hiệu lực trong 5 phút.',
  logo: { light_media_id: string; dark_media_id: string },
): ZnsTemplateLayout {
  return {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [
        { OTP: { value: otpValue.replace(/{{(\w+)}}/g, '<$1>') } },
        { PARAGRAPH: { value: paragraphValue } },
      ],
    },
  };
}

export function createPaymentRequestTemplateLayout(
  title: string,
  payment: {
    bank_code: string;
    account_name: string;
    bank_account: string;
    amount: string;
    note?: string;
  },
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  logo: { light_media_id: string; dark_media_id: string },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [{ TITLE: { value: title } }, { PAYMENT: payment }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createVoucherTemplateLayout(
  title: string,
  voucher: {
    name: string;
    condition: string;
    voucher_code: string;
    start_date?: string;
    end_date: string;
    display_code?: number;
  },
  paragraphs: { value: string; params?: string[] }[] = [],
  table?: { rows: ZnsTableRow[] },
  header?: {
    type: 'LOGO' | 'IMAGES';
    light_media_id?: string;
    dark_media_id?: string;
    media_id?: string;
  },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    body: {
      components: [{ TITLE: { value: title } }, { VOUCHER: voucher }],
    },
  };

  // Add paragraphs (up to 4)
  if (paragraphs.length > 0) {
    if (paragraphs.length > 4)
      throw new Error('Cannot have more than 4 PARAGRAPH components');
    layout.body.components.push(
      ...paragraphs.map((p) => ({
        PARAGRAPH: { value: p.value.replace(/{{(\w+)}}/g, '<$1>') },
      })),
    );
  }

  // Add table (if provided)
  if (table) {
    layout.body.components.push({ TABLE: { rows: table.rows } });
  }

  // Add header (LOGO or IMAGES)
  if (header) {
    layout.header = {
      components: [
        header.type === 'IMAGES'
          ? {
              IMAGES: {
                items: [{ type: 'IMAGE', media_id: header.media_id! }],
              },
            }
          : {
              LOGO: {
                light: { type: 'IMAGE', media_id: header.light_media_id! },
                dark: { type: 'IMAGE', media_id: header.dark_media_id! },
              },
            },
      ],
    };
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

export function createServiceRatingTemplateLayout(
  title: string,
  rating: {
    items: {
      star: number;
      title: string;
      question?: string;
      answers?: string[];
      thanks: string;
      description: string;
    }[];
  },
  paragraph?: { value: string; params?: string[] },
  logo: { light_media_id: string; dark_media_id: string },
  buttons: { title: string; content: string; type: number }[] = [],
): ZnsTemplateLayout {
  const layout: ZnsTemplateLayout = {
    header: {
      components: [
        {
          LOGO: {
            light: { type: 'IMAGE', media_id: logo.light_media_id },
            dark: { type: 'IMAGE', media_id: logo.dark_media_id },
          },
        },
      ],
    },
    body: {
      components: [{ TITLE: { value: title } }, { RATING: rating }],
    },
  };

  // Add paragraph (if provided)
  if (paragraph) {
    layout.body.components.push({
      PARAGRAPH: { value: paragraph.value.replace(/{{(\w+)}}/g, '<$1>') },
    });
  }

  // Add footer buttons (up to 2)
  if (buttons.length > 0) {
    if (buttons.length > 2) throw new Error('Cannot have more than 2 buttons');
    layout.footer = {
      components: [{ BUTTONS: { items: buttons } }],
    };
  }

  return layout;
}

/**
 * Kiểm tra xem lỗi có thể retry được không
 * @param errorCode Mã lỗi từ Zalo API
 * @returns true nếu có thể retry, false nếu không
 */
function isRetryableError(errorCode: number): boolean {
  const retryableErrors = [
    -100, // Data is invalid | Cannot create template. Plz try again
    -153, // Data is invalid | Cannot create template. Plz try again
    -500, // Internal server error
    -503, // Service unavailable
    -504, // Gateway timeout
    -429, // Too many requests
  ];

  return retryableErrors.includes(errorCode);
}

// Error message function
function getDetailedErrorMessage(errorCode: number): string {
  switch (errorCode) {
    case -153:
      return 'Dữ liệu không hợp lệ hoặc server Zalo đang bận. Hệ thống sẽ tự động thử lại.';
    case -1131:
      return 'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
    case -1132:
      return 'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
    case -1133:
      return 'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.';
    case -1134:
      return 'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.';
    case -1135:
      return 'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.';
    case -1136:
      return 'Cấu trúc template không hợp lệ. Template phải có ít nhất một tiêu đề và một đoạn văn bản.';
    case -1137:
      return 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
    case -1138:
      return 'Số lượng nút trong template vượt quá giới hạn. Mỗi template chỉ được có tối đa 3 nút.';
    case -1139:
      return 'Tiêu đề nút không hợp lệ. Tiêu đề nút phải có độ dài từ 1-20 ký tự.';
    case -1140:
      return 'Loại nút không hợp lệ. Loại nút phải là số từ 1-5.';
    case -1141:
      return 'Số lượng tham số vượt quá giới hạn. Mỗi template chỉ được có tối đa 20 tham số.';
    case -1142:
      return 'Số lượng hàng trong bảng vượt quá giới hạn. Mỗi bảng chỉ được có tối đa 4 hàng.';
    case -1143:
      return 'Tiêu đề hàng trong bảng không hợp lệ. Tiêu đề hàng phải có độ dài từ 1-20 ký tự.';
    case -1144:
      return 'Giá trị hàng trong bảng không hợp lệ. Giá trị hàng không được để trống.';
    case -1145:
      return 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.';
    case -1146:
      return 'Media ID không hợp lệ. Media ID phải được tạo trước bằng API upload ảnh.';
    case -1147:
      return 'Loại media không hợp lệ. Loại media phải là "IMAGE".';
    case -1148:
      return 'Tag template không hợp lệ. Tag phải là số từ 1-1000.';
    case -1149:
      return 'Loại template không hợp lệ. Loại template phải là số từ 1-3.';
    case -1150:
      return 'Ghi chú template không hợp lệ. Ghi chú không được vượt quá 200 ký tự.';
    case -1151:
      return 'Tracking ID không hợp lệ. Tracking ID không được vượt quá 50 ký tự.';
    case -124:
      return 'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
    case -500:
      return 'Lỗi server nội bộ của Zalo. Hệ thống sẽ tự động thử lại.';
    case -503:
      return 'Dịch vụ Zalo tạm thời không khả dụng. Hệ thống sẽ tự động thử lại.';
    case -504:
      return 'Timeout khi kết nối với server Zalo. Hệ thống sẽ tự động thử lại.';
    case -429:
      return 'Quá nhiều yêu cầu gửi đến Zalo API. Hệ thống sẽ tự động thử lại sau ít phút.';
    default:
      return `Lỗi không xác định từ Zalo API (mã lỗi: ${errorCode}). Vui lòng thử lại sau hoặc liên hệ hỗ trợ.`;
  }
}
