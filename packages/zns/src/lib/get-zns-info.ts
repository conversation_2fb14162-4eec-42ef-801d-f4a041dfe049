import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { getValidZnsToken } from './utils';
import { Database } from "@kit/supabase/database";

/**
 * Interface cho response khi lấy thông tin trạng thái ZNS
 */
export interface ZnsMessageStatusResponse {
  delivery_time: string;
  status: number;
  message: string;
}

/**
 * Interface cho response khi lấy thông tin quota ZNS
 */
export interface ZnsQuotaResponse {
  dailyQuota: string;
  remainingQuota: string;
  dailyQuotaPromotion: string | null;
  remainingQuotaPromotion: string | null;
  monthlyPromotionQuota: number;
  remainingMonthlyPromotionQuota: number;
  estimatedNextMonthPromotionQuota: number;
}

/**
 * Interface cho response khi lấy thông tin loại nội dung ZNS được phép gửi
 */
export interface ZnsTemplateTagResponse {
  data: string[];
}

/**
 * Interface cho response khi lấy danh sách template ZNS
 */
export interface ZnsTemplateListResponse {
  data: ZnsTemplateListItem[];
  metadata: {
    total: number;
  };
}

/**
 * Interface cho item trong danh sách template ZNS
 */
export interface ZnsTemplateListItem {
  templateId: string;
  templateName: string;
  createdTime: number;
  status: string;
  templateQuality: string;
}

/**
 * Interface cho response khi lấy thông tin chi tiết template ZNS
 */
export interface ZnsTemplateDetailResponse {
  templateId: string;
  templateName: string;
  status: string;
  reason?: string;
  listParams: ZnsTemplateParamDetail[];
  listButtons: ZnsTemplateButton[];
  timeout: number;
  previewUrl: string;
  templateQuality: string;
  templateTag: string;
  price: string;
}

/**
 * Interface cho param trong template ZNS detail
 */
export interface ZnsTemplateParamDetail {
  name: string;
  require: boolean;
  type: string;
  maxLength: number;
  minLength: number;
  acceptNull: boolean;
}

/**
 * Interface cho button trong template ZNS
 */
export interface ZnsTemplateButton {
  type: number;
  title: string;
  content: string;
}

/**
 * Interface cho response khi lấy dữ liệu mẫu của template ZNS
 */
export interface ZnsTemplateSampleDataResponse {
  [key: string]: any;
}

/**
 * Interface cho response khi lấy thông tin đánh giá của khách hàng
 */
export interface ZnsRatingResponse {
  total: number;
  data: ZnsRatingItem[];
}

/**
 * Interface cho item trong danh sách đánh giá
 */
export interface ZnsRatingItem {
  note: string;
  rate: number;
  submitDate: string;
  msgId: string;
  feedbacks: string[];
  trackingId: string;
}

/**
 * Interface cho response khi lấy thông tin chất lượng gửi ZNS
 */
export interface ZnsQualityResponse {
  oaCurrentQuality: string;
  oa7dayQuality: string;
}

/**
 * Enum cho trạng thái template ZNS
 */
export enum ZnsTemplateStatus {
  ENABLE = 1,
  PENDING_REVIEW = 2,
  REJECT = 3,
  DISABLE = 4
}

/**
 * Lấy thông tin trạng thái ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param messageId ID của thông báo ZNS
 * @returns Thông tin trạng thái ZNS
 */
export async function getZnsMessageStatus(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  messageId: string
): Promise<ZnsMessageStatusResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin trạng thái ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/status',
      {
        params: {
          message_id: messageId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS message status: ${response.data.message}`);
    }

    // Trả về thông tin trạng thái ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS message status: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin quota ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin quota ZNS
 */
export async function getZnsQuota(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<ZnsQuotaResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin quota ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/quota',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS quota: ${response.data.message}`);
    }

    // Trả về thông tin quota ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS quota: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin loại nội dung ZNS được phép gửi
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin loại nội dung ZNS được phép gửi
 */
export async function getZnsTemplateTag(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<string[]> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin loại nội dung ZNS được phép gửi
    const response = await axios.get(
      'https://business.openapi.zalo.me/message/template-tag',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template tag: ${response.data.message}`);
    }

    // Trả về thông tin loại nội dung ZNS được phép gửi
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template tag: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy danh sách template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param offset Thứ tự của template đầu tiên trong danh sách trả về
 * @param limit Số lượng template muốn lấy
 * @param status Trạng thái của template muốn lấy
 * @returns Danh sách template ZNS
 */
export async function getZnsTemplateList(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  offset: number = 0,
  limit: number = 100,
  status?: ZnsTemplateStatus
): Promise<ZnsTemplateListResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Tạo params
    const params: Record<string, any> = {
      offset,
      limit
    };

    // Thêm status nếu được cung cấp
    if (status !== undefined) {
      params.status = status;
    }

    // Gọi API lấy danh sách template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/all',
      {
        params,
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template list: ${response.data.message}`);
    }

    // Trả về danh sách template ZNS
    return {
      data: response.data.data,
      metadata: response.data.metadata
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template list: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin chi tiết template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @returns Thông tin chi tiết template ZNS
 */
export async function getZnsTemplateDetail(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<ZnsTemplateDetailResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin chi tiết template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/info/v2',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template detail: ${response.data.message}`);
    }

    // Trả về thông tin chi tiết template ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template detail: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy dữ liệu mẫu của template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @returns Dữ liệu mẫu của template ZNS
 */
export async function getZnsTemplateSampleData(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string
): Promise<ZnsTemplateSampleDataResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy dữ liệu mẫu của template ZNS
    const response = await axios.get(
      'https://business.openapi.zalo.me/template/sample-data',
      {
        params: {
          template_id: templateId
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS template sample data: ${response.data.message}`);
    }

    // Trả về dữ liệu mẫu của template ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS template sample data: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin đánh giá của khách hàng
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template ZNS
 * @param fromTime Thời điểm bắt đầu của khoảng thời gian cần lấy dữ liệu
 * @param toTime Thời điểm kết thúc của khoảng thời gian cần lấy dữ liệu
 * @param offset Vị trí thứ tự của đánh giá đầu tiên được trả về
 * @param limit Số lượng đánh giá tối đa được trả về
 * @returns Thông tin đánh giá của khách hàng
 */
export async function getZnsRating(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
  fromTime: number,
  toTime: number,
  offset: number = 0,
  limit: number = 10
): Promise<ZnsRatingResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin đánh giá của khách hàng
    const response = await axios.get(
      'https://business.openapi.zalo.me/rating/get',
      {
        params: {
          template_id: templateId,
          from_time: fromTime,
          to_time: toTime,
          offset,
          limit
        },
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS rating: ${response.data.message}`);
    }

    // Trả về thông tin đánh giá của khách hàng
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS rating: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}

/**
 * Lấy thông tin chất lượng gửi ZNS hiện tại của OA
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @returns Thông tin chất lượng gửi ZNS hiện tại của OA
 */
export async function getZnsQuality(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string
): Promise<ZnsQualityResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);

  try {
    // Gọi API lấy thông tin chất lượng gửi ZNS hiện tại của OA
    const response = await axios.get(
      'https://business.openapi.zalo.me/quality',
      {
        headers: {
          'Content-Type': 'application/json',
          'access_token': accessToken
        }
      }
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to get ZNS quality: ${response.data.message}`);
    }

    // Trả về thông tin chất lượng gửi ZNS hiện tại của OA
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(`Failed to get ZNS quality: ${error.response.data.message || error.message}`);
    }
    throw error;
  }
}
