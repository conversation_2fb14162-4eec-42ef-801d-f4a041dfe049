import { ZnsParamType, ZnsTemplateLayout, ZnsTemplateParam, ZnsTemplateType } from './types';

export interface ZnsTemplateError {
  error: number;
  message: string;
  details?: string;
  code?: string;
}

export interface ValidationResult {
  valid: boolean;
  error?: ZnsTemplateError;
}

/**
 * Validate layout structure
 * @param templateType Template type
 * @param layout Layout structure
 * @param params Parameters
 * @returns Object with validation result
 */
export function validateTemplate(
  templateType: ZnsTemplateType | number,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
): ValidationResult {
  try {
    // Validate header
    if (!layout.header || !Array.isArray(layout.header.components)) {
      return {
        valid: false,
        error: {
          error: -1136,
          message: 'Header must have components array',
          details: 'Cấu trúc template không hợp lệ. Header phải có mảng components.',
          code: 'ZNS_ERROR_INVALID_HEADER'
        }
      };
    }

    // Check if header has at least one logo or image
    const hasLogoOrImage = layout.header.components.some(
      (component) => component.type === 'LOGO' || component.type === 'IMAGE',
    );

    if (!hasLogoOrImage) {
      return {
        valid: false,
        error: {
          error: -1137,
          message: 'Header must have at least one LOGO or IMAGE component',
          details: 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.',
          code: 'ZNS_ERROR_MISSING_MEDIA'
        }
      };
    }

    // Validate body
    if (!layout.body || !Array.isArray(layout.body.components)) {
      return {
        valid: false,
        error: {
          error: -1136,
          message: 'Body must have components array',
          details: 'Cấu trúc template không hợp lệ. Body phải có mảng components.',
          code: 'ZNS_ERROR_INVALID_BODY'
        }
      };
    }

    // Validate footer
    if (layout.footer) {
      if (!Array.isArray(layout.footer.components)) {
        return {
          valid: false,
          error: {
            error: -1136,
            message: 'Footer must have components array',
            details: 'Cấu trúc template không hợp lệ. Footer phải có mảng components.',
            code: 'ZNS_ERROR_INVALID_FOOTER'
          }
        };
      }

      // Validate buttons
      const buttons = layout.footer.components.filter(
        (component) => component.type === 'BUTTON',
      );

      if (buttons.length > 3) {
        return {
          valid: false,
          error: {
            error: -1138,
            message: 'Footer can have at most 3 buttons',
            details: 'Số lượng nút trong template vượt quá giới hạn. Mỗi template chỉ được có tối đa 3 nút.',
            code: 'ZNS_ERROR_TOO_MANY_BUTTONS'
          }
        };
      }

      // Validate button content
      for (const button of buttons) {
        if (!button.content || !button.content.title) {
          return {
            valid: false,
            error: {
              error: -1139,
              message: 'Button must have title',
              details: 'Tiêu đề nút không hợp lệ. Tiêu đề nút phải có độ dài từ 1-20 ký tự.',
              code: 'ZNS_ERROR_INVALID_BUTTON_TITLE'
            }
          };
        }

        if (button.content.type === 1 && !button.content.payload) {
          return {
            valid: false,
            error: {
              error: -1140,
              message: 'Button with type 1 must have payload',
              details: 'Nút loại 1 (postback) phải có payload.',
              code: 'ZNS_ERROR_INVALID_BUTTON_PAYLOAD'
            }
          };
        }

        if (button.content.type === 2 && !button.content.url) {
          return {
            valid: false,
            error: {
              error: -1131,
              message: 'Button with type 2 must have URL',
              details: 'Nút loại 2 (URL) phải có URL hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.',
              code: 'ZNS_ERROR_INVALID_BUTTON_URL'
            }
          };
        }

        if (button.content.type === 3 && !button.content.phone) {
          return {
            valid: false,
            error: {
              error: -1140,
              message: 'Button with type 3 must have phone',
              details: 'Nút loại 3 (phone) phải có số điện thoại.',
              code: 'ZNS_ERROR_INVALID_BUTTON_PHONE'
            }
          };
        }

        if (![1, 2, 3].includes(button.content.type)) {
          return {
            valid: false,
            error: {
              error: -1140,
              message: 'Button type must be 1, 2, or 3',
              details: 'Loại nút không hợp lệ. Loại nút phải là số từ 1-3.',
              code: 'ZNS_ERROR_INVALID_BUTTON_TYPE'
            }
          };
        }
      }
    }

    // Validate table if exists
    if (layout.body) {
      const tables = layout.body.components.filter(
        (component) => component.type === 'TABLE',
      );

      for (const table of tables) {
        if (!table.content || !Array.isArray(table.content.rows)) {
          return {
            valid: false,
            error: {
              error: -1136,
              message: 'TABLE must have rows array',
              details: 'Cấu trúc bảng không hợp lệ. Bảng phải có mảng rows.',
              code: 'ZNS_ERROR_INVALID_TABLE'
            }
          };
        }

        if (table.content.rows.length > 4) {
          return {
            valid: false,
            error: {
              error: -1142,
              message: 'TABLE can have at most 4 rows',
              details: 'Số lượng hàng trong bảng vượt quá giới hạn. Mỗi bảng chỉ được có tối đa 4 hàng.',
              code: 'ZNS_ERROR_TOO_MANY_TABLE_ROWS'
            }
          };
        }

        for (const row of table.content.rows) {
          if (!row.title) {
            return {
              valid: false,
              error: {
                error: -1143,
                message: 'TABLE row must have title',
                details: 'Tiêu đề hàng trong bảng không hợp lệ. Tiêu đề hàng phải có độ dài từ 1-20 ký tự.',
                code: 'ZNS_ERROR_INVALID_TABLE_ROW_TITLE'
              }
            };
          }

          if (![1, 2, 3].includes(row.type)) {
            return {
              valid: false,
              error: {
                error: -1145,
                message: 'TABLE row_type must be 1, 2, or 3',
                details: 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.',
                code: 'ZNS_ERROR_INVALID_TABLE_ROW_TYPE'
              }
            };
          }
        }
      }
    }

    // Validate parameters
    if (params) {
      // Check if all parameters have valid types
      for (const param of params) {
        const paramType = Number(param.type);
        if (isNaN(paramType) || paramType < 1 || paramType > 15) {
          return {
            valid: false,
            error: {
              error: -1133,
              message: `Parameter ${param.name} has invalid type: ${param.type}`,
              details: 'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.',
              code: 'ZNS_ERROR_INVALID_PARAM_TYPE'
            }
          };
        }

        // Check if parameter name is valid
        if (!/^[a-zA-Z0-9_]+$/.test(param.name)) {
          return {
            valid: false,
            error: {
              error: -1134,
              message: `Parameter name ${param.name} is invalid. Only letters, numbers, and underscore are allowed.`,
              details: 'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.',
              code: 'ZNS_ERROR_INVALID_PARAM_NAME'
            }
          };
        }

        // Check if sample value is provided
        if (!param.sample_value) {
          return {
            valid: false,
            error: {
              error: -1135,
              message: `Parameter ${param.name} must have sample_value`,
              details: 'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.',
              code: 'ZNS_ERROR_INVALID_PARAM_SAMPLE'
            }
          };
        }
      }
    }

    // All validations passed
    return { valid: true };
  } catch (error) {
    // Catch any unexpected errors
    return {
      valid: false,
      error: {
        error: -1,
        message: error instanceof Error ? error.message : 'Unknown validation error',
        details: 'Lỗi không xác định khi kiểm tra template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_VALIDATION_FAILED'
      }
    };
  }
}
