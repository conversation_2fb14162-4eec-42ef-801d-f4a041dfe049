export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      account_themes: {
        Row: {
          account_id: string
          config: <PERSON><PERSON>
          created_at: string | null
          id: string
          is_active: boolean | null
          mini_app_id: string | null
          name: string
          oa_config_id: string | null
          template_id: string | null
          updated_at: string | null
          version: string | null
        }
        Insert: {
          account_id: string
          config?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          mini_app_id?: string | null
          name: string
          oa_config_id?: string | null
          template_id?: string | null
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          account_id?: string
          config?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          mini_app_id?: string | null
          name?: string
          oa_config_id?: string | null
          template_id?: string | null
          updated_at?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      account_usage_stats: {
        Row: {
          account_id: string
          counters: Json | null
          last_updated: string | null
        }
        Insert: {
          account_id: string
          counters?: Json | null
          last_updated?: string | null
        }
        Update: {
          account_id?: string
          counters?: Json | null
          last_updated?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      accounts: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          phone: string | null
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name: string
          phone?: string | null
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name?: string
          phone?: string | null
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      accounts_memberships: {
        Row: {
          account_id: string
          account_role: string
          created_at: string
          created_by: string | null
          updated_at: string
          updated_by: string | null
          user_id: string
        }
        Insert: {
          account_id: string
          account_role: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id: string
        }
        Update: {
          account_id?: string
          account_role?: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["account_role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      analytics_events: {
        Row: {
          account_id: string
          created_at: string
          device_type: string | null
          event_data: Json
          event_type: string
          id: string
          source: string | null
          theme_id: string | null
          user_id: string | null
          visitor_id: string | null
        }
        Insert: {
          account_id: string
          created_at?: string
          device_type?: string | null
          event_data?: Json
          event_type: string
          id?: string
          source?: string | null
          theme_id?: string | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string
          device_type?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          source?: string | null
          theme_id?: string | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Relationships: []
      }
      billing_customers: {
        Row: {
          account_id: string
          customer_id: string
          email: string | null
          id: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Insert: {
          account_id: string
          customer_id: string
          email?: string | null
          id?: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Update: {
          account_id?: string
          customer_id?: string
          email?: string | null
          id?: number
          provider?: Database["public"]["Enums"]["billing_provider"]
        }
        Relationships: [
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      branch_products: {
        Row: {
          branch_id: string
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean
          product_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          branch_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean
          product_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          branch_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean
          product_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "branch_products_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branch_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      branches: {
        Row: {
          account_id: string
          address: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          location: string | null
          name: string
          phone: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          address?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          name: string
          phone?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          address?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          name?: string
          phone?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          account_id: string
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      config: {
        Row: {
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing: boolean
          enable_team_account_billing: boolean
          enable_team_accounts: boolean
        }
        Insert: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Update: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Relationships: []
      }
      customer_activities: {
        Row: {
          account_id: string
          action: string
          created_at: string | null
          customer_id: string
          details: Json | null
          id: string
        }
        Insert: {
          account_id: string
          action: string
          created_at?: string | null
          customer_id: string
          details?: Json | null
          id?: string
        }
        Update: {
          account_id?: string
          action?: string
          created_at?: string | null
          customer_id?: string
          details?: Json | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_order_items: {
        Row: {
          attribute_id: string | null
          created_at: string | null
          created_by: string | null
          discount_percentage: number | null
          flash_sale_id: string | null
          id: string
          order_id: string
          original_price: number | null
          price: number
          product_id: string
          quantity: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          attribute_id?: string | null
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number | null
          flash_sale_id?: string | null
          id?: string
          order_id: string
          original_price?: number | null
          price: number
          product_id: string
          quantity: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          attribute_id?: string | null
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number | null
          flash_sale_id?: string | null
          id?: string
          order_id?: string
          original_price?: number | null
          price?: number
          product_id?: string
          quantity?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_order_items_attribute_id_fkey"
            columns: ["attribute_id"]
            isOneToOne: false
            referencedRelation: "product_attributes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "customer_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_orders: {
        Row: {
          account_id: string
          branch_id: string | null
          created_at: string | null
          created_by: string | null
          customer_id: string | null
          discount_amount: number
          id: string
          metadata: Json | null
          order_code: string | null
          payment_method: string | null
          status: string
          subtotal: number
          total_amount: number
          updated_at: string | null
          updated_by: string | null
          voucher_id: string | null
          webhook_data: Json | null
          webhook_processed: boolean | null
          webhook_url: string | null
        }
        Insert: {
          account_id: string
          branch_id?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          discount_amount?: number
          id?: string
          metadata?: Json | null
          order_code?: string | null
          payment_method?: string | null
          status?: string
          subtotal?: number
          total_amount: number
          updated_at?: string | null
          updated_by?: string | null
          voucher_id?: string | null
          webhook_data?: Json | null
          webhook_processed?: boolean | null
          webhook_url?: string | null
        }
        Update: {
          account_id?: string
          branch_id?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          discount_amount?: number
          id?: string
          metadata?: Json | null
          order_code?: string | null
          payment_method?: string | null
          status?: string
          subtotal?: number
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
          voucher_id?: string | null
          webhook_data?: Json | null
          webhook_processed?: boolean | null
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
        ]
      }
      flash_sale_products: {
        Row: {
          created_at: string | null
          created_by: string | null
          discount_percentage: number
          flash_sale_id: string
          id: string
          product_id: string
          quantity_limit: number | null
          quantity_sold: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          discount_percentage: number
          flash_sale_id: string
          id?: string
          product_id: string
          quantity_limit?: number | null
          quantity_sold?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number
          flash_sale_id?: string
          id?: string
          product_id?: string
          quantity_limit?: number | null
          quantity_sold?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flash_sale_products_flash_sale_id_fkey"
            columns: ["flash_sale_id"]
            isOneToOne: false
            referencedRelation: "flash_sales"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sale_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      flash_sales: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          description: string | null
          end_time: string
          id: string
          name: string
          start_time: string
          status: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_time: string
          id?: string
          name: string
          start_time: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_time?: string
          id?: string
          name?: string
          start_time?: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      import_export_templates: {
        Row: {
          account_id: string
          created_at: string | null
          filters: Json | null
          id: string
          mapping: Json
          name: string
          resource: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          filters?: Json | null
          id?: string
          mapping: Json
          name: string
          resource: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          filters?: Json | null
          id?: string
          mapping?: Json
          name?: string
          resource?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_mappings: {
        Row: {
          created_at: string | null
          id: string
          integration_id: string | null
          is_active: boolean | null
          resource_type: string
          source_field: string
          target_field: string
          transform_function: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          integration_id?: string | null
          is_active?: boolean | null
          resource_type: string
          source_field: string
          target_field: string
          transform_function?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          integration_id?: string | null
          is_active?: boolean | null
          resource_type?: string
          source_field?: string
          target_field?: string
          transform_function?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_mappings_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_sync_items: {
        Row: {
          created_at: string | null
          error_message: string | null
          external_id: string
          id: string
          internal_id: string | null
          processed_data: Json | null
          raw_data: Json | null
          resource_type: string
          status: string
          sync_log_id: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          external_id: string
          id?: string
          internal_id?: string | null
          processed_data?: Json | null
          raw_data?: Json | null
          resource_type: string
          status: string
          sync_log_id?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          external_id?: string
          id?: string
          internal_id?: string | null
          processed_data?: Json | null
          raw_data?: Json | null
          resource_type?: string
          status?: string
          sync_log_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_sync_items_sync_log_id_fkey"
            columns: ["sync_log_id"]
            isOneToOne: false
            referencedRelation: "integration_sync_logs"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_sync_logs: {
        Row: {
          completed_at: string | null
          created_by: string | null
          error_message: string | null
          id: string
          integration_id: string | null
          items_created: number | null
          items_failed: number | null
          items_processed: number | null
          items_updated: number | null
          metadata: Json | null
          resource_type: string
          started_at: string | null
          status: string
        }
        Insert: {
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          integration_id?: string | null
          items_created?: number | null
          items_failed?: number | null
          items_processed?: number | null
          items_updated?: number | null
          metadata?: Json | null
          resource_type: string
          started_at?: string | null
          status: string
        }
        Update: {
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          integration_id?: string | null
          items_created?: number | null
          items_failed?: number | null
          items_processed?: number | null
          items_updated?: number | null
          metadata?: Json | null
          resource_type?: string
          started_at?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "integration_sync_logs_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      integrations: {
        Row: {
          account_id: string | null
          config: Json | null
          created_at: string | null
          credentials: Json | null
          description: string | null
          enabled: boolean | null
          error_message: string | null
          id: string
          last_sync_at: string | null
          metadata: Json | null
          name: string | null
          oauth_access_token: string | null
          oauth_expires_at: string | null
          oauth_refresh_token: string | null
          status: Database["public"]["Enums"]["integration_status"] | null
          type: Database["public"]["Enums"]["integration_type"]
          updated_at: string | null
          webhook_secret: string | null
          webhook_url: string | null
        }
        Insert: {
          account_id?: string | null
          config?: Json | null
          created_at?: string | null
          credentials?: Json | null
          description?: string | null
          enabled?: boolean | null
          error_message?: string | null
          id?: string
          last_sync_at?: string | null
          metadata?: Json | null
          name?: string | null
          oauth_access_token?: string | null
          oauth_expires_at?: string | null
          oauth_refresh_token?: string | null
          status?: Database["public"]["Enums"]["integration_status"] | null
          type: Database["public"]["Enums"]["integration_type"]
          updated_at?: string | null
          webhook_secret?: string | null
          webhook_url?: string | null
        }
        Update: {
          account_id?: string | null
          config?: Json | null
          created_at?: string | null
          credentials?: Json | null
          description?: string | null
          enabled?: boolean | null
          error_message?: string | null
          id?: string
          last_sync_at?: string | null
          metadata?: Json | null
          name?: string | null
          oauth_access_token?: string | null
          oauth_expires_at?: string | null
          oauth_refresh_token?: string | null
          status?: Database["public"]["Enums"]["integration_status"] | null
          type?: Database["public"]["Enums"]["integration_type"]
          updated_at?: string | null
          webhook_secret?: string | null
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory: {
        Row: {
          attribute_id: string | null
          branch_id: string
          created_at: string | null
          created_by: string | null
          id: string
          product_id: string
          reserved_stock: number
          stock: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          attribute_id?: string | null
          branch_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          product_id: string
          reserved_stock?: number
          stock?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          attribute_id?: string | null
          branch_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          product_id?: string
          reserved_stock?: number
          stock?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_attribute_id_fkey"
            columns: ["attribute_id"]
            isOneToOne: false
            referencedRelation: "product_attributes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
        Insert: {
          account_id: string
          created_at?: string
          email: string
          expires_at?: string
          id?: number
          invite_token: string
          invited_by: string
          role: string
          updated_at?: string
        }
        Update: {
          account_id?: string
          created_at?: string
          email?: string
          expires_at?: string
          id?: number
          invite_token?: string
          invited_by?: string
          role?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      marketplace_themes: {
        Row: {
          account_id: string | null
          account_theme_id: string | null
          category: string | null
          created_at: string | null
          description: string | null
          downloads_count: number | null
          id: string
          name: string
          price: number | null
          rating: number | null
          status: string | null
          tags: string[] | null
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          account_theme_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          downloads_count?: number | null
          id?: string
          name: string
          price?: number | null
          rating?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          account_theme_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          downloads_count?: number | null
          id?: string
          name?: string
          price?: number | null
          rating?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_theme_id_fkey"
            columns: ["account_theme_id"]
            isOneToOne: false
            referencedRelation: "account_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      newsletter_subscribers: {
        Row: {
          created_at: string
          email: string
          id: string
          status: string
          subscribed_at: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          status?: string
          subscribed_at?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          status?: string
          subscribed_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      nonces: {
        Row: {
          client_token: string
          created_at: string
          expires_at: string
          id: string
          last_verification_at: string | null
          last_verification_ip: unknown | null
          last_verification_user_agent: string | null
          metadata: Json | null
          nonce: string
          purpose: string
          revoked: boolean
          revoked_reason: string | null
          scopes: string[] | null
          used_at: string | null
          user_id: string | null
          verification_attempts: number
        }
        Insert: {
          client_token: string
          created_at?: string
          expires_at: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce: string
          purpose: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Update: {
          client_token?: string
          created_at?: string
          expires_at?: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce?: string
          purpose?: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Relationships: []
      }
      notifications: {
        Row: {
          account_id: string
          body: string
          channel: Database["public"]["Enums"]["notification_channel"]
          created_at: string
          dismissed: boolean
          expires_at: string | null
          id: number
          link: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          account_id: string
          body: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          account_id?: string
          body?: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      oa_configurations: {
        Row: {
          access_token: string | null
          account_id: string | null
          app_id: string | null
          author_id: string | null
          created_at: string | null
          id: string
          is_system_default: boolean | null
          oa_id: string | null
          oa_metadata: Json | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          refresh_token: string | null
          secret_key: string | null
          theme_id: string | null
          token_expires_at: string | null
          updated_at: string | null
          user_oa_id: string | null
        }
        Insert: {
          access_token?: string | null
          account_id?: string | null
          app_id?: string | null
          author_id?: string | null
          created_at?: string | null
          id?: string
          is_system_default?: boolean | null
          oa_id?: string | null
          oa_metadata?: Json | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          refresh_token?: string | null
          secret_key?: string | null
          theme_id?: string | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_oa_id?: string | null
        }
        Update: {
          access_token?: string | null
          account_id?: string | null
          app_id?: string | null
          author_id?: string | null
          created_at?: string | null
          id?: string
          is_system_default?: boolean | null
          oa_id?: string | null
          oa_metadata?: Json | null
          oa_type?: Database["public"]["Enums"]["oa_type"]
          refresh_token?: string | null
          secret_key?: string | null
          theme_id?: string | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_oa_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string
          id: string
          order_id: string
          price_amount: number | null
          product_id: string
          quantity: number
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          order_id: string
          price_amount?: number | null
          product_id: string
          quantity?: number
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          order_id?: string
          price_amount?: number | null
          product_id?: string
          quantity?: number
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
        Insert: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at?: string
        }
        Update: {
          account_id?: string
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency?: string
          id?: string
          status?: Database["public"]["Enums"]["payment_status"]
          total_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      product_attributes: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          name: string
          price_modifier: number | null
          product_id: string
          updated_at: string | null
          updated_by: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name: string
          price_modifier?: number | null
          product_id: string
          updated_at?: string | null
          updated_by?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name?: string
          price_modifier?: number | null
          product_id?: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_attributes_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          account_id: string
          barcode: string | null
          category_id: string | null
          compare_at_price: number
          created_at: string | null
          created_by: string | null
          description: string | null
          dimensions: Json | null
          external_id: string | null
          id: string
          image_url: string | null
          image_urls: string[] | null
          metadata: Json | null
          name: string
          price: number
          sku: string | null
          status: string | null
          tax_rate: number | null
          type: Database["public"]["Enums"]["product_type"]
          updated_at: string | null
          updated_by: string | null
          weight: number | null
        }
        Insert: {
          account_id: string
          barcode?: string | null
          category_id?: string | null
          compare_at_price: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          dimensions?: Json | null
          external_id?: string | null
          id?: string
          image_url?: string | null
          image_urls?: string[] | null
          metadata?: Json | null
          name: string
          price: number
          sku?: string | null
          status?: string | null
          tax_rate?: number | null
          type?: Database["public"]["Enums"]["product_type"]
          updated_at?: string | null
          updated_by?: string | null
          weight?: number | null
        }
        Update: {
          account_id?: string
          barcode?: string | null
          category_id?: string | null
          compare_at_price?: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          dimensions?: Json | null
          external_id?: string | null
          id?: string
          image_url?: string | null
          image_urls?: string[] | null
          metadata?: Json | null
          name?: string
          price?: number
          sku?: string | null
          status?: string | null
          tax_rate?: number | null
          type?: Database["public"]["Enums"]["product_type"]
          updated_at?: string | null
          updated_by?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          id: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Insert: {
          id?: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Update: {
          id?: number
          permission?: Database["public"]["Enums"]["app_permissions"]
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      roles: {
        Row: {
          hierarchy_level: number
          name: string
        }
        Insert: {
          hierarchy_level: number
          name: string
        }
        Update: {
          hierarchy_level?: number
          name?: string
        }
        Relationships: []
      }
      short_links: {
        Row: {
          account_id: string
          click_count: number | null
          created_at: string | null
          description: string | null
          expires_at: string | null
          id: string
          last_clicked_at: string | null
          metadata: Json | null
          reference_id: string
          reference_table: string
          short_id: string
          title: string | null
          type: Database["public"]["Enums"]["short_link_type"]
          updated_at: string | null
          url: string
        }
        Insert: {
          account_id: string
          click_count?: number | null
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string
          last_clicked_at?: string | null
          metadata?: Json | null
          reference_id: string
          reference_table: string
          short_id: string
          title?: string | null
          type?: Database["public"]["Enums"]["short_link_type"]
          updated_at?: string | null
          url: string
        }
        Update: {
          account_id?: string
          click_count?: number | null
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string
          last_clicked_at?: string | null
          metadata?: Json | null
          reference_id?: string
          reference_table?: string
          short_id?: string
          title?: string | null
          type?: Database["public"]["Enums"]["short_link_type"]
          updated_at?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_items: {
        Row: {
          created_at: string
          id: string
          interval: string
          interval_count: number
          metadata: Json | null
          price_amount: number | null
          product_id: string
          quantity: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          interval: string
          interval_count: number
          metadata?: Json | null
          price_amount?: number | null
          product_id: string
          quantity?: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          interval?: string
          interval_count?: number
          metadata?: Json | null
          price_amount?: number | null
          product_id?: string
          quantity?: number
          subscription_id?: string
          type?: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          is_free: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
        Insert: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at?: string
          currency: string
          id: string
          is_free?: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Update: {
          account_id?: string
          active?: boolean
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end?: boolean
          created_at?: string
          currency?: string
          id?: string
          is_free?: boolean | null
          period_ends_at?: string
          period_starts_at?: string
          status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      temp_images: {
        Row: {
          account_id: string
          created_at: string | null
          expires_at: string
          id: string
          is_moved: boolean | null
          temp_path: string
          url: string
        }
        Insert: {
          account_id: string
          created_at?: string | null
          expires_at: string
          id?: string
          is_moved?: boolean | null
          temp_path: string
          url: string
        }
        Update: {
          account_id?: string
          created_at?: string | null
          expires_at?: string
          id?: string
          is_moved?: boolean | null
          temp_path?: string
          url?: string
        }
        Relationships: []
      }
      temp_themes: {
        Row: {
          account_id: string
          account_theme_id: string | null
          config: Json
          created_at: string | null
          expires_at: string
          id: string
          preview_token: string
          theme_id: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          account_theme_id?: string | null
          config?: Json
          created_at?: string | null
          expires_at: string
          id?: string
          preview_token: string
          theme_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          account_theme_id?: string | null
          config?: Json
          created_at?: string | null
          expires_at?: string
          id?: string
          preview_token?: string
          theme_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_theme_id_fkey"
            columns: ["account_theme_id"]
            isOneToOne: false
            referencedRelation: "account_themes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      theme_purchases: {
        Row: {
          buyer_account_id: string
          created_at: string | null
          id: string
          marketplace_theme_id: string
          price_paid: number
          status: string | null
          updated_at: string | null
        }
        Insert: {
          buyer_account_id: string
          created_at?: string | null
          id?: string
          marketplace_theme_id: string
          price_paid: number
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          buyer_account_id?: string
          created_at?: string | null
          id?: string
          marketplace_theme_id?: string
          price_paid?: number
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_marketplace_theme_id_fkey"
            columns: ["marketplace_theme_id"]
            isOneToOne: false
            referencedRelation: "marketplace_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      theme_reviews: {
        Row: {
          comment: string | null
          created_at: string | null
          id: string
          marketplace_theme_id: string
          rating: number
          reviewer_account_id: string
          updated_at: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          id?: string
          marketplace_theme_id: string
          rating: number
          reviewer_account_id: string
          updated_at?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          id?: string
          marketplace_theme_id?: string
          rating?: number
          reviewer_account_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "theme_reviews_marketplace_theme_id_fkey"
            columns: ["marketplace_theme_id"]
            isOneToOne: false
            referencedRelation: "marketplace_themes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      themes: {
        Row: {
          author_id: string | null
          category: string | null
          config: Json
          created_at: string | null
          description: string | null
          id: string
          mini_app_id: string | null
          name: string
          oa_config_id: string | null
          preview_url: string | null
          thumbnail_url: string | null
          type: Database["public"]["Enums"]["theme_type"]
          updated_at: string | null
          version: string | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          config?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          mini_app_id?: string | null
          name: string
          oa_config_id?: string | null
          preview_url?: string | null
          thumbnail_url?: string | null
          type: Database["public"]["Enums"]["theme_type"]
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          config?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          mini_app_id?: string | null
          name?: string
          oa_config_id?: string | null
          preview_url?: string | null
          thumbnail_url?: string | null
          type?: Database["public"]["Enums"]["theme_type"]
          updated_at?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "themes_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
        ]
      }
      voucher_customer_phones: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          phone_number: string
          voucher_id: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          phone_number: string
          voucher_id: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          phone_number?: string
          voucher_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "voucher_customer_phones_voucher_id_fkey"
            columns: ["voucher_id"]
            isOneToOne: false
            referencedRelation: "vouchers"
            referencedColumns: ["id"]
          },
        ]
      }
      voucher_redemptions: {
        Row: {
          customer_id: string | null
          discount_amount: number
          id: string
          order_id: string
          redeemed_at: string | null
          voucher_id: string
        }
        Insert: {
          customer_id?: string | null
          discount_amount: number
          id?: string
          order_id: string
          redeemed_at?: string | null
          voucher_id: string
        }
        Update: {
          customer_id?: string | null
          discount_amount?: number
          id?: string
          order_id?: string
          redeemed_at?: string | null
          voucher_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "voucher_redemptions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "customer_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voucher_redemptions_voucher_id_fkey"
            columns: ["voucher_id"]
            isOneToOne: false
            referencedRelation: "vouchers"
            referencedColumns: ["id"]
          },
        ]
      }
      vouchers: {
        Row: {
          account_id: string
          code: string
          created_at: string | null
          created_by: string | null
          description: string | null
          discount_type: string
          discount_value: number
          end_date: string
          excluded_category_ids: string[] | null
          excluded_product_ids: string[] | null
          first_time_customers_only: boolean | null
          id: string
          included_category_ids: string[] | null
          included_product_ids: string[] | null
          is_customer_specific: boolean | null
          max_discount_value: number | null
          max_uses: number | null
          min_order_value: number | null
          min_previous_orders: number | null
          name: string
          start_date: string
          status: string | null
          updated_at: string | null
          updated_by: string | null
          usage_limit_per_customer: number | null
          uses_count: number | null
        }
        Insert: {
          account_id: string
          code: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type: string
          discount_value: number
          end_date: string
          excluded_category_ids?: string[] | null
          excluded_product_ids?: string[] | null
          first_time_customers_only?: boolean | null
          id?: string
          included_category_ids?: string[] | null
          included_product_ids?: string[] | null
          is_customer_specific?: boolean | null
          max_discount_value?: number | null
          max_uses?: number | null
          min_order_value?: number | null
          min_previous_orders?: number | null
          name: string
          start_date: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_limit_per_customer?: number | null
          uses_count?: number | null
        }
        Update: {
          account_id?: string
          code?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type?: string
          discount_value?: number
          end_date?: string
          excluded_category_ids?: string[] | null
          excluded_product_ids?: string[] | null
          first_time_customers_only?: boolean | null
          id?: string
          included_category_ids?: string[] | null
          included_product_ids?: string[] | null
          is_customer_specific?: boolean | null
          max_discount_value?: number | null
          max_uses?: number | null
          min_order_value?: number | null
          min_previous_orders?: number | null
          name?: string
          start_date?: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_limit_per_customer?: number | null
          uses_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      zns_templates: {
        Row: {
          account_id: string | null
          created_at: string | null
          enabled: boolean | null
          event_type: Database["public"]["Enums"]["zns_event_type"]
          id: string
          oa_config_id: string | null
          params: Json | null
          template_id: string
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          created_at?: string | null
          enabled?: boolean | null
          event_type: Database["public"]["Enums"]["zns_event_type"]
          id?: string
          oa_config_id?: string | null
          params?: Json | null
          template_id: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          created_at?: string | null
          enabled?: boolean | null
          event_type?: Database["public"]["Enums"]["zns_event_type"]
          id?: string
          oa_config_id?: string | null
          params?: Json | null
          template_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
        ]
      }
      zns_usage: {
        Row: {
          account_id: string | null
          cost: number | null
          event_type: Database["public"]["Enums"]["zns_event_type"]
          id: string
          metadata: Json | null
          oa_config_id: string | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          sent_at: string | null
          status: Database["public"]["Enums"]["zns_status"]
        }
        Insert: {
          account_id?: string | null
          cost?: number | null
          event_type: Database["public"]["Enums"]["zns_event_type"]
          id?: string
          metadata?: Json | null
          oa_config_id?: string | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          sent_at?: string | null
          status: Database["public"]["Enums"]["zns_status"]
        }
        Update: {
          account_id?: string | null
          cost?: number | null
          event_type?: Database["public"]["Enums"]["zns_event_type"]
          id?: string
          metadata?: Json | null
          oa_config_id?: string | null
          oa_type?: Database["public"]["Enums"]["oa_type"]
          sent_at?: string | null
          status?: Database["public"]["Enums"]["zns_status"]
        }
        Relationships: [
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      account_product_counts: {
        Row: {
          account_id: string | null
          product_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      account_zns_counts: {
        Row: {
          account_id: string | null
          zns_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      daily_orders: {
        Row: {
          account_id: string | null
          average_order_value: number | null
          customers_count: number | null
          date: string | null
          orders_count: number | null
          revenue: number | null
          source: string | null
          theme_id: string | null
        }
        Relationships: []
      }
      daily_pageviews: {
        Row: {
          account_id: string | null
          date: string | null
          desktop_pageviews: number | null
          desktop_visitors: number | null
          mobile_pageviews: number | null
          mobile_visitors: number | null
          pageviews_count: number | null
          source: string | null
          tablet_pageviews: number | null
          tablet_visitors: number | null
          theme_id: string | null
          visitors_count: number | null
        }
        Relationships: []
      }
      daily_product_events: {
        Row: {
          account_id: string | null
          add_to_carts: number | null
          date: string | null
          product_id: string | null
          purchases: number | null
          source: string | null
          theme_id: string | null
          views: number | null
        }
        Relationships: []
      }
      user_account_workspace: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
        }
        Relationships: []
      }
      user_accounts: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          role: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
    }
    Functions: {
      accept_invitation: {
        Args: { token: string; user_id: string }
        Returns: string
      }
      add_customer_phones_to_voucher: {
        Args: { p_voucher_id: string; p_phone_numbers: string[] }
        Returns: Json
      }
      add_invitations_to_account: {
        Args: {
          account_slug: string
          invitations: Database["public"]["CompositeTypes"]["invitation"][]
        }
        Returns: Database["public"]["Tables"]["invitations"]["Row"][]
      }
      apply_voucher_to_order: {
        Args: {
          p_order_id: string
          p_voucher_code: string
          p_customer_phone?: string
        }
        Returns: Json
      }
      can_action_account_member: {
        Args: { target_team_account_id: string; target_user_id: string }
        Returns: boolean
      }
      can_connect_oa: {
        Args: { oa_config_id: string }
        Returns: boolean
      }
      can_create_resource: {
        Args: { p_account_id: string; p_resource_type: string }
        Returns: boolean
      }
      cleanup_expired_temp_themes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_default_integrations: {
        Args: { p_account_id: string }
        Returns: undefined
      }
      create_flash_sale: {
        Args: {
          p_account_id: string
          p_name: string
          p_description: string
          p_start_time: string
          p_end_time: string
          p_products: Json
          p_status?: string
        }
        Returns: Json
      }
      create_import_export_templates_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_invitation: {
        Args: { account_id: string; email: string; role: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
      }
      create_nonce: {
        Args: {
          p_user_id?: string
          p_purpose?: string
          p_expires_in_seconds?: number
          p_metadata?: Json
          p_scopes?: string[]
          p_revoke_previous?: boolean
        }
        Returns: Json
      }
      create_order: {
        Args: {
          p_account_id: string
          p_customer_id: string
          p_branch_id: string
          p_product_id: string
          p_quantity: number
          p_total_amount: number
          p_payment_method: string
          p_status: string
        }
        Returns: Json
      }
      create_order_with_inventory: {
        Args: {
          p_account_id: string
          p_customer_id: string
          p_branch_id: string
          p_items: Json
          p_subtotal: number
          p_discount: number
          p_total_amount: number
          p_payment_method: string
          p_status?: string
          p_voucher_code?: string
          p_voucher_id?: string
          p_voucher_discount?: number
          p_metadata?: Json
          p_webhook_url?: string
        }
        Returns: Json
      }
      create_sample_data_for_account: {
        Args: { p_account_id: string; p_industry_template: string }
        Returns: Json
      }
      create_team_account: {
        Args: { account_name: string }
        Returns: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          phone: string | null
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
        }
      }
      create_voucher: {
        Args: {
          p_account_id: string
          p_code: string
          p_name: string
          p_description: string
          p_discount_type: string
          p_discount_value: number
          p_min_order_value: number
          p_max_discount_value: number
          p_max_uses: number
          p_start_date: string
          p_end_date: string
          p_is_customer_specific?: boolean
          p_usage_limit_per_customer?: number
          p_first_time_customers_only?: boolean
          p_min_previous_orders?: number
          p_excluded_product_ids?: string[]
          p_included_product_ids?: string[]
          p_excluded_category_ids?: string[]
          p_included_category_ids?: string[]
        }
        Returns: Json
      }
      decrement_account_counter: {
        Args: {
          p_account_id: string
          p_counter_name: string
          p_decrement?: number
        }
        Returns: undefined
      }
      delete_flash_sale: {
        Args: { p_flash_sale_id: string; p_account_id: string }
        Returns: Json
      }
      delete_voucher: {
        Args: { p_voucher_id: string; p_account_id: string }
        Returns: Json
      }
      get_account_invitations: {
        Args: { account_slug: string }
        Returns: {
          id: number
          email: string
          account_id: string
          invited_by: string
          role: string
          created_at: string
          updated_at: string
          expires_at: string
          inviter_name: string
          inviter_email: string
        }[]
      }
      get_account_members: {
        Args: { account_slug: string }
        Returns: {
          id: string
          user_id: string
          account_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          name: string
          email: string
          picture_url: string
          created_at: string
          updated_at: string
        }[]
      }
      get_config: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_dashboard_overview: {
        Args: { p_account_id: string; p_start_date: string }
        Returns: {
          total_visitors: number
          total_pageviews: number
          total_orders: number
          total_revenue: number
          conversion_rate: number
        }[]
      }
      get_nonce_status: {
        Args: { p_id: string }
        Returns: Json
      }
      get_oa_config_for_account: {
        Args: { p_account_id: string }
        Returns: string
      }
      get_product_flash_sale: {
        Args: { p_product_id: string; p_account_id: string }
        Returns: Json
      }
      get_upper_system_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_active_subscription: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      has_more_elevated_role: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: {
          user_id: string
          account_id: string
          permission_name: Database["public"]["Enums"]["app_permissions"]
        }
        Returns: boolean
      }
      has_role_on_account: {
        Args: { account_id: string; account_role?: string }
        Returns: boolean
      }
      has_same_role_hierarchy_level: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      increment_account_counter: {
        Args: {
          p_account_id: string
          p_counter_name: string
          p_increment?: number
        }
        Returns: undefined
      }
      initialize_account_usage_stats: {
        Args: { p_account_id: string }
        Returns: boolean
      }
      insert_analytics_event: {
        Args: {
          account_id: string
          theme_id: string
          event_type: string
          event_data: Json
          visitor_id: string
          user_id: string
          device_type: string
          source: string
        }
        Returns: Json
      }
      is_aal2: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_account_owner: {
        Args: { account_id: string }
        Returns: boolean
      }
      is_account_team_member: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      is_mfa_compliant: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_oa_token_valid: {
        Args: { oa_config_id: string }
        Returns: boolean
      }
      is_set: {
        Args: { field_name: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_team_member: {
        Args: { account_id: string; user_id: string }
        Returns: boolean
      }
      refresh_usage_stats: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      remove_customer_phones_from_voucher: {
        Args: { p_voucher_id: string; p_phone_numbers: string[] }
        Returns: Json
      }
      revoke_nonce: {
        Args: { p_id: string; p_reason?: string }
        Returns: boolean
      }
      save_temp_theme_to_account: {
        Args: {
          p_temp_theme_id: string
          p_account_id: string
          p_mini_app_id: string
        }
        Returns: string
      }
      team_account_workspace: {
        Args: { account_slug: string }
        Returns: {
          id: string
          name: string
          picture_url: string
          slug: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          subscription_status: Database["public"]["Enums"]["subscription_status"]
          permissions: Database["public"]["Enums"]["app_permissions"][]
        }[]
      }
      transfer_team_account_ownership: {
        Args: { target_account_id: string; new_owner_id: string }
        Returns: undefined
      }
      update_flash_sale: {
        Args: {
          p_flash_sale_id: string
          p_account_id: string
          p_name: string
          p_description: string
          p_start_time: string
          p_end_time: string
          p_products: Json
          p_status?: string
        }
        Returns: Json
      }
      upsert_order: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_order_id: string
          status: Database["public"]["Enums"]["payment_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          total_amount: number
          currency: string
          line_items: Json
        }
        Returns: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
      }
      upsert_subscription: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_subscription_id: string
          active: boolean
          status: Database["public"]["Enums"]["subscription_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          currency: string
          period_starts_at: string
          period_ends_at: string
          line_items: Json
          trial_starts_at?: string
          trial_ends_at?: string
        }
        Returns: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          is_free: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
      }
      verify_nonce: {
        Args: {
          p_token: string
          p_purpose: string
          p_user_id?: string
          p_required_scopes?: string[]
          p_max_verification_attempts?: number
          p_ip?: unknown
          p_user_agent?: string
        }
        Returns: Json
      }
    }
    Enums: {
      app_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
        | "teams.manage"
        | "products.manage"
        | "categories.manage"
        | "orders.manage"
        | "points.manage"
        | "branches.manage"
        | "inventory.manage"
        | "notifications.manage"
        | "miniapps.manage"
        | "miniapps.view"
        | "miniapps.themes.manage"
        | "zns.manage"
        | "zns.view"
        | "customers.manage"
        | "customers.view"
        | "orders.view"
        | "points.view"
        | "integrations.manage"
        | "integrations.view"
        | "integrations.connect"
        | "data.manage"
      billing_provider: "stripe" | "lemon-squeezy" | "paddle" | "zalopay"
      integration_status: "not_connected" | "connected" | "error" | "pending"
      integration_type:
        | "stripe"
        | "zapier"
        | "zalo"
        | "shopee"
        | "lazada"
        | "tiktok"
        | "facebook"
        | "google"
        | "custom"
        | "ipos"
      notification_channel: "in_app" | "email"
      notification_type: "info" | "warning" | "error"
      oa_type: "shared" | "private"
      payment_status: "pending" | "succeeded" | "failed"
      product_type: "physical" | "digital" | "service"
      short_link_type: "theme" | "product" | "campaign" | "custom"
      subscription_item_type: "flat" | "per_seat" | "metered"
      subscription_status:
        | "active"
        | "trialing"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "incomplete"
        | "incomplete_expired"
        | "paused"
      theme_type: "free" | "paid" | "custom" | "default"
      zns_event_type:
        | "order_created"
        | "order_updated"
        | "promotion"
        | "theme_activated"
      zns_status: "pending" | "success" | "failed"
    }
    CompositeTypes: {
      invitation: {
        email: string | null
        role: string | null
      }
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: { _bucket_id: string; _name: string }
        Returns: undefined
      }
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      delete_prefix: {
        Args: { _bucket_id: string; _name: string }
        Returns: boolean
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_level: {
        Args: { name: string }
        Returns: number
      }
      get_prefix: {
        Args: { name: string }
        Returns: string
      }
      get_prefixes: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_legacy_v1: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v1_optimised: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v2: {
        Args: {
          prefix: string
          bucket_name: string
          limits?: number
          levels?: number
          start_after?: string
        }
        Returns: {
          key: string
          name: string
          id: string
          updated_at: string
          created_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      app_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
        "teams.manage",
        "products.manage",
        "categories.manage",
        "orders.manage",
        "points.manage",
        "branches.manage",
        "inventory.manage",
        "notifications.manage",
        "miniapps.manage",
        "miniapps.view",
        "miniapps.themes.manage",
        "zns.manage",
        "zns.view",
        "customers.manage",
        "customers.view",
        "orders.view",
        "points.view",
        "integrations.manage",
        "integrations.view",
        "integrations.connect",
        "data.manage",
      ],
      billing_provider: ["stripe", "lemon-squeezy", "paddle", "zalopay"],
      integration_status: ["not_connected", "connected", "error", "pending"],
      integration_type: [
        "stripe",
        "zapier",
        "zalo",
        "shopee",
        "lazada",
        "tiktok",
        "facebook",
        "google",
        "custom",
        "ipos",
      ],
      notification_channel: ["in_app", "email"],
      notification_type: ["info", "warning", "error"],
      oa_type: ["shared", "private"],
      payment_status: ["pending", "succeeded", "failed"],
      product_type: ["physical", "digital", "service"],
      short_link_type: ["theme", "product", "campaign", "custom"],
      subscription_item_type: ["flat", "per_seat", "metered"],
      subscription_status: [
        "active",
        "trialing",
        "past_due",
        "canceled",
        "unpaid",
        "incomplete",
        "incomplete_expired",
        "paused",
      ],
      theme_type: ["free", "paid", "custom", "default"],
      zns_event_type: [
        "order_created",
        "order_updated",
        "promotion",
        "theme_activated",
      ],
      zns_status: ["pending", "success", "failed"],
    },
  },
  storage: {
    Enums: {},
  },
} as const

