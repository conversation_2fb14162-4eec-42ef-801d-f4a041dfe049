import { createClient } from '@supabase/supabase-js';
import { initZnsEventHandler } from '@kit/zns/lib/event-handler';
import { getSupabaseClient } from '@kit/supabase/client';

let initialized = false;

/**
 * Khởi tạo ZNS Event Handler
 * Chỉ khởi tạo một lần
 */
export function initializeZnsEventHandler() {
  if (initialized) return;
  
  const supabase = getSupabaseClient();
  initZnsEventHandler(supabase);
  
  initialized = true;
  console.log('ZNS Event Handler initialized');
}
