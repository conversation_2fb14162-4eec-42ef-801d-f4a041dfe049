import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { initZnsEventHandler } from '@kit/zns/';

let initialized = false;

/**
 * Khởi tạo ZNS Event Handler
 * Chỉ khởi tạo một lần
 */
export function initializeZnsEventHandler() {
  if (initialized) return;

  const supabase = getSupabaseServerClient();
  initZnsEventHandler(supabase);

  initialized = true;
  console.log('ZNS Event Handler initialized');
}
