{"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> n<PERSON>i cửa hàng của bạn với các dịch v<PERSON> kh<PERSON>c", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "setup": "Cài đặt", "connected": "<PERSON><PERSON> kết nối", "not_connected": "<PERSON><PERSON><PERSON> k<PERSON>", "enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt", "status": {"connected": "<PERSON><PERSON> kết nối", "not_connected": "<PERSON><PERSON><PERSON> k<PERSON>", "pending": "<PERSON><PERSON> chờ", "error": "Lỗi"}, "zns": {"title": "Zalo Notification Service", "description": "<PERSON><PERSON><PERSON> thông báo đến khách hàng qua Zalo OA", "name": "Mẫu thông báo", "welcome": "Chào mừng đến với Zalo Notification Service", "welcomeDescription": "Kết nối với Zalo Notification Service để gửi tin nhắn thông báo đến người dùng <PERSON>.", "getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u ngay", "notConfigured": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connected": "<PERSON><PERSON> kết nối", "tokenExpired": "To<PERSON> đã hết hạn", "tokenExpiredDescription": "Token của bạn đã hết hạn. <PERSON><PERSON> lòng kết nối lại để tiếp tục sử dụng Zalo Notification Service.", "reconnect": "<PERSON><PERSON><PERSON><PERSON> lại", "documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "documentationDescription": "T<PERSON>m hiểu thêm về Zalo Notification Service", "documentationLinks": {"overview": "<PERSON><PERSON><PERSON> quan về ZNS", "templates": "API quản lý mẫu tin", "sendZns": "API gửi tin ZNS"}, "support": "Hỗ trợ", "supportDescription": "<PERSON><PERSON><PERSON> hệ với đội ngũ hỗ trợ của <PERSON>alo", "supportLinks": {"developerPortal": "<PERSON><PERSON><PERSON> thông tin dành cho nhà phát triển", "zaloOA": "Zalo Official Account", "email": "Gửi email hỗ trợ"}, "pageTitle": "<PERSON><PERSON><PERSON>", "configure": {"title": "<PERSON><PERSON><PERSON> hình tích hợp <PERSON>", "oa_id": "ID Zalo OA", "oa_token": "Token Zalo OA", "template_id": "ID mẫu thông báo", "success": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> thành công", "error": "<PERSON><PERSON><PERSON><PERSON> thể cấu hình <PERSON>. <PERSON><PERSON> lòng thử lại."}, "setup": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin Zalo OA của bạn để kết nối với dịch vụ thông báo <PERSON>alo", "howToGet": "<PERSON><PERSON><PERSON> l<PERSON>y thông tin Zalo OA", "steps": {"step1": "<PERSON><PERSON><PERSON><PERSON> ", "step2": "<PERSON><PERSON><PERSON> nhập và chọn OA của bạn", "step3": "<PERSON><PERSON><PERSON> lý > <PERSON>hông tin ứng dụng", "step4": "Sao chép App ID và Secret Key"}, "form": {"appId": "App ID", "appIdDescription": "ID ứng dụng Zalo OA của bạn", "secretKey": "Secret Key", "secretKeyDescription": "<PERSON><PERSON><PERSON><PERSON> bí mật của <PERSON>ng dụng Zalo OA", "oaId": "OA ID", "oaIdDescription": "ID của Zalo O<PERSON> (bắt đầu bằng số)", "manualTokenSection": "<PERSON><PERSON><PERSON><PERSON> token thủ công (t<PERSON><PERSON> ch<PERSON>n)", "manualTokenDescription": "<PERSON><PERSON><PERSON> có thể nhập trực tiếp Access token và Refresh token từ", "getTokenLink": "công c<PERSON>", "selectOaTokenType": "(ch<PERSON><PERSON> lo<PERSON> \"OA Access Token\" và nhấp \"Lấy Access Token\")", "enterAppIdFirst": "nhập App ID trước", "accessToken": "Access Token", "accessTokenPlaceholder": "Nhập Access Token của Zalo OA", "accessTokenDescription": "Token truy cập để gọi API Zalo", "refreshToken": "Refresh <PERSON>", "refreshTokenPlaceholder": "Nhập Refresh <PERSON> của <PERSON>", "refreshTokenDescription": "Token để làm mới Access Token khi hết hạn", "submit": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancel": "<PERSON><PERSON><PERSON>"}, "permissionDenied": "<PERSON><PERSON><PERSON> không có quyền cập nhật cấu hình OA này. Cấu hình này được quản lý bởi {{manager}}.", "cannotModifySystem": "Bạn không thể chỉnh sửa cấu hình OA mặc định của hệ thống. <PERSON><PERSON> lòng tạo một cấu hình mới cho tài khoản của bạn.", "systemAuthor": "<PERSON><PERSON> th<PERSON>ng", "themeAuthor": "tác g<PERSON><PERSON> giao <PERSON>n", "anotherUser": "người dùng kh<PERSON>c", "success": "<PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON>alo OA thành công", "error": "<PERSON><PERSON><PERSON><PERSON> thể lưu cấu hình <PERSON> O<PERSON>. <PERSON><PERSON> lòng thử lại."}, "connect": {"title": "Kết nối Zalo Notification Service", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Official Account để gửi thông báo", "status": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectedDescription": "Zalo OA của bạn đã được kết nối và sẵn sàng gửi thông báo", "notConnectedDescription": "Zalo OA của bạn chưa được kết nối. <PERSON><PERSON> lòng nhấp vào \"<PERSON>ết nối Zalo OA\" để xác thực.", "notConfiguredDescription": "<PERSON>ết n<PERSON>i <PERSON>alo <PERSON> của bạn để bắt đầu gửi thông báo", "readyToSendNotifications": "Sẵn sàng g<PERSON>i thông báo", "zaloUser": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "zaloUserId": "ID người d<PERSON>ng <PERSON>", "enableZns": "Bật <PERSON>", "enabledDescription": "ZNS đã đư<PERSON><PERSON> bật và sẽ gửi thông báo", "disabledDescription": "ZNS đã bị tắt và sẽ không gửi thông báo", "configureOa": "<PERSON><PERSON><PERSON>", "connectOa": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "reconnectOa": "<PERSON><PERSON><PERSON> n<PERSON> lại Zalo OA", "loginOa": "Đăng nhập OA", "connecting": "<PERSON><PERSON> kết nối...", "connectionFailed": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "authUrlError": "<PERSON><PERSON><PERSON><PERSON> thể tạo URL xác thực", "checkOaConfig": "<PERSON><PERSON> lòng kiểm tra cấu hình O<PERSON> của bạn", "oaConfigError": "Lỗi cấu hình <PERSON>", "checkSettings": "<PERSON><PERSON> lỗi khi tải cấu hình <PERSON>. <PERSON>ui lòng kiểm tra cài đặt của bạn.", "setupOaFirst": "<PERSON><PERSON> lòng cấu hình OA trước", "enabledSuccess": "ZNS đã đ<PERSON><PERSON><PERSON> bật thành công", "disabledSuccess": "ZNS đã đư<PERSON><PERSON> tắt thành công", "updateStatusError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái <PERSON>", "oaInformation": "Thông tin Zalo OA", "oaInformationDescription": "<PERSON> tiết về Zalo Official Account đ<PERSON> kết nối", "oaConfigType": "Loạ<PERSON> c<PERSON>u h<PERSON>nh O<PERSON>", "systemDefault": "Mặc đ<PERSON><PERSON> hệ thống", "theme": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "createdBy": "Tạo bởi", "tokenExpires": "<PERSON><PERSON> hết hạn", "tokenExpired": "To<PERSON> đã hết hạn", "tokenExpiresAt": "To<PERSON> hết hạn vào", "expiresInDays": "Còn {{days}} ngày", "expiresInHours": "Còn {{hours}} giờ", "lastConnected": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> lần cuối", "connectedBy": "<PERSON><PERSON><PERSON> n<PERSON> bởi", "oaIdMissing": "OA ID chưa đ<PERSON><PERSON><PERSON> cấu hình", "connectionDetails": "<PERSON> tiết kết n<PERSON>i", "configuredAt": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON>u hình", "tokenValidatedAt": "<PERSON>h<PERSON>i gian xác thực token", "tokenLastRefreshed": "<PERSON><PERSON><PERSON> mới token lần cuối", "authTimestamp": "<PERSON>h<PERSON><PERSON> gian x<PERSON>c thực", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "advancedInfo": "Thông tin nâng cao", "connect": {"templates": {"title": "Mẫu tin thông báo", "description": "<PERSON><PERSON><PERSON><PERSON> lý các mẫu tin thông báo Z<PERSON>", "configureTemplates": "<PERSON><PERSON><PERSON> hình mẫu tin", "totalTemplates": "Tổng số mẫu", "approvedTemplates": "Mẫu đã du<PERSON>t", "pendingTemplates": "Mẫu đang chờ duyệt", "templateTypes": "Loại mẫu tin", "viewAllTemplates": "<PERSON><PERSON> tất cả mẫu tin", "tags": {"transaction": "<PERSON><PERSON><PERSON>", "customer_care": "<PERSON><PERSON><PERSON> s<PERSON> k<PERSON>ch hàng", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi"}, "events": {"order_created": "<PERSON><PERSON><PERSON> đơn hàng", "order_confirmed": "<PERSON><PERSON><PERSON> nh<PERSON>n đơn hàng", "order_shipped": "<PERSON><PERSON><PERSON>", "order_delivered": "Đ<PERSON> giao hàng", "order_cancelled": "<PERSON><PERSON><PERSON> đơn hàng"}, "createTemplate": "Tạo mẫu tin mới", "createTemplateDescription": "Tạo mẫu tin mới cho Zalo Notification Service", "configureParameters": "<PERSON><PERSON><PERSON> hình tham số", "configureParametersDescription": "<PERSON><PERSON>u hình tham số và thêm ghi chú để hỗ trợ quá trình duyệt", "stepDesign": "<PERSON><PERSON><PERSON><PERSON> kế", "stepParams": "<PERSON>ham s<PERSON>", "editTemplate": "Cài đặt mẫu tin", "useSample": "Dùng mẫu có sẵn", "sampleTemplates": "Mẫu tin có sẵn", "selectSampleTemplate": "<PERSON><PERSON><PERSON> một mẫu tin có sẵn để bắt đầu n<PERSON>h chóng", "templateApplied": "<PERSON><PERSON> áp dụng mẫu tin thành công", "templateNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mẫu tin", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "notConnectedDescription": "Zalo OA của bạn chưa được kết nối. <PERSON><PERSON> lòng kết nối để tạo mẫu tin.", "connectNow": "<PERSON><PERSON><PERSON> n<PERSON> ngay", "nameRequired": "Tên mẫu tin là bắt buộc", "titleRequired": "Tiêu đề mẫu tin là bắt buộc", "contentRequired": "Nội dung mẫu tin là bắt buộc", "typeRequired": "Loại mẫu tin là bắt buộc", "tagRequired": "Tag mẫu tin là bắt buộc", "lightImageRequired": "Ảnh header chế độ sáng là bắt buộc", "darkImageRequired": "Ảnh header chế độ tối là bắt buộc", "paramValueRequired": "<PERSON><PERSON><PERSON> trị tham số là bắt buộc", "paramTypeRequired": "<PERSON><PERSON><PERSON> tham số là bắt buộc và phải từ 1 đến 15", "validationErrors": "<PERSON><PERSON> lòng sửa các lỗi trước khi tiếp tục", "uploadingImages": "<PERSON><PERSON> tả<PERSON> lê<PERSON> header...", "headerImageRequired": "Ảnh header cho cả chế độ sáng và tối là bắt buộc", "imageUploadError": "Lỗi khi tải lên ảnh. <PERSON><PERSON> lòng thử tải lên ảnh thủ công.", "createSuccess": "Tạo mẫu tin thành công", "createError": "Lỗi khi tạo mẫu tin", "agreeTermsRequired": "<PERSON>ui lòng đồng ý với điều khoản và ch<PERSON>h sách sử dụng", "paramTypes": {"customerName": "<PERSON><PERSON><PERSON> (30)", "phoneNumber": "<PERSON><PERSON> đi<PERSON> th<PERSON> (15)", "address": "Đ<PERSON>a chỉ (200)", "code": "<PERSON><PERSON> (30)", "customLabel": "<PERSON><PERSON><PERSON><PERSON> tùy chỉnh (30)", "transactionStatus": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i giao <PERSON> (30)", "contactInfo": "<PERSON><PERSON><PERSON><PERSON> tin liên hệ (50)", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h / <PERSON><PERSON> x<PERSON>ng (5)", "productName": "<PERSON><PERSON><PERSON> sản phẩm / <PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON> (200)", "quantityAmount": "<PERSON><PERSON> / Số tiề<PERSON> (20)", "time": "<PERSON><PERSON><PERSON><PERSON>ian (20)", "otp": "OTP (10)", "url": "URL (200)", "currency": "<PERSON><PERSON><PERSON><PERSON> (VNĐ) (12)", "bankTransferNote": "Bank transfer note (90)"}}}, "templates": {"title": "Mẫu tin ZNS", "list": "<PERSON><PERSON> s<PERSON>ch mẫu tin", "listDescription": "<PERSON><PERSON><PERSON><PERSON> lý các mẫu tin ZNS của bạn", "description": "<PERSON><PERSON><PERSON><PERSON> lý các mẫu tin ZNS", "createTemplate": "Tạo mẫu tin mới", "createFirstTemplate": "Tạo mẫu tin đầu tiên", "editTemplate": "Chỉnh sửa mẫu tin", "viewTemplate": "<PERSON>em chi tiết mẫu tin", "search": "T<PERSON>m kiếm mẫu tin...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "showing": "<PERSON><PERSON>n thị {{start}}-{{end}} của {{total}} mẫu tin", "templateId": "ID mẫu tin", "templateName": "Tên mẫu tin", "quality": "<PERSON><PERSON><PERSON>", "price": "Giá", "timeout": "<PERSON>h<PERSON>i gian chờ", "openPreview": "Mở xem trước", "details": "<PERSON> ti<PERSON>", "parameters": "<PERSON>ham s<PERSON>", "sampleData": "<PERSON><PERSON> liệu mẫu", "paramName": "<PERSON>ên tham số", "paramType": "<PERSON><PERSON><PERSON> tham số", "paramRequired": "<PERSON><PERSON><PERSON> b<PERSON>", "paramLength": "<PERSON><PERSON> dài", "paramAcceptNull": "<PERSON><PERSON><PERSON> null", "sampleValue": "<PERSON><PERSON><PERSON> trị mẫu", "noParameters": "<PERSON><PERSON><PERSON><PERSON> có tham số nào", "noSampleData": "<PERSON><PERSON><PERSON>ng có dữ liệu mẫu", "sampleDataNotAvailable": "Dữ liệu mẫu chỉ khả dụng cho các mẫu tin đã được phê duyệt", "useTemplate": "Sử dụng mẫu tin này", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>", "notConnectedDescription": "Bạn cần kết nối với Zalo Notification Service trước khi quản lý mẫu tin.", "connectNow": "<PERSON><PERSON><PERSON> n<PERSON> ngay", "noTemplates": "Chưa có mẫu tin nào", "noTemplatesDescription": "Bạn chưa có mẫu tin ZNS nào. Hãy tạo mẫu tin đầu tiên.", "noTemplatesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mẫu tin nào phù hợp", "configureParameters": "<PERSON><PERSON><PERSON> hình tham số", "configureParametersDescription": "<PERSON><PERSON>u hình tham số và thêm ghi chú để hỗ trợ quá trình duyệt", "submitForReview": "<PERSON><PERSON><PERSON>", "submitForReviewDescription": "<PERSON>ọn cài đặt tham số tương ứng và điền ghi chú nhằm hỗ trợ kiểm duyệt ch<PERSON>h xác", "parameterName": "<PERSON>ên tham số", "parameterType": "<PERSON><PERSON><PERSON> đặt kỹ thuật", "parameterValue": "<PERSON><PERSON><PERSON> dung tham số", "parameterTypesInfo": "<PERSON><PERSON><PERSON> tham số (ký tự tối đa)", "example": "VD", "reviewNotes": "<PERSON><PERSON> chú cho ki<PERSON>", "reviewNotesPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho nội dung", "reviewNotesDescription": "<PERSON><PERSON> cấp thêm thông tin để giúp người kiểm duyệt hiểu rõ về mẫu tin của bạn", "agreeTerms": "Tôi đã đọc và đồng ý với", "termsAndPolicies": "<PERSON><PERSON><PERSON><PERSON> và <PERSON> sách sử dụng", "agreeTermsRequired": "<PERSON>ui lòng đồng ý với điều khoản và ch<PERSON>h sách sử dụng", "validationErrors": "<PERSON><PERSON> lòng sửa các lỗi trước khi tiếp tục", "nameRequired": "Tên mẫu tin là bắt buộc", "titleRequired": "Tiêu đề mẫu tin là bắt buộc", "contentRequired": "Nội dung mẫu tin là bắt buộc", "typeRequired": "Loại mẫu tin là bắt buộc", "tagRequired": "Tag mẫu tin là bắt buộc", "lightImageRequired": "Ảnh header chế độ sáng là bắt buộc", "darkImageRequired": "Ảnh header chế độ tối là bắt buộc", "paramValueRequired": "<PERSON><PERSON><PERSON> trị tham số là bắt buộc", "paramTypeRequired": "<PERSON><PERSON>i tham số là bắt buộc", "tableData": "<PERSON><PERSON><PERSON> dữ liệu", "addTableRow": "<PERSON><PERSON><PERSON><PERSON> dòng", "tableRowTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "tableRowValue": "<PERSON><PERSON><PERSON> trị", "tableRowType": "<PERSON><PERSON><PERSON> dòng", "buttonSettings": "<PERSON><PERSON><PERSON> thao tác", "buttonSettingsDescription": "Chỉ đư<PERSON><PERSON> thêm tối đa 2 nút thao tác", "addButton": "<PERSON><PERSON><PERSON><PERSON> nút thao tác", "paramTypes": {"customerName": "<PERSON><PERSON><PERSON> (30)", "phoneNumber": "<PERSON><PERSON> đi<PERSON> th<PERSON> (15)", "address": "Đ<PERSON>a chỉ (200)", "code": "<PERSON><PERSON> (30)", "customLabel": "<PERSON><PERSON><PERSON><PERSON> tùy chỉnh (30)", "transactionStatus": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i giao <PERSON> (30)", "contactInfo": "<PERSON><PERSON><PERSON><PERSON> tin liên hệ (50)", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h / <PERSON><PERSON> x<PERSON>ng (5)", "productName": "<PERSON><PERSON><PERSON> sản phẩm / <PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON> (200)", "quantityAmount": "<PERSON><PERSON> / Số tiề<PERSON> (20)", "time": "<PERSON><PERSON><PERSON><PERSON>ian (20)", "otp": "OTP (10)", "url": "URL (200)", "currency": "<PERSON><PERSON><PERSON><PERSON> (VNĐ) (12)", "bankTransferNote": "Bank transfer note (90)"}, "refreshSuccess": "<PERSON><PERSON><PERSON> mới danh sách mẫu tin thành công", "templateNotFoundDescription": "Mẫu tin bạn đang tìm kiếm không tồn tại hoặc bạn không có quyền truy cập.", "rejectionReason": "<PERSON>ý do từ chối", "statusReason": "Lý do trạng thái", "cannotEditTemplate": "Chỉ có thể chỉnh sửa mẫu tin có trạng thái REJECT", "editTemplateDescription": "Chỉnh sửa thông tin mẫu tin của bạn dưới đây", "editFormPlaceholder": "Form chỉnh sửa mẫu tin sẽ được hiển thị ở đây", "createTemplateDescription": "Tạo mẫu tin mới cho Zalo Notification Service", "createFormPlaceholder": "Form tạo mẫu tin sẽ được hiển thị ở đây", "templateType": "Loại mẫu tin", "templateTag": "Thẻ mẫu tin", "templateNote": "<PERSON><PERSON> chú kiể<PERSON>", "templateLayout": "B<PERSON> cục mẫu tin", "templateParams": "<PERSON><PERSON> số mẫu tin", "saveSuccess": "<PERSON><PERSON><PERSON> mẫu tin thành công", "createSuccess": "Tạo mẫu tin thành công", "editTab": "Chỉnh sửa", "previewTab": "<PERSON><PERSON>", "generatePreview": "<PERSON><PERSON><PERSON> bản xem trước", "previewGenerated": "<PERSON><PERSON><PERSON> bản xem tr<PERSON><PERSON><PERSON> thành công", "previewError": "Lỗi khi tạo bản xem trước", "noPreview": "<PERSON><PERSON><PERSON> c<PERSON> bản xem trước", "generatePreviewHelp": "Điền thông tin mẫu tin và nhấn '<PERSON><PERSON><PERSON> bản xem trước' để xem mẫu tin sẽ hiển thị như thế nào.", "previewDisclaimer": "<PERSON><PERSON><PERSON> là bản xem trước của mẫu tin. <PERSON><PERSON><PERSON> thị thực tế có thể khác.", "useSample": "Dùng mẫu có sẵn", "sampleTemplates": "Mẫu tin có sẵn", "selectSampleTemplate": "<PERSON><PERSON><PERSON> một mẫu tin có sẵn để bắt đầu n<PERSON>h chóng", "templateApplied": "<PERSON><PERSON> áp dụng mẫu tin thành công", "fetchSamplesError": "Lỗi khi lấy danh sách mẫu tin", "fetchDetailError": "Lỗi khi lấy thông tin chi tiết mẫu tin", "preview": "<PERSON><PERSON>", "use": "Sử dụng", "templatePreview": "<PERSON>em trước mẫu tin", "noPreviewAvailable": "<PERSON><PERSON><PERSON><PERSON> có bản xem trước cho mẫu tin này.", "applyError": "Lỗi khi áp dụng mẫu tin", "templateNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mẫu tin", "fetchSampleDataError": "Lỗi khi lấy dữ liệu mẫu", "fetchError": "Lỗi khi lấy danh sách template", "tokenInvalid": "Token đã hết hạn hoặc không hợp lệ. <PERSON><PERSON> lòng kết nối lại tài khoản <PERSON> của bạn.", "templateTitle": "Tiêu đề mẫu tin", "templateContent": "Nội dung mẫu tin", "templateContentHelp": "<PERSON><PERSON> dụng <n> để chèn tham số tên khách hàng", "templateNameDescription": "<PERSON><PERSON><PERSON><PERSON> tên cho mẫu tin của bạn", "templateTypeDescription": "<PERSON><PERSON>n loại mẫu tin", "templateTagDescription": "Chọn thẻ cho mẫu tin của bạn", "templateNoteDescription": "<PERSON><PERSON> chú này sẽ được gửi đến người kiểm duyệt", "templateNotePlaceholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho người kiể<PERSON> du<PERSON>", "selectType": "<PERSON><PERSON><PERSON>", "selectTag": "<PERSON><PERSON><PERSON> thẻ", "layoutDescription": "<PERSON><PERSON><PERSON> là n<PERSON>i bạn cấu hình bố cục, thành phần và tham số của mẫu tin", "types": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "authentication": "<PERSON><PERSON><PERSON> th<PERSON>c", "paymentRequest": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>h toán", "voucher": "Voucher", "serviceRating": "Đánh gi<PERSON> d<PERSON>ch vụ"}, "statusTitle": "<PERSON><PERSON><PERSON><PERSON> thái", "tagTitle": "Thẻ", "status": {"all": "<PERSON><PERSON><PERSON> cả trạng thái", "enabled": "Đ<PERSON> kích ho<PERSON>", "pendingReview": "<PERSON><PERSON> chờ du<PERSON>", "rejected": "Đ<PERSON> từ chối", "disabled": "Đ<PERSON> vô hiệu hóa"}, "tag": {"title": "Thẻ", "transaction": "<PERSON><PERSON><PERSON>", "customerCare": "<PERSON><PERSON><PERSON> s<PERSON> k<PERSON>ch hàng", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "configureTemplates": "<PERSON><PERSON><PERSON> hình mẫu", "addTemplates": "Thê<PERSON> mẫu thông báo", "addTemplate": "Thê<PERSON> mẫu", "deleteTemplate": "Xóa mẫu", "templateAdded": "<PERSON><PERSON> thêm mẫu thông báo thành công", "templateUpdated": "<PERSON><PERSON> cập nhật mẫu thông báo thành công", "templateDeleted": "Đã xóa mẫu thông báo thành công", "templateError": "Lỗi khi quản lý mẫu thông báo", "enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt", "form": {"enabled": "<PERSON><PERSON><PERSON>", "templateId": "ID mẫu", "templateIdDescription": "ID mẫu ZNS", "content": "<PERSON><PERSON>i dung", "contentDescription": "<PERSON><PERSON>i dung mẫu", "params": "<PERSON>ham s<PERSON>", "paramsDescription": "<PERSON><PERSON><PERSON> tham số sử dụng trong mẫu", "submit": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancel": "<PERSON><PERSON><PERSON>"}}, "send": {"title": "<PERSON><PERSON><PERSON>", "formTitle": "<PERSON><PERSON><PERSON>", "formDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin để gửi tin Z<PERSON>", "templateId": "ID mẫu tin", "templateIdDescription": "ID của mẫu tin ZNS bạn muốn sử dụng", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneDescription": "<PERSON><PERSON> điện tho<PERSON>i ng<PERSON><PERSON> (định dạng chu<PERSON> h<PERSON>, ví dụ: 84987654321)", "sendingMode": "<PERSON><PERSON> độ gửi", "sendingModeDescription": "<PERSON><PERSON><PERSON> chế độ gử<PERSON> tin <PERSON>", "selectSendingMode": "<PERSON><PERSON><PERSON> chế độ gửi", "sendingModes": {"normal": "<PERSON><PERSON><PERSON>", "exceedQuota": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> hạn mức", "development": "<PERSON><PERSON> độ phát triển"}, "useHashPhone": "Sử dụng hash phone", "useHashPhoneDescription": "<PERSON><PERSON> hóa số điện thoại trư<PERSON><PERSON> khi gửi", "trackingId": "Tracking ID", "trackingIdDescription": "ID theo dõi do bạn định nghĩa", "parameters": "<PERSON>ham s<PERSON>", "paramTypeDescription": "Lo<PERSON>i: {{type}}, <PERSON><PERSON> dài: {{min}}-{{max}}", "useSampleData": "Sử dụng dữ liệu mẫu", "sendButton": "<PERSON><PERSON><PERSON> tin", "result": "<PERSON><PERSON><PERSON> quả gửi tin", "resultDescription": "Thông tin về tin ZNS đã gửi", "success": "<PERSON><PERSON><PERSON> tin thành công", "error": "Lỗi khi gửi tin", "messageId": "ID tin nhắn", "sentTime": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "remainingQuota": "<PERSON><PERSON><PERSON> mức còn lại", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>", "notConnectedDescription": "Bạn cần kết nối với Zalo Notification Service trước khi gửi tin.", "connectNow": "<PERSON><PERSON><PERSON> n<PERSON> ngay", "fetchTemplateError": "Lỗi khi tải thông tin mẫu tin", "fetchSampleDataError": "Lỗi khi tải dữ liệu mẫu", "sampleDataApplied": "<PERSON><PERSON> áp dụng dữ liệu mẫu"}, "dashboard": {"title": "<PERSON><PERSON>ng đi<PERSON>u k<PERSON>n ZNS", "backToIntegrations": "Quay lại Integrations", "manageTemplates": "<PERSON><PERSON><PERSON><PERSON>", "connectionStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "connected": "<PERSON><PERSON> kết nối", "error": "Lỗi", "disconnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectedTooltip": "ZNS đã đư<PERSON><PERSON> kết nối và hoạt động", "errorTooltip": "Có lỗi với kết nối Z<PERSON>", "disconnectedTooltip": "ZNS chưa đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "notConnected": "ZNS chưa đư<PERSON><PERSON> kết nối. <PERSON><PERSON> lòng cấu hình kết nối của bạn.", "connectNow": "<PERSON><PERSON><PERSON> n<PERSON> ngay", "oaName": "<PERSON><PERSON>n <PERSON>", "oaId": "OA ID", "appId": "App ID", "lastConnected": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> lần cuối", "tokenExpires": "<PERSON><PERSON> hết hạn", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "templateStats": "<PERSON><PERSON><PERSON><PERSON> kê Template", "templateStatsTooltip": "<PERSON><PERSON><PERSON>ng kê về các template ZNS của bạn", "totalTemplates": "Tổng số Templates", "enabledTemplates": "Templates <PERSON><PERSON> k<PERSON><PERSON>", "pendingTemplates": "Templates <PERSON><PERSON> chờ <PERSON>", "rejectedTemplates": "Templates bị từ chối", "connectToViewTemplates": "<PERSON><PERSON><PERSON> n<PERSON>i với ZNS để xem thống kê template.", "quotaUsage": "S<PERSON> dụ<PERSON>", "quotaUsageTooltip": "Thông tin sử dụng quota tin nhắn ZNS hàng ngày của bạn", "dailyQuota": "<PERSON>uota hàng ng<PERSON>y", "remainingToday": "<PERSON><PERSON>n lại hôm nay", "usedToday": "Đã sử dụng hôm nay", "usagePercentage": "<PERSON><PERSON>n tr<PERSON>m sử dụng", "connectToViewQuota": "<PERSON><PERSON><PERSON> n<PERSON>i với ZNS để xem thông tin sử dụng quota.", "viewAnalytics": "Xem Analytics", "healthStatus": "<PERSON><PERSON><PERSON> trạng hoạt động", "healthStatusTooltip": "Tình trạng hoạt động hiện tại của kết nối ZNS của bạn", "apiLatency": "<PERSON><PERSON> trễ API", "uptime": "<PERSON><PERSON><PERSON><PERSON> gian ho<PERSON>t động", "errorCount": "Số lỗi", "lastError": "Lỗi gần nhất", "connectToViewHealth": "<PERSON><PERSON><PERSON> nối với ZNS để xem tình trạng hoạt động.", "manageConnection": "<PERSON><PERSON><PERSON><PERSON> lý kết n<PERSON>i", "connectionSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "connectionError": "Lỗi kết nối", "noConnectionDetails": "<PERSON><PERSON><PERSON>ng có thông tin kết nối", "pleaseConfigureFirst": "<PERSON><PERSON> lòng cấu hình kết nối trước", "accountNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài k<PERSON>n", "readyToSendNotifications": "Sẵn sàng g<PERSON>i thông báo"}}, "templates": {"title": "Mẫu thông báo", "description": "<PERSON><PERSON><PERSON> hình mẫu cho các sự kiện thông báo khác nhau", "oaConfiguration": "<PERSON><PERSON><PERSON>", "oaConfigurationDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin <PERSON>alo OA của bạn", "notificationTemplates": "Mẫu thông báo", "notificationTemplatesDescription": "<PERSON><PERSON><PERSON> hình mẫu cho các sự kiện thông báo khác nhau", "events": {"order_created": "Đ<PERSON><PERSON> hàng mới", "order_updated": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn hàng", "order_shipped": "<PERSON><PERSON><PERSON>", "order_delivered": "<PERSON><PERSON><PERSON> thành đơn hàng", "order_cancelled": "<PERSON><PERSON><PERSON> đơn hàng", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "theme_activated": "<PERSON><PERSON><PERSON> ho<PERSON> giao <PERSON>", "customer_birthday": "<PERSON><PERSON> <PERSON><PERSON>t kh<PERSON><PERSON> hàng", "abandoned_cart": "Giỏ hàng bỏ quên"}, "form": {"enabled": "<PERSON><PERSON><PERSON>", "templateId": "ID mẫu", "templateIdDescription": "ID mẫu thông báo ZNS", "content": "<PERSON><PERSON>i dung", "contentDescription": "<PERSON><PERSON>i dung mẫu thông báo", "params": "<PERSON>ham s<PERSON>", "paramsDescription": "<PERSON><PERSON><PERSON> tham số được sử dụng trong mẫu", "submit": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancel": "<PERSON><PERSON><PERSON>"}}}, "misa_eshop": {"title": "MISA eShop", "description": "Đồng bộ dữ liệu khách hàng và đơn hàng với MISA eShop. <PERSON><PERSON><PERSON> cho hồ sơ đư<PERSON><PERSON> cập nhật để quản lý bán hàng và tồn kho tốt hơn."}, "sapo": {"title": "SAPO", "description": "Tự động gửi thông tin khách hàng và đơn hàng đến <PERSON>. <PERSON><PERSON><PERSON> bảo đồng bộ hóa liền mạch cho hoạt động kinh doanh hiệu quả."}, "connect_page": {"title": "<PERSON><PERSON><PERSON> {{name}}", "description": "<PERSON>ết n<PERSON>i tài <PERSON> {{name}} của bạn để bật tích hợp", "submit": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON> n<PERSON>i thành công với {{name}}", "error": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với {{name}}. <PERSON><PERSON> lòng thử lại."}, "disconnect_confirm": {"title": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON> {{name}}", "description": "Bạn có chắc chắn muốn ngắt kết nối với {{name}}? Đi<PERSON>u này sẽ dừng tất cả các tính năng tích hợp.", "confirm": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON> ngắt kết nối thành công với {{name}}", "error": "<PERSON><PERSON><PERSON><PERSON> thể ngắt kết nối với {{name}}. <PERSON><PERSON> lòng thử lại."}, "comingSoon": "S<PERSON><PERSON> ra mắt", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "configureDescription": "<PERSON><PERSON><PERSON> hình cài đặt và tùy chọn tích hợp của bạn", "serviceUnavailable": {"title": "{{serviceName}} đang đ<PERSON><PERSON> ph<PERSON> triển", "description": "<PERSON><PERSON><PERSON> năng này hiện đang được phát triển và sẽ sẵn sàng trong tương lai gần.", "serviceInfo": "Thông tin dịch vụ", "inDevelopment": "{{serviceName}} hiện đang được phát triển bởi đội ngũ của chúng tôi.", "estimatedAvailability": "<PERSON><PERSON> kiến sẵn sàng vào: {{date}}", "interestedQuestion": "Bạn quan tâm đến tính năng này?", "priorityMessage": "<PERSON><PERSON><PERSON> cho chúng tôi biết nếu bạn muốn chúng tôi ưu tiên phát triển tính năng này sớm hơn.", "learnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "contactUs": "<PERSON><PERSON><PERSON> h<PERSON> chúng tôi", "emailSubject": "<PERSON><PERSON><PERSON> c<PERSON>u tí<PERSON> h<PERSON>p"}, "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "manageDashboard": "<PERSON><PERSON><PERSON><PERSON> lý", "setupIntegration": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "ipos": {"connect": {"title": "<PERSON><PERSON>t nối iPOS", "description": "<PERSON>ết n<PERSON>i tài k<PERSON>ản iPOS của bạn để đồng bộ sản phẩm, đơn hàng và khách hàng.", "credentials": "Thông tin xác thực", "webhook": "Webhook", "accessToken": "Access Token", "accessTokenPlaceholder": "Nhập Access Token iPOS của bạn", "accessTokenDescription": "Access Token iPOS của bạn từ cổng thông tin nhà phát triển.", "posParent": "POS Parent", "posParentPlaceholder": "Nhập ID Parent iPOS c<PERSON><PERSON> bạn", "posParentDescription": "ID Parent iPOS c<PERSON><PERSON> b<PERSON> (ID Thương hiệu).", "posId": "POS ID", "posIdPlaceholder": "Nhập ID Cửa hàng iPOS của bạn", "posIdDescription": "ID Cửa hàng iPOS của bạn.", "baseUrl": "URL API cơ sở", "baseUrlPlaceholder": "https://api.foodbook.vn", "baseUrlDescription": "URL cơ sở cho API iPOS.", "webhookSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "webhookDescription": "<PERSON><PERSON><PERSON><PERSON> lập webhook để nhận cập nhật thời gian thực từ iPOS.", "webhookUrl": "URL Webhook", "webhookUrlDescription": "Thêm URL này vào cổng thông tin nhà phát triển iPOS của bạn để nhận webhook.", "webhookEvents": "<PERSON><PERSON> kiện Webhook", "webhookEventsDescription": "<PERSON><PERSON><PERSON> ký các sự kiện sau trong cổng thông tin nhà phát triển iPOS của bạn:", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testing": "<PERSON><PERSON> kiểm tra...", "testSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "testSuccessDescription": "Đã kết nối thành công với API iPOS.", "testError": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "testErrorDescription": "Không thể kết nối với API iPOS. Vui lòng kiểm tra thông tin xác thực của bạn.", "connect": "<PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON> kết nối thành công", "successDescription": "<PERSON><PERSON>i khoản iPOS của bạn đã đư<PERSON><PERSON> kết nối thành công.", "error": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "checkOaConfig": "<PERSON><PERSON> lòng kiểm tra cấu hình iPOS của bạn.", "footer": "Cần trợ giúp? Xem <1>tài liệu API iPOS</1>.", "benefitsTitle": "<PERSON><PERSON><PERSON>ch tích hợp:", "benefit1": "Tự động đồng bộ sản phẩm từ iPOS và<PERSON> hệ thống của bạn", "benefit2": "Gi<PERSON> đơn hàng đồng bộ giữa các nền tảng", "benefit3": "<PERSON><PERSON> trì dữ liệu khách hàng nhất quán", "benefit4": "<PERSON><PERSON><PERSON><PERSON> cập nhật thời gian thực qua webhook", "setupInstructions": "H<PERSON>ớng dẫn thiết lập", "setupSteps": "<PERSON><PERSON><PERSON> thành các bước sau để thiết lập tích hợp iPOS của bạn:", "step1": "<PERSON><PERSON><PERSON><PERSON> thông tin xác thực API iPOS của bạn (bắt buộc)", "step2": "<PERSON><PERSON><PERSON> tra kết nối để xác minh thông tin xác thực", "step3": "<PERSON><PERSON><PERSON><PERSON> lập webhook để nhận cập nhật thời gian thực (t<PERSON><PERSON> chọn)", "step4": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON> tất thiết lập\" để kết thúc", "exampleNote": "<0><PERSON><PERSON><PERSON> ý:</0> <PERSON><PERSON><PERSON> giá trị mẫu được điền sẵn để kiểm tra nhanh.", "accessTokenExample": "<0>Ví dụ:</0> JHTHWPCE6OCZBW0PBH9XRRBC6JTR1UWQ", "posParentExample": "<0><PERSON><PERSON> dụ:</0> SAOBANG", "posIdExample": "<0>Ví dụ:</0> 3160", "baseUrlDefault": "<0>Mặc định:</0> https://api.foodbook.vn"}, "dashboard": {"title": "B<PERSON>ng điều khiển iPOS", "connectionStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "connected": "<PERSON><PERSON> kết nối", "connectionIssue": "<PERSON><PERSON><PERSON> đề kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectionHealthy": "<PERSON><PERSON>t nối đang hoạt động tốt", "connectionUnhealthy": "<PERSON>ết nối đang gặp vấn đề", "connectionNotEstablished": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> chưa đ<PERSON><PERSON><PERSON> thiết lập", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "testConnection": "<PERSON><PERSON><PERSON> tra", "connectionSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "connectionError": "Lỗi kết nối", "apiLatency": "<PERSON><PERSON> trễ API", "mappingStatus": "Trạng thái ánh xạ trường", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "customers": "<PERSON><PERSON><PERSON><PERSON>", "branches": "<PERSON> n<PERSON>h", "categories": "<PERSON><PERSON>", "vouchers": "<PERSON><PERSON><PERSON> gi<PERSON> giá", "configured": "<PERSON><PERSON> cấu hình", "notConfigured": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "syncedItems": "<PERSON><PERSON> đồng bộ", "noSyncYet": "<PERSON><PERSON>a có dữ liệu đồng bộ", "editMapping": "<PERSON><PERSON><PERSON>nh xạ", "setupMapping": "<PERSON><PERSON><PERSON><PERSON> lập <PERSON>nh <PERSON>", "syncHistory": "<PERSON><PERSON><PERSON> sử đồng bộ", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "syncNow": "<PERSON><PERSON><PERSON> bộ ngay", "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "syncData": "<PERSON>ồng bộ dữ liệu", "syncDataDescription": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>, đ<PERSON><PERSON> hàng và khách hàng từ iPOS", "configureMapping": "<PERSON><PERSON><PERSON> hình <PERSON>nh xạ", "configureMappingDescription": "<PERSON><PERSON><PERSON><PERSON> lập ánh xạ trường giữa iPOS và hệ thống của bạn", "viewSyncHistory": "<PERSON><PERSON> l<PERSON> sử đồng bộ", "viewSyncHistoryDescription": "<PERSON><PERSON><PERSON> tra kết quả và nhật ký đồng bộ trước đây", "configureConnection": "<PERSON><PERSON><PERSON> hình kết n<PERSON>i", "configureConnectionDescription": "<PERSON><PERSON><PERSON> nhật cài đặt và thông tin xác thực kết nối iPOS của bạn", "backToIntegrations": "Quay lại tích hợp", "viewAllMappings": "<PERSON><PERSON> t<PERSON>t cả"}, "mapping": {"title": "<PERSON><PERSON> xạ dữ liệu iPOS", "description": "<PERSON><PERSON> xạ trường từ iPOS sang hệ thống của bạn để đồng bộ dữ liệu ch<PERSON>h x<PERSON>c.", "resourceType": "<PERSON><PERSON><PERSON> tài nguyên", "selectResource": "<PERSON><PERSON><PERSON> loại tài nguyên", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "customers": "<PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON>", "branches": "<PERSON> n<PERSON>h", "vouchers": "<PERSON><PERSON><PERSON> gi<PERSON> giá", "loading": "<PERSON><PERSON> tải trường...", "sourceFields": "Trường iPOS", "sourceFieldsDescription": "<PERSON><PERSON><PERSON> trường từ đây đến trường đích.", "targetFields": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON> thống", "targetFieldsDescription": "<PERSON>hả trường iPOS vào đây để tạo ánh xạ.", "mapped": "Đã ánh xạ", "mappedTo": "<PERSON><PERSON> xạ đến", "iposLabel": "iPOS", "alwaysIpos": "<PERSON><PERSON><PERSON> là \"ipos\"", "autoMapped": "Tự động ánh xạ", "defaultField": "Mặc định", "defaultTarget": "<PERSON><PERSON><PERSON> tiêu mặc định", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "recommendedField": "<PERSON><PERSON> xuất", "recommendedForSync": "Sync ID", "systemField": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON> thống", "smartMappingTitle": "<PERSON><PERSON> xạ <PERSON> thông minh", "smartMappingDescription": "<PERSON><PERSON> thống sẽ thông minh khớp các bản ghi bằng cách sử dụng các trường đồng bộ đề xuất (đư<PERSON><PERSON> đánh dấu bằng Sync ID). Điều này giúp tránh xung đột ID và đảm bảo theo dõi bản ghi đúng cách. Các trường có * là bắt buộc.", "metadataFieldsTitle": "Trường metadata cho tích hợp di động", "metadataFieldsDescription": "<PERSON><PERSON> thống bao gồm các trường metadata đặc biệt (external_id, source, last_synced_at, integration_data) để lưu trữ các định danh bên thứ ba và thông tin nguồn. Các trường này là cần thiết cho tích hợp di động và theo dõi các bản ghi đã đồng bộ.", "defaultMappingsTitle": "<PERSON>nh xạ mặc định (Chỉ đọc)", "defaultMappingsDescription": "<PERSON><PERSON><PERSON><PERSON> ánh xạ này là cần thiết để hệ thống hoạt động chính xác và không thể thay đổi.", "noDefaultMappings": "<PERSON><PERSON><PERSON><PERSON> có ánh xạ mặc định nào cho loại tài nguyên này.", "previewDataDescription": "<PERSON><PERSON><PERSON> là bản xem trước dữ liệu của bạn sau khi ánh xạ. Bảng hiển thị các trường đích và giá trị của chúng từ iPOS.", "recommendedMappingsApplied": "Đã áp dụng {count} ánh xạ đề xuất thành công", "noNewMappingsApplied": "<PERSON><PERSON><PERSON>ng có ánh xạ mới được áp dụng. Tất cả các trường đề xuất đã được ánh xạ.", "applyRecommendedMappings": "<PERSON><PERSON> dụng ánh xạ đề xuất", "drag": "Kéo", "dropHere": "<PERSON><PERSON><PERSON> vào đây", "dropToRemove": "<PERSON><PERSON><PERSON> vào đây để xóa ánh xạ", "testMapping": "<PERSON><PERSON><PERSON> tra <PERSON>nh x<PERSON>", "testing": "<PERSON><PERSON> kiểm tra...", "testSuccess": "<PERSON><PERSON><PERSON> tra thành công", "testSuccessMessage": "<PERSON><PERSON> xử lý {count} mục", "scrollDownToPreview": "<PERSON><PERSON><PERSON><PERSON> xuống để xem trước dữ liệu", "testFailed": "<PERSON><PERSON><PERSON> tra thất b<PERSON>i", "testError": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra ánh xạ. <PERSON><PERSON> lòng thử lại.", "previewData": "<PERSON><PERSON> tr<PERSON><PERSON> dữ liệu", "noPreviewData": "<PERSON>hông có dữ liệu để xem trước. Đi<PERSON><PERSON> này có thể là do không có bản ghi nào trong iPOS hoặc ánh xạ không chính xác.", "showingPreview": "<PERSON><PERSON><PERSON> thị {shown} trên {total} mục", "syncNow": "<PERSON><PERSON><PERSON> bộ ngay", "syncing": "<PERSON><PERSON> đồng bộ...", "saveSuccess": "<PERSON><PERSON> lưu <PERSON>nh <PERSON>", "saveSuccessDescription": "<PERSON><PERSON> xạ trường của bạn đã đư<PERSON><PERSON> lưu thành công.", "saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu ánh xạ", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải trường", "integrationNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tích hợp iPOS", "connectFirst": "<PERSON><PERSON> lòng kết nối với iPOS trước."}, "sync": {"title": "Đồng bộ dữ liệu iPOS", "description": "Đồng bộ dữ liệu từ iPOS sang hệ thống của bạn.", "loading": "<PERSON><PERSON> tả<PERSON>...", "selectResource": "<PERSON><PERSON><PERSON> loại tài nguyên", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "productsDescription": "Đồng bộ dữ liệu sản phẩm từ iPOS", "orders": "<PERSON><PERSON><PERSON> hàng", "ordersDescription": "<PERSON><PERSON>ng bộ dữ liệu đơn hàng từ iPOS", "customers": "<PERSON><PERSON><PERSON><PERSON>", "customersDescription": "Đồng bộ dữ liệu kh<PERSON>ch hàng từ iPOS", "setupMapping": "<PERSON><PERSON><PERSON><PERSON> lập <PERSON>nh <PERSON>", "syncing": "<PERSON><PERSON> đồng bộ...", "startSync": "<PERSON><PERSON>t đầu đồng bộ", "viewHistory": "<PERSON><PERSON> l<PERSON> sử đồng bộ", "editMapping": "<PERSON><PERSON><PERSON>nh xạ", "syncComplete": "Đồng bộ hoàn tất", "syncResultDescription": "<PERSON><PERSON> xử lý {total} mục: {created} đã tạo/cập nhật, {failed} thất bại.", "viewDetails": "<PERSON><PERSON> chi tiết đồng bộ", "syncSuccess": "Đồng bộ hoàn tất", "syncSuccessDescription": "<PERSON><PERSON> xử lý {total} mục: {created} đã tạo/cập nhật, {failed} thất bại.", "syncError": "<PERSON>ồng bộ thất bại", "integrationNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tích hợp iPOS", "connectFirst": "<PERSON><PERSON> lòng kết nối với iPOS trước."}, "syncHistory": {"title": "<PERSON><PERSON><PERSON> sử đồng bộ iPOS", "description": "<PERSON><PERSON> l<PERSON>ch sử đồng bộ dữ liệu với iPOS.", "backToMapping": "Quay lại ánh xạ", "newSync": "Đồng bộ mới", "searchPlaceholder": "T<PERSON>m kiếm theo loại tài nguyên hoặc thông báo lỗi", "resourceFilter": "<PERSON><PERSON><PERSON> theo tài nguyên", "statusFilter": "<PERSON><PERSON><PERSON> theo trạng thái", "allResources": "<PERSON><PERSON><PERSON> cả tài nguyên", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "customers": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> công", "error": "Lỗi", "partial": "<PERSON><PERSON><PERSON>", "inProgress": "<PERSON><PERSON> ti<PERSON>n hành", "loading": "<PERSON><PERSON> tải lịch sử đồng bộ...", "noLogs": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhật ký đồng bộ", "startSync": "<PERSON><PERSON>t đầu đồng bộ", "noIntegration": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tích hợp iPOS hoạt động cho tài kho<PERSON>n này", "setupIntegration": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> t<PERSON><PERSON> hợp", "resourceType": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "items": "<PERSON><PERSON><PERSON>", "startedAt": "<PERSON><PERSON><PERSON> đ<PERSON>u", "duration": "<PERSON><PERSON><PERSON><PERSON> gian", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "itemsProcessed": "{count} đ<PERSON> xử lý", "itemsCreated": "{count} đ<PERSON> tạo", "itemsFailed": "{count} thất b<PERSON>i", "viewDetails": "<PERSON>em chi tiết"}, "syncHistoryDetail": {"title": "<PERSON> tiết đồng bộ", "loading": "<PERSON><PERSON> tải chi tiết đồng bộ...", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhật ký đồng bộ", "backToHistory": "<PERSON><PERSON> lại lịch sử đồng bộ", "errorFetchingDetails": "Lỗi khi lấy thông tin đồng bộ", "syncDetails": "<PERSON> tiết đồng bộ", "startedAt": "<PERSON><PERSON><PERSON> đ<PERSON> {0}", "processed": "Đã xử lý", "created": "Đã tạo", "updated": "<PERSON><PERSON> cập nh<PERSON>t", "startTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "endTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "duration": "<PERSON><PERSON><PERSON><PERSON> gian", "inProgress": "<PERSON><PERSON> ti<PERSON>n hành", "initiatedBy": "Khởi tạo bởi", "system": "<PERSON><PERSON> th<PERSON>", "error": "Lỗi", "items": "<PERSON><PERSON><PERSON> đ<PERSON> bộ", "itemsDescription": "<PERSON> tiết các mục riêng lẻ được xử lý trong quá trình đồng bộ này.", "overview": "<PERSON><PERSON><PERSON> quan", "success": "<PERSON><PERSON><PERSON><PERSON> công", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "noItems": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục", "noSuccessItems": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục thành công", "noFailedItems": "<PERSON><PERSON><PERSON><PERSON> tìm thấy mục thất bại", "externalId": "<PERSON> bên ngo<PERSON>i", "internalId": "ID nội bộ", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "Tạo lúc", "showingItems": "<PERSON><PERSON><PERSON> thị {shown} trên {total} mục", "showingSuccessItems": "<PERSON><PERSON>n thị {shown} trên {total} mục thành công", "showingFailedItems": "<PERSON><PERSON>n thị {shown} trên {total} mục thất bại", "itemId": "ID mục: {id}", "rawData": "<PERSON><PERSON> liệu thô", "processedData": "<PERSON><PERSON> liệu đã xử lý", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "customers": "<PERSON><PERSON><PERSON><PERSON>"}}}