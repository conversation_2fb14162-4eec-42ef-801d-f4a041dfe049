'use client';

import { useCallback, useEffect, useState } from 'react';

import Image from '@tiptap/extension-image';
import Placeholder from '@tiptap/extension-placeholder';
import TextAlign from '@tiptap/extension-text-align';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { ChevronDown, ImageIcon, Info, Maximize2 } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Card } from '@kit/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import {
  ImageUploaderAdvance,
  ImageUploaderProps,
} from '@kit/ui/image-uploader-advance';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';

import { getCategories } from '~/home/<USER>/categories/_lib/server/get-categories';
import {
  deleteProductImage,
  uploadProductImage,
} from '~/home/<USER>/products/_lib/server/upload-product-image';

import { EditorToolbar } from '../editor-toolbar';
import { FullscreenEditor } from '../fullscreen-editor';

interface TempImage {
  tempPath: string;
  url: string;
}

interface Category {
  id: string;
  name: string;
}

export function BasicInfoSection({ accountId }: { accountId: string }) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(true);
  const { control, setValue, watch } = useFormContext();
  const [thumbUrls, setThumbUrls] = useState<string[]>([]);
  const [galleryUrls, setGalleryUrls] = useState<string[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [tempImages, setTempImages] = useState<TempImage[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);

  // Watch form values to initialize local state
  const imageUrl = watch('image_url');
  const imageUrls = watch('image_urls');
  const description = watch('description');

  // Initialize local state with form values
  useEffect(() => {
    if (imageUrl && thumbUrls.length === 0) {
      setThumbUrls([imageUrl]);
    }
  }, [imageUrl, thumbUrls]);

  useEffect(() => {
    if (imageUrls && imageUrls.length > 0 && galleryUrls.length === 0) {
      setGalleryUrls(imageUrls);
    }
  }, [imageUrls, galleryUrls]);

  // Add loadCategories function
  const loadCategories = useCallback(async () => {
    try {
      const data = await getCategories(accountId);
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('Failed to load categories');
    }
  }, [accountId]);

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  const onThumbChange = useCallback(
    async (files: File[] | null) => {
      if (!files || files.length === 0) {
        setThumbUrls([]);
        setValue('image_url', '');
        return;
      }

      const { url, tempPath } = await uploadProductImage(files[0], accountId);
      setThumbUrls([url]);
      setValue('image_url', url);
      if (tempPath) {
        setTempImages((prev) => [...prev, { tempPath, url }]);
      }
    },
    [accountId, setValue],
  );

  const onGalleryChange = useCallback(
    async (files: File[] | null) => {
      if (!files || files.length === 0) {
        setGalleryUrls([]);
        setValue('image_urls', []);
        return;
      }

      const urls: string[] = [];
      for (const file of files) {
        const { url, tempPath } = await uploadProductImage(file, accountId);
        urls.push(url);
        if (tempPath) {
          setTempImages((prev) => [...prev, { tempPath, url }]);
        }
      }
      setGalleryUrls(urls);
      setValue('image_urls', urls);
    },
    [accountId, setValue],
  );

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen((prev) => !prev);
  }, []);

  const uploadImageHandler = useCallback(
    async (file: File) => {
      try {
        const { url, tempPath } = await uploadProductImage(file, accountId);
        if (tempPath) {
          setTempImages((prev) => [...prev, { tempPath, url }]);
        }
        return url;
      } catch (error) {
        console.error('Failed to upload image:', error);
        return null;
      }
    },
    [accountId],
  );

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full',
        },
        uploadHandler: uploadImageHandler,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Placeholder.configure({
        placeholder: 'Describe your product features and benefits in detail...',
      }),
    ],
    content: description || '',
    onUpdate: ({ editor }) => {
      const description = editor.getHTML();
      setValue('description', description, { shouldValidate: false });
    },
    editorProps: {
      handleDrop: (view, event, slice, moved) => {
        if (!moved && event.dataTransfer?.files?.length) {
          const images = Array.from(event.dataTransfer.files).filter((file) =>
            file.type.startsWith('image/'),
          );
          if (images.length > 0) {
            event.preventDefault();
            images.forEach(async (image) => {
              const url = await uploadImageHandler(image);
              if (url) {
                editor.chain().focus().setImage({ src: url }).run();
              }
            });
            return true;
          }
        }
        return false;
      },
      handlePaste: (view, event) => {
        if (event.clipboardData?.files?.length) {
          const images = Array.from(event.clipboardData.files).filter((file) =>
            file.type.startsWith('image/'),
          );
          if (images.length > 0) {
            event.preventDefault();
            images.forEach(async (image) => {
              const url = await uploadImageHandler(image);
              if (url) {
                editor.chain().focus().setImage({ src: url }).run();
              }
            });
            return true;
          }
        }
        return false;
      },
      handleDOMEvents: {
        dragover: (view, event) => {
          event.preventDefault();
          setIsDragging(true);
        },
        dragleave: () => {
          setIsDragging(false);
        },
        drop: () => {
          setIsDragging(false);
        },
      },
    },
  });

  useEffect(() => {
    return () => {
      if (tempImages.length > 0) {
        tempImages.forEach(async ({ url }) => {
          try {
            await deleteProductImage(url);
            console.log(`Deleted temp image: ${url}`);
          } catch (error) {
            console.error('Failed to delete temp image:', error);
          }
        });
        setTempImages([]);
      }
    };
  }, [tempImages]);

  const imageUploaderProps: Partial<ImageUploaderProps> = {
    accept: 'image/*',
    bucket: 'products',
    className:
      'w-full mt-2 rounded-lg border-2 border-dashed hover:border-primary/50 transition-colors',
  };

  // Add handleRemove function
  const handleRemove = useCallback(
    async (url: string, type: 'thumb' | 'gallery') => {
      try {
        // Delete the image from storage
        await deleteProductImage(url);

        // Remove from tempImages if it exists
        setTempImages((prev) => prev.filter((img) => img.url !== url));

        // Update state and form values based on type
        if (type === 'thumb') {
          setThumbUrls([]);
          setValue('image_url', '');
        } else {
          setGalleryUrls((prev) => prev.filter((u) => u !== url));
          setValue(
            'image_urls',
            galleryUrls.filter((u) => u !== url),
          );
        }
        // toast.success('Image removed successfully');
      } catch (error) {
        console.error('Failed to remove image:', error);
        toast.error('Failed to remove image');
      }
    },
    [setValue, galleryUrls],
  );

  return (
    <Card className="border-l-primary border-l-4">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="bg-muted/30 border-b">
          <CollapsibleTrigger className="hover:bg-muted/50 flex w-full items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <h3 className="text-xl font-semibold">
                <Trans i18nKey="products:basic_info:title">
                  Basic Information
                </Trans>
              </h3>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="cursor-help">
                    <Info className="text-muted-foreground h-4 w-4" />
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <Trans i18nKey="products:basic_info:tooltip">
                    Essential details about your product
                  </Trans>
                </TooltipContent>
              </Tooltip>
            </div>
            <ChevronDown
              className={`h-5 w-5 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            />
          </CollapsibleTrigger>
        </div>

        <CollapsibleContent>
          <div className="space-y-8 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
              <FormField
                control={control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      <Trans i18nKey="products:name.label">Product Name</Trans>
                    </FormLabel>
                    <FormDescription className="text-sm">
                      <Trans i18nKey="products:name.description">
                        Choose a clear and descriptive name
                      </Trans>
                    </FormDescription>
                    <FormControl>
                      <Input {...field} className="mt-2 w-full" placeholder={t('products:name:placeholder')} data-testid="product-name-input" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      <Trans i18nKey="products:type.label">Product Type</Trans>
                    </FormLabel>
                    <FormDescription className="text-sm">
                      <Trans i18nKey="products:type.description">
                        Select the type that best describes your product
                      </Trans>
                    </FormDescription>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="mt-2 w-full">
                          <SelectValue placeholder={t('products:type:placeholder')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="physical">
                          <div className="flex flex-col py-1">
                            <span className="font-medium">
                              <Trans i18nKey="products:type.physical">
                                Physical Product
                              </Trans>
                            </span>
                            <span className="text-muted-foreground mt-0.5 text-xs">
                              <Trans i18nKey="products:type.physical_description">
                                Tangible items that require shipping
                              </Trans>
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="digital">
                          <div className="flex flex-col py-1">
                            <span className="font-medium">
                              <Trans i18nKey="products:type.digital">
                                Digital Product
                              </Trans>
                            </span>
                            <span className="text-muted-foreground mt-0.5 text-xs">
                              <Trans i18nKey="products:type.digital_description">
                                Downloadable items or content
                              </Trans>
                            </span>
                          </div>
                        </SelectItem>
                        <SelectItem value="service">
                          <div className="flex flex-col py-1">
                            <span className="font-medium">
                              <Trans i18nKey="products:type.service">
                                Service
                              </Trans>
                            </span>
                            <span className="text-muted-foreground mt-0.5 text-xs">
                              <Trans i18nKey="products:type.service_description">
                                Time or expertise-based offerings
                              </Trans>
                            </span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      <Trans i18nKey="products:category.label">Category</Trans>
                    </FormLabel>
                    <FormDescription className="text-sm">
                      <Trans i18nKey="products:category.description">
                        Select a category for your product
                      </Trans>
                    </FormDescription>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="mt-2 w-full">
                          <SelectValue placeholder={t('products:category:placeholder')} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
              <FormField
                control={control}
                name="image_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      <Trans i18nKey="products:thumbnail.label">
                        Thumbnail
                      </Trans>
                    </FormLabel>
                    <FormDescription className="text-xs">
                      <Trans i18nKey="products:thumbnail.help">
                        Main product image (recommended: 400x400px)
                      </Trans>
                    </FormDescription>
                    <FormControl>
                      <ImageUploaderAdvance
                        {...imageUploaderProps}
                        value={thumbUrls}
                        onChange={onThumbChange}
                        onRemove={handleRemove}
                        maxFiles={1}
                        className={`${imageUploaderProps.className} aspect-square`}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="image_urls"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      <Trans i18nKey="products:gallery.label">
                        Gallery Images
                      </Trans>
                    </FormLabel>
                    <FormDescription className="text-xs">
                      <Trans i18nKey="products:gallery.help">
                        Additional product images (up to 5)
                      </Trans>
                    </FormDescription>
                    <FormControl>
                      <ImageUploaderAdvance
                        {...imageUploaderProps}
                        value={galleryUrls}
                        onChange={onGalleryChange}
                        onRemove={handleRemove}
                        maxFiles={5}
                        className={`${imageUploaderProps.className} aspect-[4/3]`}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormField
                control={control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <div className="mb-2 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                      <div>
                        <FormLabel className="text-base font-medium">
                          <Trans i18nKey="products:description.label">
                            Product Description
                          </Trans>
                        </FormLabel>
                        <FormDescription className="mt-1 text-sm">
                          <Trans i18nKey="products:description.help">
                            Describe your product's features and benefits in
                            detail
                          </Trans>
                        </FormDescription>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleFullscreen}
                        className="gap-2 w-full md:w-auto"
                        type="button"
                      >
                        <Maximize2 className="h-4 w-4" />
                        <span>Full Screen</span>
                      </Button>
                    </div>
                    <FormControl>
                      <div
                        className={`bg-background mt-2 min-h-[250px] rounded-lg border shadow-sm ${isDragging ? 'border-primary border-2 border-dashed' : ''}`}
                      >
                        <div className="bg-muted/30 sticky top-0 border-b">
                          <EditorToolbar
                            editor={editor}
                            onImageSelect={uploadImageHandler}
                          />
                        </div>
                        <EditorContent
                          editor={editor}
                          className="prose prose-sm max-w-none p-6 focus:outline-none"
                          style={{
                            minHeight: '250px',
                            fontSize: '16px',
                            lineHeight: '1.6',
                          }}
                        />
                        <div className="bg-muted/30 border-t px-6 py-3">
                          <div className="text-muted-foreground flex items-center gap-2 text-sm">
                            <ImageIcon className="h-4 w-4" />
                            <span>
                              Drag and drop images here to add them to your
                              description
                            </span>
                          </div>
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                    {isFullscreen && (
                      <FullscreenEditor
                        editor={editor}
                        onClose={toggleFullscreen}
                        onImageSelect={uploadImageHandler}
                      />
                    )}
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
