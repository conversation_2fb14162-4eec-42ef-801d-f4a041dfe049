'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { ChevronLeft, Loader2, Save } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Form } from '@kit/ui/form';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { ProductPreview } from '~/home/<USER>/products/_components/preview/product-preview';
import { AttributesSection } from '~/home/<USER>/products/_components/sections/attributes-section';
import { BasicInfoSection } from '~/home/<USER>/products/_components/sections/basic-info-section';
import { BranchSection } from '~/home/<USER>/products/_components/sections/branch-section';
import { InventorySection } from '~/home/<USER>/products/_components/sections/inventory-section';
import { PricingSection } from '~/home/<USER>/products/_components/sections/pricing-section';
import { updateProduct } from '~/home/<USER>/products/_lib/server/products';

import { ProductFormData, productSchema } from '../../../_lib/types';

interface EditProductFormProps {
  accountId: string;
  accountSlug: string;
  product: any; // Use the product data fetched from the server
}

export function EditProductForm({
  accountId,
  accountSlug,
  product,
}: EditProductFormProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Transform the product data to match the ProductFormData structure
  const defaultValues: ProductFormData = {
    name: product.name || '',
    description: product.description || '',
    type: product.type || 'physical',
    status: product.status || 'draft',
    category_id: product.category_id || '',
    sku: product.sku || '',
    barcode: product.barcode || '',
    price: product.price || 0,
    tax_rate: product.tax_rate || 0,
    cost_per_item: product.metadata?.cost_per_item || 0,
    weight: product.weight || 0,
    dimensions: product.dimensions || {
      length: 0,
      width: 0,
      height: 0,
    },
    track_inventory: product.track_inventory || false,
    inventory_quantity:
      product.inventory?.reduce(
        (sum: number, inv: any) => sum + (inv.stock || 0),
        0,
      ) || 0,
    selected_branches:
      product.branch_products
        ?.filter((bp: any) => bp.is_active)
        .map((bp: any) => bp.branch_id) || [],
    inventory:
      product.inventory?.map((inv: any) => ({
        branch_id: inv.branch_id,
        base_quantity: !inv.attribute_id ? inv.stock : undefined,
        attribute_combinations: inv.attribute_id
          ? [
              {
                attribute_values: {
                  [product.product_attributes.find(
                    (attr: any) => attr.id === inv.attribute_id,
                  )?.name || '']:
                    product.product_attributes.find(
                      (attr: any) => attr.id === inv.attribute_id,
                    )?.value || '',
                },
                quantity: inv.stock,
              },
            ]
          : undefined,
      })) || [],
    image_url: product.image_url || '',
    image_urls: product.image_urls || [],
    images: product.image_url
      ? [{ url: product.image_url, tempPath: undefined }]
      : [],
    attributes: product.product_attributes
      ? Object.entries(
          product.product_attributes.reduce((acc: any, attr: any) => {
            if (!acc[attr.name]) {
              acc[attr.name] = {
                name: attr.name,
                values: [],
                hasPriceModifier: attr.price_modifier !== 0,
                priceModifiers: [],
              };
            }
            acc[attr.name].values.push(attr.value);
            acc[attr.name].priceModifiers.push(attr.price_modifier || 0);
            return acc;
          }, {}),
        ).map(([_, attr]: [string, any]) => attr)
      : [],
    is_global: product.branch_products?.length === 0,
    vendor: product.metadata?.vendor || '',
    categories: product.metadata?.categories || [],
    collections: product.metadata?.collections || [],
    tags: product.metadata?.tags || [],
    seo: product.metadata?.seo || {
      title: '',
      description: '',
      keywords: '',
    },
    metadata: product.metadata || {},
  };
  console.log(defaultValues);
  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = async (formData: ProductFormData) => {
    try {
      setLoading(true);
      console.log('Form data on submit:', JSON.stringify(formData, null, 2));

      // Validate form data
      const validationResult = productSchema.safeParse(formData);
      if (!validationResult.success) {
        console.error('Validation errors:', validationResult.error);
        throw new Error('Form validation failed');
      }

      // Prepare the data according to the database structure
      const productData: ProductFormData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        status: formData.status,
        category_id: formData.category_id || null,
        price: formData.price,
        sku: formData.sku || null,
        barcode: formData.barcode || null,
        weight: formData.weight || null,
        dimensions:
          formData.dimensions &&
          Object.values(formData.dimensions).some((v) => v > 0)
            ? formData.dimensions
            : null,
        tax_rate: formData.tax_rate || null,
        image_url: formData.image_url,
        image_urls: formData.image_urls,
        track_inventory: formData.track_inventory,
        inventory_quantity: formData.inventory_quantity,
        selected_branches: formData.selected_branches,
        inventory: formData.inventory,
        attributes: formData.attributes?.flatMap((attr) =>
          attr.values.map((value, index) => ({
            name: attr.name,
            value,
            price_modifier: attr.hasPriceModifier
              ? attr.priceModifiers[index] || 0
              : 0,
          })),
        ),
        metadata: {
          vendor: formData.vendor,
          categories: formData.categories,
          collections: formData.collections,
          tags: formData.tags,
          seo: formData.seo,
          cost_per_item: formData.cost_per_item,
          ...formData.metadata,
        },
      };

      const result = await updateProduct(accountId, product.id, productData);

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success(
        <Trans i18nKey="products:update:success">
          Cập nhật sản phẩm thành công
        </Trans>,
      );

      router.push(`/home/<USER>/products`);
      router.refresh();
    } catch (error) {
      toast.error(
        <Trans i18nKey="products:update:error">
          {error.message || 'Không thể cập nhật sản phẩm'}
        </Trans>,
      );
      console.error('Lỗi khi cập nhật sản phẩm:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Header */}
          <div className="sticky top-0 z-50 border-b bg-white/80 backdrop-blur-lg">
            <div className="container flex h-16 items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="rounded-full p-2 transition-colors hover:bg-gray-100"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <h1 className="bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-sm md:text-xl font-semibold text-transparent truncate">
                  <Trans i18nKey="products:edit:title">
                    Chỉnh sửa sản phẩm
                  </Trans>
                </h1>
              </div>

              <div className="flex items-center gap-1 md:gap-3">
                <div className="hidden md:block mr-2 text-sm text-gray-500">
                  {loading ? (
                    <span className="flex items-center">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    </span>
                  ) : (
                    <Trans i18nKey="common:autoSaved">Auto saved</Trans>
                  )}
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => router.back()}
                  disabled={loading}
                  className="hover:bg-gray-100 hidden md:flex"
                >
                  <Trans i18nKey="common:cancel">Hủy</Trans>
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  size={"sm"}
                  className={cn(
                    'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-md transition-all duration-200 hover:from-indigo-600 hover:to-purple-600 hover:shadow-lg',
                    loading && 'cursor-wait opacity-80',
                  )}
                >
                  <Save className="mr-1 md:mr-2 h-4 w-4" />
                  <span className="hidden md:inline">
                    <Trans i18nKey="products:actions:save">Save product</Trans>
                  </span>
                  <span className="md:hidden">
                    <Trans i18nKey="common:save">Lưu</Trans>
                  </span>
                </Button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="container py-8">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Form Sections */}
              <div className="w-full lg:w-2/3 space-y-6">
                <div className="flex flex-col gap-6">
                  <div className="rounded-2xl bg-white p-4 md:p-6 shadow-sm ring-1 ring-gray-100 transition-shadow duration-200 hover:shadow-md">
                    <BasicInfoSection accountId={accountId} />
                  </div>

                  <div className="rounded-2xl bg-white p-4 md:p-6 shadow-sm ring-1 ring-gray-100 transition-shadow duration-200 hover:shadow-md">
                    <PricingSection />
                  </div>

                  {/* Mobile Preview - Only visible on mobile */}
                  <div className="lg:hidden rounded-2xl bg-white p-4 shadow-sm ring-1 ring-gray-100">
                    <ProductPreview form={form} />
                  </div>

                  <div className="rounded-2xl bg-white p-4 md:p-6 shadow-sm ring-1 ring-gray-100 transition-shadow duration-200 hover:shadow-md">
                    <AttributesSection accountId={accountId} />
                  </div>

                  <div className="rounded-2xl bg-white p-4 md:p-6 shadow-sm ring-1 ring-gray-100 transition-shadow duration-200 hover:shadow-md">
                    <BranchSection accountId={accountId} />
                  </div>

                  <div className="rounded-2xl bg-white p-4 md:p-6 shadow-sm ring-1 ring-gray-100 transition-shadow duration-200 hover:shadow-md">
                    <InventorySection accountId={accountId} />
                  </div>
                </div>
              </div>

              {/* Desktop Preview - Hidden on mobile */}
              <div className="hidden lg:block lg:w-1/3">
                <div className="sticky top-24">
                  <div className="rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100">
                    <ProductPreview form={form} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
