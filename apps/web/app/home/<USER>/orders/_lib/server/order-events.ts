'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { emitZnsEvent } from '@kit/zns/lib/event-bus';

/**
 * Tạo đơn hàng và phát sự kiện
 * @param params Tham số tạo đơn hàng
 * @returns Kết quả tạo đơn hàng
 */
export async function createOrderWithEvents(params: {
  account_id: string;
  customer_id: string;
  branch_id?: string | null;
  items: Array<{
    product_id: string;
    attribute_id?: string | null;
    quantity: number;
    price: number;
    original_price?: number;
    flash_sale_id?: string | null;
    discount_percentage?: number | null;
  }>;
  subtotal: number;
  discount: number;
  total_amount: number;
  payment_method: 'cash' | 'card' | 'transfer';
  status?: 'pending' | 'processing' | 'completed' | 'cancelled';
  voucher_code?: string | null;
  voucher_id?: string | null;
  voucher_discount?: number | null;
  metadata?: Record<string, any> | null;
  webhook_url?: string | null;
  team_account_id: string;
}) {
  const client = getSupabaseServerClient();
  
  // Gọi RPC để tạo đơn hàng
  const { data, error } = await client.rpc('create_order_with_inventory', {
    p_account_id: params.account_id,
    p_customer_id: params.customer_id,
    p_branch_id: params.branch_id || null,
    p_items: params.items,
    p_subtotal: params.subtotal,
    p_discount: params.discount,
    p_total_amount: params.total_amount,
    p_payment_method: params.payment_method,
    p_status: params.status || 'pending',
    p_voucher_code: params.voucher_code || null,
    p_voucher_id: params.voucher_id || null,
    p_voucher_discount: params.voucher_discount || null,
    p_metadata: params.metadata || null,
    p_webhook_url: params.webhook_url || null,
  });
  
  if (error) {
    console.error('Error creating order:', error);
    throw error;
  }
  
  // Lấy thông tin đơn hàng vừa tạo
  const { data: orderData, error: orderError } = await client
    .from('customer_orders')
    .select(`
      id,
      order_code,
      customer_id,
      customers(
        id,
        name,
        email,
        phone
      ),
      branch_id,
      branches(
        id,
        name
      ),
      customer_order_items(
        id,
        product_id,
        products(
          id,
          name
        ),
        quantity,
        price
      ),
      subtotal,
      discount_amount,
      total_amount,
      status,
      payment_method,
      created_at
    `)
    .eq('id', data.order_id)
    .single();
  
  if (orderError) {
    console.error('Error fetching order details:', orderError);
  }
  
  // Phát sự kiện đơn hàng đã tạo
  if (orderData) {
    emitZnsEvent(
      'orders',
      'created',
      orderData,
      {
        teamAccountId: params.team_account_id,
        orderId: data.order_id,
      }
    );
    
    console.log(`Order created event emitted for order ${data.order_id}`);
  }
  
  return data;
}

/**
 * Cập nhật trạng thái đơn hàng và phát sự kiện
 * @param params Tham số cập nhật trạng thái
 * @returns Kết quả cập nhật
 */
export async function updateOrderStatusWithEvents(params: {
  order_id: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  team_account_id: string;
}) {
  const client = getSupabaseServerClient();
  
  // Cập nhật trạng thái đơn hàng
  const { data, error } = await client
    .from('customer_orders')
    .update({ status: params.status })
    .eq('id', params.order_id)
    .select()
    .single();
  
  if (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
  
  // Lấy thông tin đơn hàng đã cập nhật
  const { data: orderData, error: orderError } = await client
    .from('customer_orders')
    .select(`
      id,
      order_code,
      customer_id,
      customers(
        id,
        name,
        email,
        phone
      ),
      branch_id,
      branches(
        id,
        name
      ),
      customer_order_items(
        id,
        product_id,
        products(
          id,
          name
        ),
        quantity,
        price
      ),
      subtotal,
      discount_amount,
      total_amount,
      status,
      payment_method,
      created_at,
      updated_at
    `)
    .eq('id', params.order_id)
    .single();
  
  if (orderError) {
    console.error('Error fetching updated order details:', orderError);
  }
  
  // Phát sự kiện trạng thái đơn hàng đã cập nhật
  if (orderData) {
    emitZnsEvent(
      'orders',
      'status_updated',
      orderData,
      {
        teamAccountId: params.team_account_id,
        orderId: params.order_id,
        previousStatus: data.status,
        newStatus: params.status,
      }
    );
    
    console.log(`Order status updated event emitted for order ${params.order_id}`);
  }
  
  return data;
}
