'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { OrderFormData } from '../order.schema';

interface UpdateOrderParams extends OrderFormData {
  id: string;
  accountId: string;
  accountSlug: string;
}

export async function updateOrder({
  id,
  customer_id,
  product_id,
  branch_id,
  quantity,
  total_amount,
  status,
  payment_method,
  accountId,
  accountSlug,
}: UpdateOrderParams) {
  const client = getSupabaseServerClient();

  const { error } = await client
    .from('customer_orders')
    .update({
      customer_id,
      product_id,
      branch_id,
      quantity,
      total_amount,
      status,
      payment_method,
    })
    .eq('id', id)
    .eq('account_id', accountId);

  if (error) {
    throw error;
  }

  revalidatePath(`/home/<USER>/orders`);
}
