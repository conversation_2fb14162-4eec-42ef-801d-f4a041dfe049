'use client';

import { useState } from 'react';

import { useTranslation } from 'react-i18next';
import { Trans } from '@kit/ui/trans';
import { FormattedPrice } from '../../../../../components/currency/price-display';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { formatDate } from '@kit/ui/utils';

import type { Order } from '../_lib/server/orders-page.loader';

interface OrderDetailsModalProps {
  children: React.ReactNode;
  order: Order;
}

export default function OrderDetailsModal({
  children,
  order,
}: OrderDetailsModalProps) {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle><Trans i18nKey="orders:detail.orderDetails">Order Details</Trans></DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:detail.customer">Customer</Trans>
              </div>
              <div>{order.customer_email}</div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:detail.product">Product</Trans>
              </div>
              <div>{order.product_name}</div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:detail.quantity">Quantity</Trans>
              </div>
              <div>{order.quantity}</div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:detail.totalAmount">Total Amount</Trans>
              </div>
              <div><FormattedPrice amount={order.total_amount} /></div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:fields.status">Status</Trans>
              </div>
              <div className="capitalize">{t(`orders:status.${order.status}`, { defaultValue: order.status })}</div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:fields.payment">Payment Method</Trans>
              </div>
              <div className="capitalize">{t(`orders:paymentMethods.${order.payment_method}`, { defaultValue: order.payment_method })}</div>
            </div>
            <div>
              <div className="text-muted-foreground text-sm font-medium">
                <Trans i18nKey="orders:fields.createdAt">Created At</Trans>
              </div>
              <div>{formatDate(order.created_at)}</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
