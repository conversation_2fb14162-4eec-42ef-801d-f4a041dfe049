'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function updateOrderStatus(
  orderId: string,
  accountSlug: string,
  status: 'approved' | 'canceled',
) {
  const supabase = getSupabaseServerClient();

  // Lấy account_id từ slug
  const { data: accountData, error: accountError } = await supabase
    .from('accounts')
    .select('id')
    .eq('slug', accountSlug)
    .single();

  if (accountError || !accountData) throw new Error('Account not found');

  const accountId = accountData.id;

  // Sử dụng hàm updateOrderStatusWithEvents để cập nhật trạng thái và phát sự kiện
  const { updateOrderStatusWithEvents } = await import('../../../_lib/server/order-events');
  await updateOrderStatusWithEvents({
    order_id: orderId,
    status: status === 'approved' ? 'completed' : 'cancelled',
    team_account_id: accountId,
  });

  // Revalidate đường dẫn với account slug
  revalidatePath(`/home/<USER>/orders/${orderId}`);
}
