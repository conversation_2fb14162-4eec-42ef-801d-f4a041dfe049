'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { Category } from './categories-page.loader';

export async function getCategories(accountId: string): Promise<Category[]> {
  const client = getSupabaseServerClient();

  const { data, error } = await client
    .from('categories')
    .select(
      `
      id,
      name,
      description,
      parent_id,
      parent:categories!parent_id (name),
      created_at,
      image_url
    `,
    )
    .eq('account_id', accountId)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return (data || []).map((category) => ({
    ...category,
    parent_name: category.parent?.name ?? null,
    product_count: 0, // Not needed here, set to 0
  }));
}
