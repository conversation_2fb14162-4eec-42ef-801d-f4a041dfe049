'use server';

import { randomUUID } from 'crypto';
import sharp from 'sharp';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Using the same bucket as products for now
const CATEGORIES_BUCKET = 'products';

function sanitizeFileName(fileName: string): string {
  return fileName.toLowerCase().replace(/[^a-z0-9.]/g, '-');
}

async function convertToWebp(file: File): Promise<Buffer> {
  const buffer = Buffer.from(await file.arrayBuffer());
  return sharp(buffer).webp({ quality: 80 }).toBuffer();
}

export async function uploadCategoryImage(
  file: File,
  accountId: string,
  categoryId?: string,
): Promise<{ url: string; tempPath?: string }> {
  if (!accountId) throw new Error('Account ID is required');

  const supabase = getSupabaseServerClient();
  const sanitizedName = sanitizeFileName(file.name).replace(
    /\.[^/.]+$/,
    '.webp',
  );

  // Always use a temp path first, then move it later
  const tempFolder = randomUUID();
  // Make sure the second part is the accountId (which is a UUID)
  // This is important for the storage policy
  const tempPath = `${tempFolder}/${accountId}/${randomUUID()}-${sanitizedName}`;

  console.log('Uploading to bucket:', CATEGORIES_BUCKET, 'Path:', tempPath);

  try {
    const webpBuffer = await convertToWebp(file);

    const { error } = await supabase.storage
      .from(CATEGORIES_BUCKET)
      .upload(tempPath, webpBuffer, {
        contentType: 'image/webp',
        upsert: true,
      });

    if (error) {
      console.error('Upload error:', error);
      throw error;
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from(CATEGORIES_BUCKET).getPublicUrl(tempPath);

    // Always save as temp image
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Expires after 24 hours
    const { error: dbError } = await supabase.from('temp_images').insert({
      temp_path: tempPath,
      url: publicUrl,
      expires_at: expiresAt.toISOString(),
      account_id: accountId,
    });

    if (dbError) {
      console.error('Failed to save temp image metadata:', dbError);
      await supabase.storage.from(CATEGORIES_BUCKET).remove([tempPath]);
      throw new Error('Failed to save temp image metadata');
    }

    // If we have a categoryId, move the image immediately
    if (categoryId) {
      return await moveTempCategoryImage(tempPath, categoryId, accountId);
    }

    return {
      url: publicUrl,
      tempPath: tempPath,
    };
  } catch (error) {
    console.error('Error in uploadCategoryImage:', error);
    throw error;
  }
}

export async function moveTempCategoryImage(
  tempPath: string,
  categoryId: string,
  accountId: string,
): Promise<{ url: string; tempPath?: string }> {
  const supabase = getSupabaseServerClient();
  const fileName = tempPath.split('/').pop()!;

  // Keep the path structure with accountId as the second part
  // This is critical for the storage policy to work
  const newPath = `categories/${accountId}/${categoryId}/${fileName}`;

  console.log('Moving from:', tempPath, 'to:', newPath, 'in bucket:', CATEGORIES_BUCKET);

  try {
    // Instead of moving, which might cause RLS issues, download and re-upload
    // First, download the file
    const { data: fileData, error: downloadError } = await supabase.storage
      .from(CATEGORIES_BUCKET)
      .download(tempPath);

    if (downloadError) {
      console.error('Download error:', downloadError);
      throw downloadError;
    }

    // Then upload to the new path
    const { error: uploadError } = await supabase.storage
      .from(CATEGORIES_BUCKET)
      .upload(newPath, fileData, {
        contentType: 'image/webp',
        upsert: true,
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      throw uploadError;
    }

    // Get the public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(CATEGORIES_BUCKET).getPublicUrl(newPath);

    // Mark the temp image as moved
    const { error: dbError } = await supabase
      .from('temp_images')
      .update({ is_moved: true })
      .eq('temp_path', tempPath);

    if (dbError) {
      console.error('Failed to update temp image metadata:', dbError);
    }

    // Try to delete the temp file, but don't fail if it doesn't work
    try {
      await supabase.storage.from(CATEGORIES_BUCKET).remove([tempPath]);
    } catch (deleteError) {
      console.error('Failed to delete temp file:', deleteError);
      // Continue anyway
    }

    return {
      url: publicUrl,
      tempPath: undefined
    };
  } catch (error) {
    console.error('Error in moveTempCategoryImage:', error);
    throw error;
  }
}

export async function deleteCategoryImage(url: string) {
  const supabase = getSupabaseServerClient();

  try {
    // First check if this is a temp image
    const { data: tempImage } = await supabase
      .from('temp_images')
      .select('temp_path')
      .eq('url', url)
      .single();

    if (tempImage?.temp_path) {
      // It's a temp image, delete it directly
      await supabase.from('temp_images').delete().eq('url', url);
      return supabase.storage.from(CATEGORIES_BUCKET).remove([tempImage.temp_path]);
    } else {
      // It's a permanent image, extract the path
      const urlPath = new URL(url).pathname;
      const filePath = urlPath.split('/').slice(2).join('/');
      if (!filePath) return;

      console.log('Deleting permanent image:', filePath);
      return supabase.storage.from(CATEGORIES_BUCKET).remove([filePath]);
    }
  } catch (error) {
    console.error('Error in deleteCategoryImage:', error);
    throw error;
  }
}
