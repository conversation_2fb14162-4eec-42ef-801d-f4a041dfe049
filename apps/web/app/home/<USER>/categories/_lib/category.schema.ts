import { z } from 'zod';

export const categorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  description: z.string().max(1000).optional().nullable(),
  parent_id: z.string().uuid().optional().nullable(),
  image_url: z.union([
    z.string().url(),
    z.string().length(0),
    z.null(),
    z.undefined()
  ]),
});

export type CategoryFormData = z.infer<typeof categorySchema>;
