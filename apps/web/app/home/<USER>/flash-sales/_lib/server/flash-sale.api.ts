import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { Database } from '~/lib/database.types';
import { CreateFlashSaleParams, FlashSale, FlashSaleWithProducts } from '~/lib/types/flash-sale';

export class FlashSaleApi {
  constructor(private readonly client: SupabaseClient<Database>) {}

  async getFlashSales(accountId: string): Promise<FlashSale[]> {
    const { data, error } = await this.client
      .from('flash_sales')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get flash sales');
      throw new Error('Failed to get flash sales');
    }

    return data || [];
  }

  async getFlashSaleById(id: string): Promise<FlashSaleWithProducts | null> {
    const { data, error } = await this.client
      .from('flash_sales')
      .select(
        `
        *,
        products:flash_sale_products(
          *,
          product:products(*)
        )
      `,
      )
      .eq('id', id)
      .single();

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get flash sale');
      throw new Error('Failed to get flash sale');
    }

    return data;
  }

  async getActiveFlashSales(accountId: string): Promise<FlashSale[]> {
    const now = new Date().toISOString();

    const { data, error } = await this.client
      .from('flash_sales')
      .select('*')
      .eq('account_id', accountId)
      .eq('status', 'active')
      .lte('start_time', now)
      .gte('end_time', now)
      .order('end_time', { ascending: true });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get active flash sales');
      throw new Error('Failed to get active flash sales');
    }

    return data || [];
  }

  async createFlashSale(
    accountId: string,
    params: CreateFlashSaleParams,
  ): Promise<{ id: string; success: boolean; message?: string }> {
    const { data, error } = await this.client.rpc('create_flash_sale', {
      p_account_id: accountId,
      p_name: params.name,
      p_description: params.description || null,
      p_start_time: params.start_time.toISOString(),
      p_end_time: params.end_time.toISOString(),
      p_products: params.products,
      p_status: params.status || 'draft',
    });

    if (error || !data?.success) {
      const logger = await getLogger();
      logger.error({ error, data }, 'Failed to create flash sale');
      return {
        id: '',
        success: false,
        message: error?.message || data?.message || 'Failed to create flash sale',
      };
    }

    return {
      id: data.flash_sale_id,
      success: true,
    };
  }

  async updateFlashSale(
    id: string,
    accountId: string,
    params: {
      name: string;
      description?: string;
      start_time: string;
      end_time: string;
      status: 'draft' | 'active' | 'ended' | 'cancelled';
      products: {
        product_id: string;
        discount_percentage: number;
        quantity_limit?: number;
      }[];
    },
  ): Promise<{ success: boolean; message?: string }> {
    const { data, error } = await this.client.rpc('update_flash_sale', {
      p_flash_sale_id: id,
      p_account_id: accountId,
      p_name: params.name,
      p_description: params.description || null,
      p_start_time: params.start_time,
      p_end_time: params.end_time,
      p_status: params.status,
      p_products: params.products,
    });

    if (error || !data?.success) {
      const logger = await getLogger();
      logger.error({ error, data }, 'Failed to update flash sale');
      return {
        success: false,
        message: error?.message || data?.message || 'Failed to update flash sale',
      };
    }

    return {
      success: true,
    };
  }

  async updateFlashSaleStatus(
    id: string,
    status: 'draft' | 'active' | 'ended' | 'cancelled',
  ): Promise<boolean> {
    const { error } = await this.client
      .from('flash_sales')
      .update({ status })
      .eq('id', id);

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to update flash sale status');
      throw new Error('Failed to update flash sale status');
    }

    return true;
  }

  async deleteFlashSale(
    id: string,
    accountId: string,
  ): Promise<{ success: boolean; message?: string }> {
    const { data, error } = await this.client.rpc('delete_flash_sale', {
      p_flash_sale_id: id,
      p_account_id: accountId,
    });

    if (error || !data?.success) {
      const logger = await getLogger();
      logger.error({ error, data }, 'Failed to delete flash sale');
      return {
        success: false,
        message: error?.message || data?.message || 'Failed to delete flash sale',
      };
    }

    return {
      success: true,
    };
  }

  async getProductFlashSale(productId: string, accountId: string): Promise<any> {
    const { data, error } = await this.client.rpc('get_product_flash_sale', {
      p_product_id: productId,
      p_account_id: accountId,
    });

    if (error) {
      const logger = await getLogger();
      logger.error({ error }, 'Failed to get product flash sale');
      return null;
    }

    return data;
  }
}

export function createFlashSaleApi(client: SupabaseClient<Database>): FlashSaleApi {
  return new FlashSaleApi(client);
}
