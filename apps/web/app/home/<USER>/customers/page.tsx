import { Suspense } from 'react';

import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { TeamAccountLayoutPageHeader } from '../_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import CustomerTable from './_components/customer-table';
import { loadCustomers } from './_lib/server/customers.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('customers:pageTitle');

  return {
    title,
  };
};

interface CustomersPageProps {
  params: Promise<{ account: string }>;
  searchParams?: Promise<{
    search?: string;
    page?: string;
  }>;
}

async function CustomersPage({ params, searchParams }: CustomersPageProps) {
  const { account: accountSlug } = await params;
  const resolvedSearchParams = (await searchParams) || {};
  const { t } = await createI18nServerInstance();

  const currentPage = resolvedSearchParams.page
    ? parseInt(resolvedSearchParams.page)
    : 1;
  const searchQuery = resolvedSearchParams.search || '';

  try {
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const limit = 10;
    const { data: customers, total } = await loadCustomers({
      accountId: account.id,
      search: searchQuery,
      page: currentPage,
      limit,
    });

    return (
      <>
        <TeamAccountLayoutPageHeader
          account={accountSlug}
          title={<Trans i18nKey="customers:pageTitle">Customers</Trans>}
          description={<AppBreadcrumbs />}
        />
        <PageBody data-testid="customers-page">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex gap-2">
              <Input
                placeholder={t('customers:searchPlaceholder')}
                className="w-64"
                defaultValue={searchQuery}
                data-testid="customer-search-input"
              />
              <Button data-testid="export-customers-button">
                <Trans i18nKey="customers:export">Export</Trans>
              </Button>
            </div>
          </div>
          <Suspense fallback={<div>Loading...</div>}>
            <CustomerTable
              customers={customers}
              account={{ id: account.id, slug: accountSlug }}
              currentPage={currentPage - 1}
              limit={limit}
              total={total}
              canManage={true}
              filters={{ query: searchQuery }}
            />
          </Suspense>
        </PageBody>
      </>
    );
  } catch (error) {
    console.error('Error loading customers page:', error);
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:loadingFailed">
              Failed to load data. Please try again later.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}

export default withI18n(CustomersPage);
