import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { Customer } from '~/home/<USER>/customers/_lib/types';

interface LoadCustomersOptions {
  accountId: string;
  search?: string;
  page?: number;
  limit?: number;
}

export async function loadCustomers({
  accountId,
  search,
  page = 1,
  limit = 10,
}: LoadCustomersOptions) {
  const supabase = getSupabaseServerClient();
  const offset = (page - 1) * limit;

  // Lấy danh sách user_id của các khách hàng thuộc account
  const { data: membershipData, error: membershipError } = await supabase
    .from('accounts_memberships')
    .select('user_id')
    .eq('account_id', accountId)
    .eq('account_role', 'customer');

  if (membershipError) {
    console.error('Error loading customer memberships:', membershipError);
    throw membershipError;
  }

  // Nếu không có khách hàng nào, tr<PERSON> về danh sách trống
  if (!membershipData || membershipData.length === 0) {
    return {
      data: [],
      total: 0,
    };
  }

  // L<PERSON>y danh sách user_id
  const userIds = membershipData.map((item) => item.user_id);

  // Truy vấn thông tin khách hàng từ bảng accounts
  let query = supabase
    .from('accounts')
    .select('*', { count: 'exact' })
    .eq('is_personal_account', true)
    .in('primary_owner_user_id', userIds);

  if (search) {
    const searchTerm = `%${search}%`;
    query = query.or(
      `name.ilike.${searchTerm},email.ilike.${searchTerm},phone.ilike.${searchTerm}`,
    );
  }

  const { data, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error loading customers:', error);
    throw error;
  }

  const customers = (data || []).map((record) => ({
    id: record.id,
    email: record.email || '',
    name: record.name || '',
    phone: record.phone || null,
    created_at: record.created_at || new Date().toISOString(),
    updated_at: record.updated_at || new Date().toISOString(),
    account_id: accountId,
    is_vip: record.public_data?.is_vip || false,
    total_orders: record.public_data?.total_orders || 0,
    total_spent: record.public_data?.total_spent || 0,
    status: 'active', // Giả định, có thể cần thêm logic
    avatar_url: record.picture_url || record.public_data?.avatar_url || null,
  })) as Customer[];

  return {
    data: customers,
    total: count || 0,
  };
}
