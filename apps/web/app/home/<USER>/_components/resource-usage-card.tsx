'use client';

import { formatDistanceToNow } from 'date-fns';
import { RefreshCw } from 'lucide-react';
import { useResourceUsage } from '../_lib/hooks/use-resource-access';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Progress } from '@kit/ui/progress';
import { Button } from '@kit/ui/button';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';

interface ResourceUsageCardProps {
  title?: string;
  description?: string;
  resourceTypes?: string[];
  accountId?: string;
}

/**
 * Component hiển thị thông tin sử dụng tài nguyên của tài khoản
 */
export function ResourceUsageCard({
  title = 'Resource Usage',
  description = '',
  resourceTypes,
  accountId
}: ResourceUsageCardProps) {
  const { resourceUsage, lastUpdated, isLoading, refetch } = useResourceUsage(accountId);

  // Lọc các loại tài nguyên nếu có
  const filteredResources = resourceTypes
    ? Object.entries(resourceUsage).filter(([type]) => resourceTypes.includes(type))
    : Object.entries(resourceUsage);

  // Hàm format tên tài nguyên
  const formatResourceName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  return (
    <Card data-testid="resource-usage-card">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => refetch()}
          disabled={isLoading}
          title="Refresh usage data"
          data-testid="refresh-usage-button"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          // Skeleton loading state
          Array(resourceTypes?.length || 3).fill(0).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))
        ) : filteredResources.length > 0 ? (
          // Resource usage data
          filteredResources.map(([type, { current, limit }]) => (
            <div key={type} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="font-medium">{formatResourceName(type)}</span>
                <span>
                  {current} / {limit === null ? '∞' : limit}
                </span>
              </div>
              <Progress
                value={limit === null ? 0 : (current / limit) * 100}
                className="h-2"
                indicatorClassName={
                  limit === null
                    ? 'bg-primary'
                    : current >= limit
                    ? 'bg-destructive'
                    : current >= limit * 0.8
                    ? 'bg-warning'
                    : 'bg-primary'
                }
              />
              {limit !== null && (
                <div className="flex justify-end text-xs text-muted-foreground">
                  {current >= limit ? (
                    <span className="text-destructive">
                      <Trans i18nKey="resources:limitReached" defaults="Limit reached" />
                    </span>
                  ) : current >= limit * 0.8 ? (
                    <span className="text-warning">
                      <Trans i18nKey="resources:approachingLimit" defaults="Approaching limit" />
                    </span>
                  ) : (
                    <span>
                      <Trans
                        i18nKey="resources:remaining"
                        values={{ count: limit - current }}
                        defaults="{{count}} remaining"
                      />
                    </span>
                  )}
                </div>
              )}
            </div>
          ))
        ) : (
          // No data
          <div className="text-center py-4 text-muted-foreground">
            <Trans i18nKey="resources:noData" defaults="Không có dữ liệu" />
          </div>
        )}

        {lastUpdated && (
          <div className="text-xs text-muted-foreground text-right pt-2">
            <Trans
              i18nKey="resources:lastUpdated"
              values={{ time: formatDistanceToNow(new Date(lastUpdated), { addSuffix: true }) }}
              defaults="Last updated {{time}}"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
