'use client';

import { z } from 'zod';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';
import { SidebarNavigation } from '@kit/ui/shadcn-sidebar';

import { useFilteredNavigation } from '../_lib/hooks/use-filtered-navigation';

interface AccessControlledSidebarNavigationProps {
  config: z.infer<typeof NavigationConfigSchema>;
  userId?: string;
  accountId?: string;
}

export function AccessControlledSidebarNavigation({
  config,
  userId,
  accountId,
}: AccessControlledSidebarNavigationProps) {
  // Use our custom hook to filter routes based on permissions
  const { filteredConfig, isLoading } = useFilteredNavigation(
    config,
    userId,
    accountId,
  );
  // Sử dụng filteredConfig đã được lọc dựa trên quyền

  return <SidebarNavigation config={filteredConfig} />;
}
