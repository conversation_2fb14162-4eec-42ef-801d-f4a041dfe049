'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Trans } from '@kit/ui/trans';
import { Button } from '@kit/ui/button';
import { HomeAddAccountButton } from './home-add-account-button';

interface ModernHeroSectionProps {
  userName?: string;
  onCreateTeam?: () => void;
}

export function ModernHeroSection({ userName, onCreateTeam }: ModernHeroSectionProps) {
  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-10 -top-10 h-64 w-64 rounded-full bg-blue-200/30 blur-3xl dark:bg-blue-400/10"></div>
        <div className="absolute -bottom-10 -left-10 h-64 w-64 rounded-full bg-indigo-200/30 blur-3xl dark:bg-indigo-400/10"></div>
      </div>
      
      <div className="relative grid gap-8 px-6 py-12 md:grid-cols-2 md:gap-12 md:px-10 md:py-16">
        {/* Text content */}
        <motion.div 
          className="flex flex-col justify-center space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl md:text-5xl">
            <Trans 
              i18nKey="home:welcome.titleWithName" 
              values={{ name: userName }}
              defaults="Welcome to MinApp, {{name}}!"
            />
          </h1>
          
          <p className="text-lg text-gray-600 dark:text-gray-300">
            <Trans i18nKey="home:welcome.description">
              Get started by creating your first business to manage your products, orders, and customers
            </Trans>
          </p>
          
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <div data-tour="create-team" data-testid="create-team-button-container">
              <HomeAddAccountButton 
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transition-all hover:shadow-blue-500/25 sm:w-auto"
                onSuccess={onCreateTeam}
              />
            </div>
            
            <Button variant="outline" className="border-2 transition-all hover:bg-gray-100 dark:hover:bg-gray-800">
              <Trans i18nKey="home:learnMore">Learn More</Trans>
            </Button>
          </div>
          
          <p className="text-sm text-gray-500 dark:text-gray-400">
            <Trans i18nKey="home:welcome.teamBenefits">
              Creating a business allows you to manage products, orders, and invite team members
            </Trans>
          </p>
        </motion.div>
        
        {/* Illustration */}
        <motion.div 
          className="flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="relative h-64 w-full max-w-md md:h-80">
            <svg
              className="absolute inset-0 h-full w-full"
              viewBox="0 0 100 100"
              preserveAspectRatio="xMidYMid meet"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M50 90C72.0914 90 90 72.0914 90 50C90 27.9086 72.0914 10 50 10C27.9086 10 10 27.9086 10 50C10 72.0914 27.9086 90 50 90Z"
                fill="url(#paint0_linear)"
                fillOpacity="0.1"
              />
              <path
                d="M83 50C83 67.6731 68.6731 82 51 82C33.3269 82 19 67.6731 19 50C19 32.3269 33.3269 18 51 18C68.6731 18 83 32.3269 83 50Z"
                stroke="url(#paint1_linear)"
                strokeWidth="0.5"
              />
              <path
                d="M75 50C75 63.2548 64.2548 74 51 74C37.7452 74 27 63.2548 27 50C27 36.7452 37.7452 26 51 26C64.2548 26 75 36.7452 75 50Z"
                stroke="url(#paint2_linear)"
                strokeWidth="0.5"
              />
              <defs>
                <linearGradient
                  id="paint0_linear"
                  x1="10"
                  y1="10"
                  x2="90"
                  y2="90"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#4F46E5" />
                  <stop offset="1" stopColor="#3B82F6" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear"
                  x1="19"
                  y1="18"
                  x2="83"
                  y2="82"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#4F46E5" />
                  <stop offset="1" stopColor="#3B82F6" />
                </linearGradient>
                <linearGradient
                  id="paint2_linear"
                  x1="27"
                  y1="26"
                  x2="75"
                  y2="74"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stopColor="#4F46E5" />
                  <stop offset="1" stopColor="#3B82F6" />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Business icons */}
            <motion.div
              className="absolute left-1/4 top-1/4 h-12 w-12 rounded-lg bg-white p-3 shadow-lg dark:bg-gray-800"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 3h18v18H3zM12 8v8M8 12h8"/>
              </svg>
            </motion.div>
            
            <motion.div
              className="absolute bottom-1/4 right-1/4 h-12 w-12 rounded-lg bg-white p-3 shadow-lg dark:bg-gray-800"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.5 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-indigo-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </motion.div>
            
            <motion.div
              className="absolute left-1/2 top-1/2 h-16 w-16 -translate-x-1/2 -translate-y-1/2 transform rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 p-4 shadow-lg"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="7" width="20" height="14" rx="2" ry="2"/>
                <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
              </svg>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
