'use client';

import { DollarSign, Package, TrendingUp, Users, Activity, BarChart3 } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';
import { Trend } from '@kit/ui/trend';

import { FormattedPrice } from '~/components/currency/price-display';

interface StatsCardsProps {
  mrr: number;
  revenue: number;
  newCustomers: number;
  usagePercent: number;
}

// Helper function to get icon variant class names
function getIconVariantClass(variant: 'primary' | 'success' | 'warning' | 'danger' | 'info') {
  const baseClasses = "rounded-full p-2 w-10 h-10 flex items-center justify-center";

  const variantClasses = {
    primary: "bg-primary/10 text-primary",
    success: "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
    warning: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400",
    danger: "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400",
    info: "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
  };

  return `${baseClasses} ${variantClasses[variant]}`;
}

export function StatsCards({
  mrr,
  revenue,
  newCustomers,
  usagePercent,
}: StatsCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="dashboard:mrr">MRR</Trans>
          </CardTitle>
          <div className={getIconVariantClass("success")}>
            <DollarSign className="h-5 w-5" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <div className="text-3xl font-bold tracking-tight">
              <FormattedPrice amount={mrr} size="xl" />
            </div>
            <Trend className="ml-2 font-medium" trend="up">
              20%
            </Trend>
          </div>
          <div className="mt-2 flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              <Trans i18nKey="dashboard:mrrDescription">
                Doanh thu hàng tháng
              </Trans>
            </p>
            <div className="h-1 w-16 rounded-full bg-gray-100 dark:bg-gray-800 overflow-hidden">
              <div className="h-full w-4/5 rounded-full bg-green-500"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="dashboard:revenue">Doanh thu</Trans>
          </CardTitle>
          <div className={getIconVariantClass("info")}>
            <BarChart3 className="h-5 w-5" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <div className="text-3xl font-bold tracking-tight">
              <FormattedPrice amount={revenue} size="xl" />
            </div>
            <Trend className="ml-2 font-medium" trend="up">
              15%
            </Trend>
          </div>
          <div className="mt-2 flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              <Trans i18nKey="dashboard:revenueDescription">
                Tổng doanh thu tháng này
              </Trans>
            </p>
            <div className="h-1 w-16 rounded-full bg-gray-100 dark:bg-gray-800 overflow-hidden">
              <div className="h-full w-3/4 rounded-full bg-blue-500"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="dashboard:newCustomers">Khách hàng mới</Trans>
          </CardTitle>
          <div className={getIconVariantClass("primary")}>
            <Users className="h-5 w-5" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <div className="text-3xl font-bold tracking-tight">{newCustomers}</div>
            <Trend className="ml-2 font-medium" trend="down">
              5%
            </Trend>
          </div>
          <div className="mt-2 flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              <Trans i18nKey="dashboard:newCustomersDescription">
                Khách hàng đăng ký mới tháng này
              </Trans>
            </p>
            <div className="h-1 w-16 rounded-full bg-gray-100 dark:bg-gray-800 overflow-hidden">
              <div className="h-full w-1/2 rounded-full bg-primary"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <Trans i18nKey="dashboard:usagePercent">Lưu lượng sử dụng</Trans>
          </CardTitle>
          <div className={getIconVariantClass(usagePercent > 80 ? "warning" : "success")}>
            <Activity className="h-5 w-5" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center">
            <div className="text-3xl font-bold tracking-tight">{usagePercent}%</div>
            {usagePercent > 80 ? (
              <Trend className="ml-2 font-medium" trend="up">
                Cao
              </Trend>
            ) : (
              <Trend className="ml-2 font-medium" trend="neutral">
                Bình thường
              </Trend>
            )}
          </div>
          <div className="mt-2 flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              <Trans i18nKey="dashboard:usageDescription">
                Lưu lượng sử dụng của gói hiện tại
              </Trans>
            </p>
            <div className="h-1 w-16 rounded-full bg-gray-100 dark:bg-gray-800 overflow-hidden">
              <div
                className={`h-full rounded-full ${usagePercent > 80 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${usagePercent}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
