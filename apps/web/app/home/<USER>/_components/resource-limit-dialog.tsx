'use client';

import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  AlertCircle,
  AlertTriangle,
  Ban,
  CreditCard,
  X,
  ArrowRight,
  CheckCircle,
  Sparkles,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';
import { Badge } from '@kit/ui/badge';

interface ResourceLimitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  reason: string;
  resourceType: string;
  accountSlug: string;
  current?: number;
  limit?: number;
}

export function ResourceLimitDialog({
  isOpen,
  onClose,
  reason,
  resourceType,
  accountSlug,
  current,
  limit,
}: ResourceLimitDialogProps) {
  const router = useRouter();
  const { t, i18n } = useTranslation('resources');
  const isVietnamese = i18n.language === 'vi';

  // Determine icon based on reason
  const Icon =
    reason === 'limit_reached'
      ? AlertCircle
      : reason === 'no_active_subscription'
      ? Sparkles
      : Ban;

  // Determine color variant based on reason
  const variant =
    reason === 'limit_reached'
      ? 'destructive'
      : reason === 'no_active_subscription'
      ? 'warning'
      : 'default';

  // Determine badge color based on reason
  const badgeVariant =
    reason === 'limit_reached'
      ? 'destructive'
      : reason === 'no_active_subscription'
      ? 'warning'
      : 'secondary';

  // Get title based on reason
  const getTitle = () => {
    if (reason === 'limit_reached') {
      return t('limit_reached.title');
    } else if (reason === 'no_active_subscription') {
      return t('no_active_subscription.title');
    } else {
      return t('no_permission.title');
    }
  };

  // Get description based on reason
  const getDescription = () => {
    if (reason === 'limit_reached') {
      return t('limit_reached.description', {
        resourceType: t(`${resourceType}`),
        current,
        max: limit
      });
    } else if (reason === 'no_active_subscription') {
      return t('no_active_subscription.description', {
        resourceType: t(`${resourceType}`)
      });
    } else {
      return t('no_permission.description', {
        resourceType: t(`${resourceType}`)
      });
    }
  };

  // Get button text based on reason
  const getButtonText = () => {
    if (reason === 'limit_reached') {
      return t('upgradeNow');
    } else if (reason === 'no_active_subscription') {
      return t('subscribeNow');
    } else {
      return t('subscribeNow');
    }
  };

  // Get benefits based on resource type
  const getBenefits = () => {
    if (resourceType === 'products') {
      return [
        t('benefits.unlimited_products'),
        t('benefits.zns_integration'),
        t('benefits.technical_support'),
        t('benefits.more_features')
      ];
    } else if (resourceType === 'miniapp') {
      return [
        t('benefits.multiple_miniapps'),
        t('benefits.customize_miniapps'),
        t('benefits.reach_customers'),
        t('benefits.more_features')
      ];
    } else if (resourceType === 'branches') {
      return [
        t('benefits.unlimited_branches'),
        t('benefits.technical_support'),
        t('benefits.more_features')
      ];
    } else {
      return [
        t('benefits.unlimited_products'),
        t('benefits.zns_integration'),
        t('benefits.technical_support'),
        t('benefits.more_features')
      ];
    }
  };

  const title = getTitle();
  const description = getDescription();
  const buttonText = getButtonText();
  const benefits = getBenefits();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className={`p-6 ${reason === 'limit_reached' ? 'bg-gradient-to-r from-red-50 to-red-100' : reason === 'no_active_subscription' ? 'bg-gradient-to-r from-amber-50 to-amber-100' : 'bg-gradient-to-r from-gray-50 to-gray-100'}`}>
          <DialogHeader className="p-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge
                variant={badgeVariant}
                className={`h-6 px-2 py-1 ${reason === 'limit_reached' ? 'bg-red-100 text-red-700 hover:bg-red-100' : reason === 'no_active_subscription' ? 'bg-amber-100 text-amber-700 hover:bg-amber-100' : 'bg-gray-200 text-gray-700 hover:bg-gray-200'}`}
                data-testid={reason === 'limit_reached' ? 'limit-reached-dialog' : reason === 'no_active_subscription' ? 'subscription-required-dialog' : 'access-denied-dialog'}
              >
                <Icon className="mr-1 h-3.5 w-3.5" />
                <Trans i18nKey={reason === 'limit_reached' ? 'resources:limitReached' :
                          reason === 'no_active_subscription' ? 'resources:subscriptionRequired' :
                          'resources:access_denied.title'} />
              </Badge>
              <DialogTitle className="text-xl font-bold">{title}</DialogTitle>
            </div>
            <DialogDescription className="mt-2 text-base">{description}</DialogDescription>
          </DialogHeader>

          {/* Progress bar for limit reached */}
          {reason === 'limit_reached' && current !== undefined && limit !== undefined && (
            <div className="mt-4">
              <div className="mb-1 flex justify-between text-sm font-medium">
                <span><Trans i18nKey="resources:used">Used</Trans>: {current}/{limit}</span>
                <span className={current === limit ? 'text-red-600 font-bold' : ''}>{Math.round((current / limit) * 100)}%</span>
              </div>
              <div className="relative h-3 w-full overflow-hidden rounded-full bg-gray-200">
                <div
                  className={`h-full w-full flex-1 transition-all ${current === limit ? 'bg-red-500' : 'bg-blue-500'}`}
                  style={{ transform: `translateX(-${100 - Math.round((current / limit) * 100)}%)` }}
                />
              </div>
            </div>
          )}
        </div>

        <div className="p-6">
          {/* Benefits section */}
          {benefits.length > 0 && (
            <div className="mb-6 rounded-lg border border-dashed border-gray-300 bg-white p-4" data-testid="subscription-benefits">
              <h4 className="mb-3 font-medium flex items-center">
                <Sparkles className="mr-2 h-4 w-4 text-amber-500" />
                {t('benefits.title')}
              </h4>
              <ul className="space-y-3">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="mr-2 h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <DialogFooter className="flex flex-col sm:flex-row sm:justify-between sm:space-x-2 p-0">
            <Button variant="outline" onClick={onClose} className="mb-2 sm:mb-0">
              <X className="mr-2 h-4 w-4" />
              <Trans i18nKey="resources:goBack">Go Back</Trans>
            </Button>

            {
              <Button
                variant="default"
                data-testid="upgrade-plan-button"
                className={`${reason === 'limit_reached' ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700' : reason === 'no_active_subscription' ? 'bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600' : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'} text-white shadow-md hover:shadow-lg transition-all duration-200`}
                onClick={() => {
                  try {
                    // Close dialog first to prevent UI issues
                    onClose();
                    // Use window.location for more reliable navigation
                    window.location.href = `/home/<USER>/billing`;
                  } catch (error) {
                    console.error('Error navigating to billing page:', error);
                  }
                }}
              >
                {reason === 'no_active_subscription' ? (
                  <Sparkles className="mr-2 h-4 w-4" />
                ) : (
                  <CreditCard className="mr-2 h-4 w-4" />
                )}
                {buttonText}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            }
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
