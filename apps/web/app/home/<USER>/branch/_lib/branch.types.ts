export interface GpsCoordinates {
  lat: number;
  lng: number;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string;
  location?: string;
  gps_coordinates?: GpsCoordinates | null;
  is_active: boolean;
  orders: number;
  revenues: number;
}

export interface BranchPageProps {
  params: {
    account: string;
  };
  searchParams?: {
    search?: string;
    filter?: string;
  };
}
