'use client';

import { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON> } from 'recharts';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Skeleton } from '@kit/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import {
  getDailyOrders,
  getDailyPageviews,
  getDailyProductEvents,
  getDashboardOverview,
} from '../_lib/server/analytics';

export default function DashboardPage() {
  const { account } = useParams();
  const { t } = useTranslation(['dashboard', 'common']);
  const [loading, setLoading] = useState(true);
  const [overview, setOverview] = useState({
    total_visitors: 0,
    total_pageviews: 0,
    total_orders: 0,
    total_revenue: 0,
    conversion_rate: 0,
  });
  const [pageviews, setPageviews] = useState([]);
  const [productEvents, setProductEvents] = useState([]);
  const [orders, setOrders] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        // Lấy dữ liệu tổng quan
        const overviewData = await getDashboardOverview(account);
        setOverview(overviewData);

        // Lấy dữ liệu lượt xem trang
        const pageviewsData = await getDailyPageviews(account);
        setPageviews(pageviewsData);

        // Lấy dữ liệu sự kiện sản phẩm
        const productEventsData = await getDailyProductEvents(account);
        setProductEvents(productEventsData);

        // Lấy dữ liệu đơn hàng
        const ordersData = await getDailyOrders(account);
        setOrders(ordersData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [account]);

  // Chuẩn bị dữ liệu cho biểu đồ lượt xem trang
  const pageviewsChartData = {
    labels: pageviews.map((item) => item.date),
    datasets: [
      {
        label: t('dashboard:pageviews'),
        data: pageviews.map((item) => item.pageviews_count),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
      },
      {
        label: t('dashboard:visitors'),
        data: pageviews.map((item) => item.visitors_count),
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgb(16, 185, 129)',
      },
    ],
  };

  // Chuẩn bị dữ liệu cho biểu đồ thiết bị
  const deviceChartData = {
    labels: [
      t('dashboard:desktop'),
      t('dashboard:mobile'),
      t('dashboard:tablet'),
    ],
    datasets: [
      {
        data: [
          pageviews.reduce((sum, item) => sum + item.desktop_visitors, 0),
          pageviews.reduce((sum, item) => sum + item.mobile_visitors, 0),
          pageviews.reduce((sum, item) => sum + item.tablet_visitors, 0),
        ],
        backgroundColor: [
          'rgba(59, 130, 246, 0.5)',
          'rgba(16, 185, 129, 0.5)',
          'rgba(249, 115, 22, 0.5)',
        ],
        borderColor: [
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(249, 115, 22)',
        ],
      },
    ],
  };

  // Chuẩn bị dữ liệu cho biểu đồ đơn hàng
  const ordersChartData = {
    labels: orders.map((item) => item.date),
    datasets: [
      {
        label: t('dashboard:orders'),
        data: orders.map((item) => item.orders_count),
        backgroundColor: 'rgba(249, 115, 22, 0.5)',
        borderColor: 'rgb(249, 115, 22)',
      },
      {
        label: t('dashboard:revenue'),
        data: orders.map((item) => item.revenue),
        backgroundColor: 'rgba(139, 92, 246, 0.5)',
        borderColor: 'rgb(139, 92, 246)',
        yAxisID: 'y1',
      },
    ],
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account}
        title={t('dashboard:title')}
        description={
          <AppBreadcrumbs values={{ dashboard: t('dashboard:title') }} />
        }
      />
      <PageBody>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">
              {t('dashboard:overview')}
            </TabsTrigger>
            <TabsTrigger value="visitors">
              {t('dashboard:visitors')}
            </TabsTrigger>
            <TabsTrigger value="orders">{t('dashboard:orders')}</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Thống kê tổng quan */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('dashboard:totalVisitors')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-8 w-full" />
                  ) : (
                    <div className="text-2xl font-bold">
                      {overview.total_visitors}
                    </div>
                  )}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('dashboard:totalPageviews')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-8 w-full" />
                  ) : (
                    <div className="text-2xl font-bold">
                      {overview.total_pageviews}
                    </div>
                  )}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('dashboard:totalOrders')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-8 w-full" />
                  ) : (
                    <div className="text-2xl font-bold">
                      {overview.total_orders}
                    </div>
                  )}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('dashboard:totalRevenue')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-8 w-full" />
                  ) : (
                    <div className="text-2xl font-bold">
                      {new Intl.NumberFormat('vi-VN', {
                        style: 'currency',
                        currency: 'VND',
                      }).format(overview.total_revenue)}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div className="mt-4 grid gap-4 md:grid-cols-2">
              {/* Biểu đồ lượt xem trang */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('dashboard:pageviewsChart')}</CardTitle>
                  <CardDescription>
                    {t('dashboard:pageviewsChartDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-80 w-full" />
                  ) : (
                    <LineChart data={pageviewsChartData} />
                  )}
                </CardContent>
              </Card>

              {/* Biểu đồ thiết bị */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('dashboard:deviceChart')}</CardTitle>
                  <CardDescription>
                    {t('dashboard:deviceChartDescription')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-80 w-full" />
                  ) : (
                    <PieChart data={deviceChartData} />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="visitors">
            <Card>
              <CardHeader>
                <CardTitle>{t('dashboard:visitorsChart')}</CardTitle>
                <CardDescription>
                  {t('dashboard:visitorsChartDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <Skeleton className="h-80 w-full" />
                ) : (
                  <LineChart data={pageviewsChartData} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders">
            <Card>
              <CardHeader>
                <CardTitle>{t('dashboard:ordersChart')}</CardTitle>
                <CardDescription>
                  {t('dashboard:ordersChartDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <Skeleton className="h-80 w-full" />
                ) : (
                  <BarChart data={ordersChartData} />
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </PageBody>
    </>
  );
}
