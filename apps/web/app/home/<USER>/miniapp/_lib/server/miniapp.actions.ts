'use server';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { checkCanCreateResource } from '~/home/<USER>/_lib/server/resource-access';
import { incrementMiniAppCounter } from './resource-access';
import type { MiniAppFormData } from '../miniapp.schema';

export async function createMiniApp(
  data: MiniAppFormData & { accountId: string },
) {
  const supabase = getSupabaseServerClient();
  const logger = await getLogger();

  try {
    // Kiểm tra quyền tạo mini app
    const { canCreate, reason } = await checkCanCreateResource(data.accountId, 'miniapp');

    if (!canCreate) {
      logger.info(
        { accountId: data.accountId, reason },
        'Cannot create mini app due to resource limits'
      );
      return { success: false, reason };
    }

    // Tạo mini app mới
    const { error } = await supabase.from('account_themes').insert({
      name: data.name,
      mini_app_id: data.mini_app_id,
      zalo_oa_id: data.oa_id,
      account_id: data.accountId,
      status: data.status,
      template_id: data.templateId,
      config: {}, // Default empty config
      version: '1.0.0',
    });

    if (error) {
      logger.error(
        { error, accountId: data.accountId },
        'Error creating mini app'
      );
      return { success: false, error: error.message };
    }

    // Tăng counter mini app
    await incrementMiniAppCounter(data.accountId);

    revalidatePath(`/home/<USER>/miniapp`);
    return { success: true };
  } catch (error) {
    logger.error(
      { error, accountId: data.accountId },
      'Error in createMiniApp'
    );
    return { success: false, error: 'unknown_error' };
  }
}

export async function updateMiniAppStatus(
  id: string,
  accountId: string,
  status: 'draft' | 'published' | 'archived',
) {
  const supabase = getSupabaseServerClient();

  const { error } = await supabase
    .from('account_themes')
    .update({ status })
    .eq('id', id)
    .eq('account_id', accountId);

  if (error) throw error;

  revalidatePath(`/home/<USER>/miniapp`);
}
