'use client';

import { useCallback } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { publishTheme } from '~/home/<USER>/miniapp/setup/_lib/server/publish.actions';

interface PreviewActionsProps {
  account: string;
  themeId: string;
}

export function PreviewActions({ account, themeId }: PreviewActionsProps) {
  const router = useRouter();

  const handlePublish = useCallback(
    async (formData: FormData) => {
      try {
        await publishTheme(themeId, account);
        toast.success('MiniApp published successfully');
        router.push(`/home/<USER>/miniapp`);
      } catch (error) {
        toast.error('Failed to publish MiniApp');
      }
    },
    [account, themeId, router],
  );

  const handleBack = useCallback(() => {
    router.push(
      `/home/<USER>/miniapp/setup/customization?editThemeId=${themeId}`,
    );
  }, [account, themeId, router]);

  return (
    <div className="flex justify-between">
      <Button variant="outline" onClick={handleBack}>
        <Trans i18nKey="common:back">Back</Trans>
      </Button>

      <form action={handlePublish}>
        <Button type="submit">
          <Trans i18nKey="miniapp:setup:publish">Publish MiniApp</Trans>
        </Button>
      </form>
    </div>
  );
}
