'use client';

import { useEffect, useState } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { format, formatDistanceToNow } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';

import { isUUID } from '@kit/shared';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

interface SyncLog {
  id: string;
  integration_id: string;
  resource_type: string;
  status: string;
  items_processed: number;
  items_created: number;
  items_updated: number;
  items_failed: number;
  error_message: string | null;
  started_at: string;
  completed_at: string | null;
  created_by: string;
  metadata: Record<string, any> | null;
}

interface SyncItem {
  id: string;
  sync_log_id: string;
  external_id: string;
  internal_id: string | null;
  resource_type: string;
  status: string;
  error_message: string | null;
  raw_data: Record<string, any> | null;
  processed_data: Record<string, any> | null;
  created_at: string;
}

export default function IPOSSyncHistoryDetailPage() {
  const { account, id } = useParams();
  const router = useRouter();
  const { t, i18n } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [syncLog, setSyncLog] = useState<SyncLog | null>(null);
  const [syncItems, setSyncItems] = useState<SyncItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'success' | 'failed'>(
    'overview',
  );
  const [successItems, setSuccessItems] = useState<SyncItem[]>([]);
  const [failedItems, setFailedItems] = useState<SyncItem[]>([]);
  const [integrationId, setIntegrationId] = useState<string | null>(null);

  // Lấy thông tin sync log và items
  useEffect(() => {
    const fetchSyncLogDetails = async () => {
      setIsLoading(true);
      try {
        // Chuyển đổi account slug thành account ID
        let accountId = account;

        if (!isUUID(account)) {
          // Nếu là slug, lấy UUID tương ứng
          const { data: accountData, error: accountError } = await supabase
            .from('accounts')
            .select('id')
            .eq('slug', account)
            .single();

          if (accountError) {
            console.error('Error fetching account by slug:', accountError);
            setIsLoading(false);
            return;
          }

          accountId = accountData.id;
        }

        // Lấy integration ID
        // Thử với cột 'type' trước
        let { data: integration, error: integrationError } = await supabase
          .from('integrations')
          .select('id')
          .eq('account_id', accountId)
          .eq('type', 'ipos')
          .eq('status', 'connected')
          .single();

        // Nếu không tìm thấy, thử với cột 'platform'
        if (integrationError && integrationError.code === 'PGRST116') {
          const { data: integrationByPlatform, error: platformError } =
            await supabase
              .from('integrations')
              .select('id')
              .eq('account_id', accountId)
              .eq('platform', 'ipos')
              .eq('status', 'connected')
              .single();

          if (!platformError) {
            integration = integrationByPlatform;
            integrationError = null;
          }
        }

        if (integrationError) {
          // Check if it's a "not found" error
          if (integrationError.code === 'PGRST116') {
            console.log('No active iPOS integration found for this account');
            setIsLoading(false);
            return;
          }

          console.error('Error fetching integration:', integrationError);
          setIsLoading(false);
          return;
        }

        if (!integration) {
          console.log('No iPOS integration found for this account');
          setIsLoading(false);
          return;
        }

        setIntegrationId(integration.id);

        // Lấy thông tin sync log
        const { data: logData, error: logError } = await supabase
          .from('integration_sync_logs')
          .select('*')
          .eq('id', id)
          .single();

        if (logError) {
          if (logError.code === 'PGRST116') {
            console.log(`No sync log found with ID: ${id}`);
          } else {
            console.error('Error fetching sync log:', logError);
          }
          setIsLoading(false);
          return;
        }

        if (!logData) {
          console.error('No sync log found with ID:', id);
          setIsLoading(false);
          return;
        }

        setSyncLog(logData);

        // Lấy danh sách sync items
        const { data: itemsData, error: itemsError } = await supabase
          .from('integration_sync_items')
          .select('*')
          .eq('sync_log_id', id)
          .order('created_at', { ascending: false });

        if (itemsError) {
          console.error('Error fetching sync items:', itemsError);
          setSyncItems([]);
          setSuccessItems([]);
          setFailedItems([]);
          setIsLoading(false);
          return;
        }

        setSyncItems(itemsData || []);

        // Phân loại items
        const success =
          itemsData?.filter((item) => item.status === 'success') || [];
        const failed =
          itemsData?.filter((item) => item.status === 'error') || [];

        setSuccessItems(success);
        setFailedItems(failed);
      } catch (error) {
        console.error('Error in fetchSyncLogDetails:', error);
        setSyncLog(null);
        setSyncItems([]);
        setSuccessItems([]);
        setFailedItems([]);

        // Show toast notification for unexpected errors
        if (error instanceof Error) {
          toast.error(
            t('integrations:ipos.syncHistoryDetail.errorFetchingDetails'),
            {
              description: error.message,
            },
          );
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchSyncLogDetails();
    }
  }, [id, supabase]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp', {
        locale: i18n.language === 'vi' ? vi : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'vi' ? vi : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    const getTransContent = () => {
      switch (status) {
        case 'success':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.success">
              Success
            </Trans>
          );
        case 'error':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.error">Error</Trans>
          );
        case 'partial':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.partial">
              Partial
            </Trans>
          );
        case 'in_progress':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.inProgress">
              In Progress
            </Trans>
          );
        default:
          return status;
      }
    };

    switch (status) {
      case 'success':
        return (
          <Badge
            variant="outline"
            className="border-green-200 bg-green-50 text-green-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'error':
        return (
          <Badge
            variant="outline"
            className="border-red-200 bg-red-50 text-red-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'partial':
        return (
          <Badge
            variant="outline"
            className="border-amber-200 bg-amber-50 text-amber-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-700"
          >
            {getTransContent()}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Render resource type badge
  const renderResourceTypeBadge = (resourceType: string) => {
    const getTransContent = () => {
      switch (resourceType) {
        case 'products':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.products">
              Products
            </Trans>
          );
        case 'orders':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.orders">Orders</Trans>
          );
        case 'customers':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.customers">
              Customers
            </Trans>
          );
        default:
          return resourceType;
      }
    };

    switch (resourceType) {
      case 'products':
        return (
          <Badge
            variant="outline"
            className="border-purple-200 bg-purple-50 text-purple-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'orders':
        return (
          <Badge
            variant="outline"
            className="border-indigo-200 bg-indigo-50 text-indigo-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'customers':
        return (
          <Badge
            variant="outline"
            className="border-cyan-200 bg-cyan-50 text-cyan-700"
          >
            {getTransContent()}
          </Badge>
        );
      default:
        return <Badge variant="outline">{resourceType}</Badge>;
    }
  };

  // Render JSON data
  const renderJsonData = (data: Record<string, any> | null) => {
    if (!data) return null;

    return (
      <pre className="bg-muted max-h-60 overflow-auto rounded-md p-4 text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    );
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:ipos.syncHistoryDetail.title">
            Sync Details
          </Trans>
        }
        description={<AppBreadcrumbs />}
        account={account as string}
      />

      <PageBody>
        {isLoading ? (
          <div className="flex h-40 items-center justify-center">
            <div className="text-center">
              <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
              <p className="text-muted-foreground mt-2 text-sm">
                <Trans i18nKey="integrations:ipos.syncHistoryDetail.loading">
                  Loading sync details...
                </Trans>
              </p>
            </div>
          </div>
        ) : !integrationId ? (
          <Card>
            <CardContent className="flex h-40 items-center justify-center">
              <div className="text-center">
                <p className="text-muted-foreground">
                  <Trans i18nKey="integrations:ipos.syncHistory.noIntegration">
                    No active iPOS integration found for this account
                  </Trans>
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    router.push(`/home/<USER>/integrations`);
                  }}
                  data-testid="setup-integration-button"
                >
                  <Trans i18nKey="integrations:ipos.syncHistory.setupIntegration">
                    Set Up Integration
                  </Trans>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : !syncLog ? (
          <Card>
            <CardContent className="flex h-40 items-center justify-center">
              <div className="text-center">
                <p className="text-muted-foreground">
                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.notFound">
                    Sync log not found
                  </Trans>
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() =>
                    router.push(
                      `/home/<USER>/integrations/ipos/sync-history`,
                    )
                  }
                  data-testid="back-to-history-button"
                >
                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.backToHistory">
                    Back to Sync History
                  </Trans>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Overview Card */}
            <Card>
              <CardHeader>
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.syncDetails">
                        Sync Details
                      </Trans>
                      {renderStatusBadge(syncLog.status)}
                    </CardTitle>
                    <CardDescription>
                      {renderResourceTypeBadge(syncLog.resource_type)}
                      <span className="ml-2">
                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.startedAt">
                          Started {formatRelativeTime(syncLog.started_at)}
                        </Trans>
                      </span>
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/ipos/sync-history`,
                      )
                    }
                    data-testid="back-button"
                  >
                    <Trans i18nKey="integrations:ipos.syncHistoryDetail.backToHistory">
                      Back to Sync History
                    </Trans>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="rounded-lg border p-4">
                    <div className="text-muted-foreground text-sm font-medium">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.processed">
                        Processed
                      </Trans>
                    </div>
                    <div className="mt-1 text-2xl font-bold">
                      {syncLog.items_processed}
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-muted-foreground text-sm font-medium">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.created">
                        Created
                      </Trans>
                    </div>
                    <div className="mt-1 text-2xl font-bold text-green-600">
                      {syncLog.items_created}
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-muted-foreground text-sm font-medium">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.updated">
                        Updated
                      </Trans>
                    </div>
                    <div className="mt-1 text-2xl font-bold text-blue-600">
                      {syncLog.items_updated}
                    </div>
                  </div>
                  <div className="rounded-lg border p-4">
                    <div className="text-muted-foreground text-sm font-medium">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.failed">
                        Failed
                      </Trans>
                    </div>
                    <div className="mt-1 text-2xl font-bold text-red-600">
                      {syncLog.items_failed}
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.startTime">
                          Start Time
                        </Trans>
                      </h3>
                      <p>{formatDate(syncLog.started_at)}</p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.endTime">
                          End Time
                        </Trans>
                      </h3>
                      <p>
                        {syncLog.completed_at
                          ? formatDate(syncLog.completed_at)
                          : t('integrations:ipos.syncHistoryDetail.inProgress')}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.duration">
                          Duration
                        </Trans>
                      </h3>
                      <p>
                        {syncLog.completed_at
                          ? formatDistanceToNow(new Date(syncLog.started_at), {
                              end: new Date(syncLog.completed_at),
                              locale: i18n.language === 'vi' ? vi : enUS,
                            })
                          : t('integrations:ipos.syncHistoryDetail.inProgress')}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-muted-foreground mb-1 text-sm font-medium">
                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.initiatedBy">
                          Initiated By
                        </Trans>
                      </h3>
                      <p>
                        {syncLog.created_by ||
                          t('integrations:ipos.syncHistoryDetail.system')}
                      </p>
                    </div>
                  </div>
                </div>

                {syncLog.error_message && (
                  <Alert className="mt-6 border-red-200 bg-red-50">
                    <AlertTitle className="text-red-800">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.error">
                        Error
                      </Trans>
                    </AlertTitle>
                    <AlertDescription className="text-red-700">
                      {syncLog.error_message}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Items Tabs */}
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.items">
                    Sync Items
                  </Trans>
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.itemsDescription">
                    Details of individual items processed during this sync.
                  </Trans>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs
                  value={activeTab}
                  onValueChange={(value) => setActiveTab(value as any)}
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="overview" data-testid="overview-tab">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.overview">
                        Overview
                      </Trans>
                      <span className="bg-muted ml-2 rounded-full px-2 py-0.5 text-xs">
                        {syncItems.length}
                      </span>
                    </TabsTrigger>
                    <TabsTrigger value="success" data-testid="success-tab">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.success">
                        Success
                      </Trans>
                      <span className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                        {successItems.length}
                      </span>
                    </TabsTrigger>
                    <TabsTrigger value="failed" data-testid="failed-tab">
                      <Trans i18nKey="integrations:ipos.syncHistoryDetail.failed">
                        Failed
                      </Trans>
                      <span className="ml-2 rounded-full bg-red-100 px-2 py-0.5 text-xs text-red-800">
                        {failedItems.length}
                      </span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="mt-4">
                    {syncItems.length === 0 ? (
                      <div className="flex h-40 items-center justify-center rounded-md border">
                        <p className="text-muted-foreground">
                          <Trans i18nKey="integrations:ipos.syncHistoryDetail.noItems">
                            No items found
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="rounded-md border">
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="bg-muted/50 border-b">
                                <th className="px-4 py-3 text-left font-medium">
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.externalId">
                                    External ID
                                  </Trans>
                                </th>
                                <th className="px-4 py-3 text-left font-medium">
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.internalId">
                                    Internal ID
                                  </Trans>
                                </th>
                                <th className="px-4 py-3 text-left font-medium">
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.status">
                                    Status
                                  </Trans>
                                </th>
                                <th className="px-4 py-3 text-left font-medium">
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.createdAt">
                                    Created At
                                  </Trans>
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {syncItems.slice(0, 10).map((item) => (
                                <tr key={item.id} className="border-b">
                                  <td className="px-4 py-3">
                                    {item.external_id}
                                  </td>
                                  <td className="px-4 py-3">
                                    {item.internal_id || '-'}
                                  </td>
                                  <td className="px-4 py-3">
                                    {item.status === 'success' ? (
                                      <Badge
                                        variant="outline"
                                        className="border-green-200 bg-green-50 text-green-700"
                                      >
                                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.success">
                                          Success
                                        </Trans>
                                      </Badge>
                                    ) : (
                                      <Badge
                                        variant="outline"
                                        className="border-red-200 bg-red-50 text-red-700"
                                      >
                                        <Trans i18nKey="integrations:ipos.syncHistoryDetail.error">
                                          Error
                                        </Trans>
                                      </Badge>
                                    )}
                                  </td>
                                  <td className="px-4 py-3">
                                    {formatDate(item.created_at)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        {syncItems.length > 10 && (
                          <div className="bg-muted/20 text-muted-foreground p-2 text-center text-xs">
                            <Trans
                              i18nKey="integrations:ipos.syncHistoryDetail.showingItems"
                              values={{ shown: 10, total: syncItems.length }}
                            >
                              Showing 10 of {syncItems.length} items
                            </Trans>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="success" className="mt-4">
                    {successItems.length === 0 ? (
                      <div className="flex h-40 items-center justify-center rounded-md border">
                        <p className="text-muted-foreground">
                          <Trans i18nKey="integrations:ipos.syncHistoryDetail.noSuccessItems">
                            No successful items found
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {successItems.slice(0, 5).map((item) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardHeader className="bg-green-50 py-3">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-base">
                                  <Trans
                                    i18nKey="integrations:ipos.syncHistoryDetail.itemId"
                                    values={{ id: item.external_id }}
                                  >
                                    Item ID: {item.external_id}
                                  </Trans>
                                </CardTitle>
                                <Badge
                                  variant="outline"
                                  className="border-green-200 bg-green-50 text-green-700"
                                >
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.success">
                                    Success
                                  </Trans>
                                </Badge>
                              </div>
                              <CardDescription>
                                <Trans
                                  i18nKey="integrations:ipos.syncHistoryDetail.internalId"
                                  values={{ id: item.internal_id || '-' }}
                                >
                                  Internal ID: {item.internal_id || '-'}
                                </Trans>
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="p-4">
                              <Tabs defaultValue="raw">
                                <TabsList className="w-full">
                                  <TabsTrigger value="raw" className="flex-1">
                                    <Trans i18nKey="integrations:ipos.syncHistoryDetail.rawData">
                                      Raw Data
                                    </Trans>
                                  </TabsTrigger>
                                  <TabsTrigger
                                    value="processed"
                                    className="flex-1"
                                  >
                                    <Trans i18nKey="integrations:ipos.syncHistoryDetail.processedData">
                                      Processed Data
                                    </Trans>
                                  </TabsTrigger>
                                </TabsList>
                                <TabsContent value="raw" className="mt-4">
                                  {renderJsonData(item.raw_data)}
                                </TabsContent>
                                <TabsContent value="processed" className="mt-4">
                                  {renderJsonData(item.processed_data)}
                                </TabsContent>
                              </Tabs>
                            </CardContent>
                          </Card>
                        ))}

                        {successItems.length > 5 && (
                          <div className="bg-muted/20 rounded-md p-2 text-center text-sm">
                            <Trans
                              i18nKey="integrations:ipos.syncHistoryDetail.showingSuccessItems"
                              values={{ shown: 5, total: successItems.length }}
                            >
                              Showing 5 of {successItems.length} successful
                              items
                            </Trans>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="failed" className="mt-4">
                    {failedItems.length === 0 ? (
                      <div className="flex h-40 items-center justify-center rounded-md border">
                        <p className="text-muted-foreground">
                          <Trans i18nKey="integrations:ipos.syncHistoryDetail.noFailedItems">
                            No failed items found
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {failedItems.slice(0, 5).map((item) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardHeader className="bg-red-50 py-3">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-base">
                                  <Trans
                                    i18nKey="integrations:ipos.syncHistoryDetail.itemId"
                                    values={{ id: item.external_id }}
                                  >
                                    Item ID: {item.external_id}
                                  </Trans>
                                </CardTitle>
                                <Badge
                                  variant="outline"
                                  className="border-red-200 bg-red-50 text-red-700"
                                >
                                  <Trans i18nKey="integrations:ipos.syncHistoryDetail.error">
                                    Error
                                  </Trans>
                                </Badge>
                              </div>
                              <CardDescription className="text-red-700">
                                {item.error_message}
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="p-4">
                              <Tabs defaultValue="raw">
                                <TabsList className="w-full">
                                  <TabsTrigger value="raw" className="flex-1">
                                    <Trans i18nKey="integrations:ipos.syncHistoryDetail.rawData">
                                      Raw Data
                                    </Trans>
                                  </TabsTrigger>
                                  <TabsTrigger
                                    value="processed"
                                    className="flex-1"
                                  >
                                    <Trans i18nKey="integrations:ipos.syncHistoryDetail.processedData">
                                      Processed Data
                                    </Trans>
                                  </TabsTrigger>
                                </TabsList>
                                <TabsContent value="raw" className="mt-4">
                                  {renderJsonData(item.raw_data)}
                                </TabsContent>
                                <TabsContent value="processed" className="mt-4">
                                  {renderJsonData(item.processed_data)}
                                </TabsContent>
                              </Tabs>
                            </CardContent>
                          </Card>
                        ))}

                        {failedItems.length > 5 && (
                          <div className="bg-muted/20 rounded-md p-2 text-center text-sm">
                            <Trans
                              i18nKey="integrations:ipos.syncHistoryDetail.showingFailedItems"
                              values={{ shown: 5, total: failedItems.length }}
                            >
                              Showing 5 of {failedItems.length} failed items
                            </Trans>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        )}
      </PageBody>
    </>
  );
}
