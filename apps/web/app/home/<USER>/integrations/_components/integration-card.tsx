'use client';

import { useState, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card } from '@kit/ui/card';
import { Switch } from '@kit/ui/switch';

import { toggleIntegration } from '../_lib/server/integrations.actions';
import type { Integration } from '../_lib/server/integrations.loader';
import { ServiceUnavailableDialog } from './service-unavailable-dialog';

interface IntegrationCardProps {
  integration: Integration;
  accountId: string;
  accountSlug: string;
}

// Danh sách các dịch vụ đã sẵn sàng
const AVAILABLE_SERVICES = ['zns', 'ipos', 'sapo', 'kiotviet', 'misa-eshop'];

export function IntegrationCard({
  integration,
  accountSlug,
  accountId,
}: IntegrationCardProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const { t } = useTranslation(['integrations', 'common']);
  const [showUnavailableDialog, setShowUnavailableDialog] = useState(false);
  // Kiểm tra xem dịch vụ có sẵn sàng không
  const isServiceAvailable = AVAILABLE_SERVICES.includes(integration.type);

  const handleConnect = () => {
    if (isServiceAvailable) {
      router.push(
        `/home/<USER>/integrations/${integration.type}/connect`,
      );
    } else {
      setShowUnavailableDialog(true);
    }
  };

  const handleConfigure = () => {
    if (isServiceAvailable) {
      // Đối với IPOS, chuyển hướng đến trang dashboard
      if (integration.type === 'ipos') {
        router.push(`/home/<USER>/integrations/ipos/dashboard`);
      } else {
        router.push(`/home/<USER>/integrations/${integration.type}`);
      }
    } else {
      setShowUnavailableDialog(true);
    }
  };

  const handleToggle = async (enabled: boolean) => {
    startTransition(async () => {
      try {
        await toggleIntegration(accountId, integration.id, enabled);
        toast.success(
          `${integration.config.name} ${enabled ? 'enabled' : 'disabled'}`,
        );
      } catch (error) {
        toast.error('Failed to update integration status');
      }
    });
  };

  const renderConfigButton = () => {
    switch (integration.type) {
      case 'zns':
        return (
          <div className="flex w-full gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                router.push(`/home/<USER>/integrations/zns/connect`)
              }
              className="relative z-10 flex-1"
            >
              {t('integrations:configure')}
            </Button>
            <Button
              type="button"
              variant="default"
              onClick={() =>
                router.push(`/home/<USER>/integrations/zns/dashboard`)
              }
              className="relative z-10 flex-1"
            >
              {t('integrations:dashboard')}
            </Button>
          </div>
        );
      case 'ipos':
        return (
          <div className="flex w-full gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                router.push(`/home/<USER>/integrations/ipos/connect`)
              }
              className="relative z-10 flex-1"
            >
              {t('integrations:configure')}
            </Button>
            <Button
              type="button"
              variant="default"
              onClick={() =>
                router.push(`/home/<USER>/integrations/ipos/dashboard`)
              }
              className="relative z-10 flex-1"
            >
              {t('integrations:dashboard')}
            </Button>
          </div>
        );
      default:
        return (
          <Button
            variant="outline"
            onClick={handleConfigure}
            className="w-full"
          >
            {t('integrations:configure')}
          </Button>
        );
    }
  };

  return (
    <>
      <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
        <div className="from-primary/5 absolute inset-0 bg-gradient-to-r via-transparent to-transparent opacity-0 transition-opacity group-hover:opacity-100" />

        <div className="space-y-4 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="bg-primary/10 absolute inset-0 rounded-full blur-sm" />
                <img
                  src={integration.config.logo}
                  alt={integration.name}
                  className="border-background relative h-12 w-12 rounded-full border-2 object-cover"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="text-foreground text-lg font-semibold">
                    {integration.name}
                  </h3>
                  {!isServiceAvailable && (
                    <Badge
                      variant="outline"
                      className="border-amber-200 bg-amber-50 text-amber-700"
                    >
                      {t('integrations:comingSoon')}
                    </Badge>
                  )}
                </div>
                <div className="text-muted-foreground text-sm">
                  {integration.status === 'connected' ? (
                    <span className="flex items-center text-green-600">
                      <span className="mr-2 h-2 w-2 rounded-full bg-green-600" />
                      {t('integrations:connected')}
                    </span>
                  ) : (
                    <span className="text-muted-foreground flex items-center">
                      <span className="bg-muted mr-2 h-2 w-2 rounded-full" />
                      {t('integrations:not_connected')}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <p className="text-muted-foreground text-sm leading-relaxed">
            {integration.description}
          </p>

          <div className="border-border flex items-center justify-between border-t pt-4">
            {integration.status === 'connected' ? (
              <div className="flex w-full flex-col space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Switch
                      checked={integration.enabled}
                      onCheckedChange={handleToggle}
                      disabled={isPending || !isServiceAvailable}
                    />
                    <span className="text-muted-foreground text-sm font-medium">
                      {integration.enabled
                        ? t('integrations:enabled')
                        : t('integrations:disabled')}
                    </span>
                  </div>
                  {integration.enabled && (
                    <span className="text-muted-foreground/60 text-xs">
                      {t('integrations:lastUpdated')}: {t('common:today')}
                    </span>
                  )}
                </div>

                {integration.enabled && (
                  <div className="w-full">
                    <div className="text-muted-foreground mb-2 text-xs">
                      {t('integrations:configureDescription')}
                    </div>
                    {renderConfigButton()}
                  </div>
                )}
              </div>
            ) : (
              <Button
                onClick={handleConnect}
                disabled={isPending}
                className="relative z-10 w-full"
                variant={integration.type === 'ipos' ? 'default' : 'outline'}
              >
                {t('integrations:connect')}
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* Dialog cho dịch vụ chưa sẵn sàng */}
      <ServiceUnavailableDialog
        isOpen={showUnavailableDialog}
        onClose={() => setShowUnavailableDialog(false)}
        serviceName={integration.name}
        serviceType={integration.type}
        estimatedAvailability={integration.config.estimatedAvailability}
        learnMoreUrl={integration.config.learnMoreUrl}
        contactEmail="<EMAIL>"
      />
    </>
  );
}
