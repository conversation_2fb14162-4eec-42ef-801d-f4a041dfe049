'use client';

import React, { useTransition } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import { AlertCircle, Info } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { getZaloOfficialAccount, refreshAccessToken } from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';

const oaConfigSchema = z.object({
  appId: z.string().min(1, 'App ID là bắt buộc'),
  secretKey: z.string().min(1, 'Secret Key là bắt buộc'),
  oaId: z.string().optional(),
  accessToken: z.string().optional(),
  refreshToken: z.string().optional(),
  userInfo: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      picture: z.string().optional(),
    })
    .optional(),
});

type OaConfigFormValues = z.infer<typeof oaConfigSchema>;

export default function ZnsSetupPage() {
  const { account: accountSlug } = useParams();
  const supabase = useSupabase();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const { t } = useTranslation(['integrations']);
  const { user: currentUser, account } = useTeamAccountWorkspace();

  // Sử dụng account ID trực tiếp từ hook useTeamAccountWorkspace
  const accountId = account?.id;
  const isLoadingAccount = !accountId;

  // Lấy thông tin OA configuration hiện tại
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['oa-config', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      // First try to get account-specific OA configuration
      let { data, error } = await supabase
        .from('oa_configurations')
        .select('*')
        .eq('account_id', accountId)
        .maybeSingle();

      if (error) throw error;

      // If no account-specific configuration exists, try to get system default
      if (!data) {
        const { data: systemData, error: systemError } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('is_system_default', true)
          .maybeSingle();

        if (systemError) throw systemError;
        data = systemData;
      }

      return data;
    },
    enabled: !!accountId,
  });

  // Kiểm tra quyền cập nhật OA configuration
  const { data: canUpdateOaConfig, isLoading: isLoadingPermission } = useQuery({
    queryKey: ['can-update-oa-config', oaConfig?.id],
    queryFn: async () => {
      if (!oaConfig?.id) return true; // Nếu chưa có OA config, cho phép tạo mới

      const { data, error } = await supabase.rpc('can_connect_oa', {
        oa_config_id: oaConfig.id,
      });

      if (error) {
        console.error('Error checking OA connection permission:', error);
        return false;
      }

      return !!data;
    },
    enabled: !!oaConfig?.id,
  });

  // Xác định loại OA configuration
  const oaConfigType = oaConfig
    ? oaConfig.is_system_default
      ? 'system'
      : oaConfig.theme_id
        ? 'theme'
        : oaConfig.account_id
          ? 'account'
          : 'unknown'
    : 'account'; // Mặc định là account nếu tạo mới

  // Form
  const form = useForm<OaConfigFormValues>({
    resolver: zodResolver(oaConfigSchema),
    defaultValues: {
      appId: '',
      secretKey: '',
      oaId: '',
      accessToken: '',
      refreshToken: '',
      userInfo: {
        id: '',
        name: '',
        picture: '',
      },
    },
  });

  // Cập nhật form khi có dữ liệu
  React.useEffect(() => {
    if (oaConfig && !form.formState.isDirty) {
      form.reset({
        appId: oaConfig.app_id || '',
        secretKey: oaConfig.secret_key || '',
        oaId: oaConfig.oa_id || '',
        accessToken: oaConfig.access_token || '',
        refreshToken: oaConfig.refresh_token || '',
        userInfo: oaConfig.oa_metadata?.user_info
          ? {
              id: oaConfig.oa_metadata.user_info.id || '',
              name: oaConfig.oa_metadata.user_info.name || '',
              picture: oaConfig.oa_metadata.user_info.picture || '',
            }
          : {
              id: '',
              name: '',
              picture: '',
            },
      });
    }
  }, [oaConfig, form]);

  /**
   * Kiểm tra Access Token và Refresh Token
   * @param appId App ID của Zalo OA
   * @param secretKey Secret Key của Zalo OA
   * @param accessToken Access Token cần kiểm tra
   * @param refreshToken Refresh Token để làm mới nếu cần
   * @returns Kết quả kiểm tra và thông tin token mới nếu có
   */
  const validateTokens = async (
    appId: string,
    secretKey: string,
    accessToken?: string,
    refreshToken?: string,
  ) => {
    if (!accessToken && !refreshToken) {
      return { isValid: false, message: 'No tokens provided' };
    }

    try {
      // Nếu không có access token hợp lệ nhưng có refresh token, thử làm mới
      if (refreshToken) {
        try {
          const result = await refreshAccessToken(
            appId,
            secretKey,
            refreshToken,
          );

          // Lấy thông tin người dùng từ access token mới
          try {
            const userInfo = await getZaloOfficialAccount(result.accessToken);
            console.log('userInfo', userInfo);
            return {
              isValid: true,
              message: 'Successfully refreshed token',
              tokenInfo: {
                accessToken: result.accessToken,
                refreshToken: result.refreshToken,
                expiresIn: result.expiresIn || 90000, // Mặc định 25 giờ nếu không có expiresIn
              },
              userInfo: {
                id: userInfo.data?.oaid,
                name: userInfo.data?.name,
                picture: userInfo.data?.avatar,
              },
            };
          } catch (userError: any) {
            // Nếu không lấy được thông tin người dùng, vẫn trả về token mới
            console.warn(
              'Could not fetch user info with new token:',
              userError,
            );

            return {
              isValid: false,
              message: userError.message || 'Unknown error',
            };
          }
        } catch (refreshError: any) {
          // Xử lý lỗi refresh token
          const errorMessage = refreshError.message || 'Unknown error';

          console.log('Refresh token error:', errorMessage);

          return {
            isValid: false,
            message: errorMessage.includes('Invalid refresh token')
              ? 'Refresh token is invalid or has expired. Please re-authenticate.'
              : `Failed to refresh token: ${errorMessage}`,
          };
        }
      }

      return { isValid: false, message: 'No valid tokens provided' };
    } catch (error: any) {
      // Xử lý lỗi chung trong hàm validateTokens
      const errorMessage = error.message || 'Unknown error';

      // Không hiển thị toàn bộ đối tượng lỗi trong console
      console.error('Token validation error:', errorMessage);

      return {
        isValid: false,
        message: errorMessage,
      };
    }
  };

  // Xử lý submit form
  const onSubmit = async (values: OaConfigFormValues) => {
    if (!accountId) return;
    // Kiểm tra quyền cập nhật
    if (oaConfig && oaConfig.is_system_default) {
      // Kiểm tra quyền cập nhật dựa trên account_id
      if (!currentUser || !account || oaConfig.author_id !== currentUser.id) {
        toast.error(t('common:errors.permissionDenied'), {
          description: t('integrations:zns.setup.cannotModifySystem'),
        });
        return;
      }

      // Nếu là account sở hữu, cho phép cập nhật
    }

    if (oaConfig && !oaConfig.is_system_default) {
      // Nếu là account sở hữu, cho phép cập nhật
      if (account && oaConfig.account_id === account.id) {
        // Cho phép cập nhật nếu là account sở hữu
      }
      // Nếu không phải là account sở hữu và không có quyền cập nhật, hiển thị thông báo lỗi
      else if (!canUpdateOaConfig) {
        toast.error(t('common:errors.permissionDenied'), {
          description: t('integrations:zns.setup.permissionDenied', {
            manager:
              oaConfigType === 'system'
                ? t('integrations:zns.setup.systemAuthor')
                : oaConfigType === 'theme'
                  ? t('integrations:zns.setup.themeAuthor')
                  : t('integrations:zns.setup.anotherUser'),
          }),
        });
        return;
      }
    }

    startTransition(async () => {
      try {
        // Sử dụng user hiện tại từ hook
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Kiểm tra Access Token và Refresh Token nếu có
        let tokenInfo = null;
        let validationResult = null;
        if (values.accessToken || values.refreshToken) {
          toast.info('Validating tokens...', { id: 'validating-tokens' });

          validationResult = await validateTokens(
            values.appId,
            values.secretKey,
            values.accessToken,
            values.refreshToken,
          );

          if (validationResult.isValid && validationResult.tokenInfo) {
            tokenInfo = validationResult.tokenInfo;

            // Lưu thông tin người dùng nếu có
            if (validationResult.userInfo) {
              form.setValue('userInfo', validationResult.userInfo);

              // Đảm bảo userInfo được lưu trực tiếp vào biến để sử dụng sau này
              values.userInfo = validationResult.userInfo;
            }

            toast.success('Tokens validated successfully', {
              id: 'validating-tokens',
            });
          } else {
            // Nếu validation thất bại, hiển thị lỗi và dừng lại
            const errorMessage = validationResult.message || 'Unknown error';

            toast.error(`Token validation failed: ${errorMessage}`, {
              id: 'validating-tokens',
            });

            return; // Dừng lại, không lưu form
          }
        }

        // Cập nhật hoặc tạo mới OA configuration
        const upsertData: any = {
          app_id: values.appId,
          secret_key: values.secretKey,
          oa_id: values.oaId || null,
          access_token: tokenInfo?.accessToken || values.accessToken || null,
          refresh_token: tokenInfo?.refreshToken || values.refreshToken || null,
          user_oa_id: validationResult?.userInfo?.id || null,
          oa_metadata: {
            ...oaConfig?.oa_metadata,
            configured_at: new Date().toISOString(),
            token_validated: !!tokenInfo,
            token_validation_time: new Date().toISOString(),
            user_info: validationResult?.userInfo || values.userInfo || null,
          },
        };
        // Cập nhật thời gian hết hạn token
        if (tokenInfo?.expiresIn && tokenInfo.expiresIn > 0) {
          // Nếu có expiresIn hợp lệ, sử dụng nó
          const expiresAt = new Date(Date.now() + tokenInfo.expiresIn * 1000);
          upsertData.token_expires_at = expiresAt.toISOString();
        } else {
          // Nếu không có expiresIn hoặc expiresIn không hợp lệ, sử dụng mặc định 24h
          const expiresAt = new Date();
          expiresAt.setHours(expiresAt.getHours() + 24); // Mặc định 24h
          upsertData.token_expires_at = expiresAt.toISOString();
          console.log('Using default expiration time (24h) for token');
        }
        let result;
        if (oaConfig) {
          // Cập nhật trực tiếp bằng id
          const { data, error } = await supabase
            .from('oa_configurations')
            .update(upsertData)
            .eq('id', oaConfig.id)
            .select()
            .single();
          console.log('oa_configurations updated', data);
          if (error) throw error;
          result = data;
        } else {
          if (!upsertData.account_id && !oaConfig.is_system_default) {
            upsertData.account_id = accountId;
          }
          const { data, error } = await supabase
            .from('oa_configurations')
            .insert(upsertData)
            .select()
            .single();
          if (error) throw error;
          result = data;
        }

        // Kết quả cuối cùng
        const data = result;

        // Kiểm tra xem integration đã tồn tại chưa
        const { data: existingIntegration, error: checkIntegrationError } =
          await supabase
            .from('integrations')
            .select('id')
            .eq('account_id', accountId)
            .eq('type', 'zalo')
            .maybeSingle();

        if (checkIntegrationError) {
          console.error(
            'Error checking existing integration:',
            checkIntegrationError,
          );
        }

        let integrationError = null;

        // Sử dụng cách tiếp cận khác để tránh lỗi cast
        if (existingIntegration) {
          // Cập nhật integration hiện tại
          const { error } = await supabase
            .from('integrations')
            .update({
              name: 'Zalo Notification Service',
              enabled: false,
              metadata: {
                oa_config_id: data.id,
                oa_config_type: oaConfigType,
              },
            })
            .eq('id', existingIntegration.id);

          integrationError = error;
        } else {
          const { data: newIntegration, error: insertError } = await supabase
            .from('integrations')
            .insert({
              account_id: accountId,
              type: 'zalo',
              name: 'Zalo Notification Service',
              enabled: false,
              metadata: {
                oa_config_id: data.id,
                oa_config_type: oaConfigType,
              },
            })
            .select()
            .single();

          integrationError = insertError;
        }

        if (integrationError) throw integrationError;

        toast.success(t('integrations:zns.setup.success'));

        // Chuyển hướng đến trang connect
        router.push(`/home/<USER>/integrations/zns/connect`);
      } catch (error: any) {
        console.error('Error saving OA configuration:', error);
        toast.error(t('integrations:zns.setup.error'), {
          description: error.message,
        });
      }
    });
  };

  const isLoading = isLoadingAccount || isLoadingOaConfig;

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={t('integrations:zns.setup.title')}
        description={
          <AppBreadcrumbs
            values={{ zns: 'ZNS', setup: t('integrations:setup') }}
          />
        }
      />
      <PageBody data-testid="zns-page">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>{t('integrations:zns.setup.title')}</CardTitle>
              <CardDescription>
                {t('integrations:zns.setup.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-6">
                <Info className="h-4 w-4" />
                <AlertTitle>{t('integrations:zns.setup.howToGet')}</AlertTitle>
                <AlertDescription>
                  <ol className="mt-2 list-decimal space-y-1 pl-4">
                    <li>
                      {t('integrations:zns.setup.steps.step1')}{' '}
                      <a
                        href="https://developers.zalo.me/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary underline"
                      >
                        Zalo Developers
                      </a>
                    </li>
                    <li>{t('integrations:zns.setup.steps.step2')}</li>
                    <li>{t('integrations:zns.setup.steps.step3')}</li>
                    <li>{t('integrations:zns.setup.steps.step4')}</li>
                  </ol>
                </AlertDescription>
              </Alert>

              {oaConfig && !canUpdateOaConfig ? (
                <Alert className="mb-6" variant="warning">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t('common:errors.permissionDenied')}</AlertTitle>
                  <AlertDescription>
                    {t('integrations:zns.setup.permissionDenied', {
                      manager:
                        oaConfigType === 'system'
                          ? t('integrations:zns.setup.systemAuthor')
                          : oaConfigType === 'theme'
                            ? t('integrations:zns.setup.themeAuthor')
                            : t('integrations:zns.setup.anotherUser'),
                    })}
                  </AlertDescription>
                </Alert>
              ) : (
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="appId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('integrations:zns.setup.form.appId')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'integrations:zns.setup.form.appId',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('integrations:zns.setup.form.appIdDescription')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="secretKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('integrations:zns.setup.form.secretKey')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder={t(
                                'integrations:zns.setup.form.secretKey',
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {t(
                              'integrations:zns.setup.form.secretKeyDescription',
                            )}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="oaId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t('integrations:zns.setup.form.oaId')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                'integrations:zns.setup.form.oaId',
                              )}
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormDescription>
                            {t('integrations:zns.setup.form.oaIdDescription')}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="bg-muted/30 rounded-lg border p-4">
                      <h3 className="mb-2 font-medium">
                        {t('integrations:zns.setup.form.manualTokenSection')}
                      </h3>
                      <p className="text-muted-foreground mb-4 text-sm">
                        {t(
                          'integrations:zns.setup.form.manualTokenDescription',
                        )}{' '}
                        {form.watch('appId') ? (
                          <>
                            <a
                              href={`https://developers.zalo.me/tools/explorer/${form.watch('appId')}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary underline"
                            >
                              {t('integrations:zns.setup.form.getTokenLink')}
                            </a>{' '}
                            {t('integrations:zns.setup.form.selectOaTokenType')}
                          </>
                        ) : (
                          <>
                            {t('integrations:zns.setup.form.getTokenLink')}{' '}
                            <span className="text-muted-foreground italic">
                              (
                              {t('integrations:zns.setup.form.enterAppIdFirst')}
                              )
                            </span>
                          </>
                        )}
                      </p>

                      <FormField
                        control={form.control}
                        name="accessToken"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('integrations:zns.setup.form.accessToken')}
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'integrations:zns.setup.form.accessTokenPlaceholder',
                                )}
                                {...field}
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              {t(
                                'integrations:zns.setup.form.accessTokenDescription',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="refreshToken"
                        render={({ field }) => (
                          <FormItem className="mt-4">
                            <FormLabel>
                              {t('integrations:zns.setup.form.refreshToken')}
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t(
                                  'integrations:zns.setup.form.refreshTokenPlaceholder',
                                )}
                                {...field}
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              {t(
                                'integrations:zns.setup.form.refreshTokenDescription',
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={isPending || isLoading}
                      className="w-full"
                    >
                      {isPending
                        ? t('common:saving')
                        : t('integrations:zns.setup.form.submit')}
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
