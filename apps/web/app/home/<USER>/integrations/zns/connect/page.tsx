'use client';

import { useTransition } from 'react';

import { router } from 'next/client';
import { useParams } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import axios from 'axios';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import { PageBody } from '@kit/ui/page';
import { Skeleton } from '@kit/ui/skeleton';
import { getValidZnsToken } from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '../../../_components/team-account-layout-page-header';
import { toggleIntegration } from '../../_lib/server/integrations.actions';
import ConnectionStatusCard from '../_components/connection-status-card';
import NotificationTemplatesCard from '../_components/notification-templates-card';
import OaInformationCard from '../_components/oa-information-card';

export default function ZnsConnectPage() {
  const { account: accountSlug } = useParams();
  const { t } = useTranslation(['integrations', 'common']);

  const supabase = useSupabase();
  const [isPending, startTransition] = useTransition();
  const { account } = useTeamAccountWorkspace();

  // Sử dụng account ID trực tiếp từ hook useTeamAccountWorkspace
  const accountId = account?.id;

  // Fetch ZNS integration
  const {
    data: integration,
    isLoading: isLoadingIntegration,
    refetch: refetchIntegration,
  } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration if we have oa_config_id
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['oa-config', integration?.metadata?.oa_config_id, accountId],
    queryFn: async () => {
      try {
        // First try to get OA configuration from integration metadata
        if (integration?.metadata?.oa_config_id) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('id', integration.metadata.oa_config_id)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            return data;
          }
        }

        // If no OA config ID in metadata or not found, try to find account-specific configuration
        if (accountId) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('account_id', accountId)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by account ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            console.log('Loaded account-specific OA config:', data);
            return data;
          }

          // If no account-specific configuration exists, try to get system default
          const { data: systemData, error: systemError } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('is_system_default', true)
            .maybeSingle();

          if (systemError) {
            console.error(
              'Error fetching system default OA config:',
              JSON.stringify(systemError, null, 2),
            );
          } else if (systemData) {
            console.log('Loaded system default OA config:', systemData);
            return systemData;
          }
        }

        return null;
      } catch (err) {
        console.error(
          'Exception fetching OA configuration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: Boolean(accountId),
  });

  // Xác định loại OA configuration
  const oaConfigType = oaConfig
    ? oaConfig.is_system_default
      ? 'system'
      : oaConfig.theme_id
        ? 'theme'
        : oaConfig.account_id
          ? 'account'
          : 'unknown'
    : null;

  // Kiểm tra token có còn hạn hay không và refresh nếu cần
  const { data: tokenStatus, isLoading: isCheckingToken } = useQuery({
    queryKey: ['zns-token-status', oaConfig?.id],
    queryFn: async () => {
      if (!oaConfig?.id) return { isValid: false };

      try {
        // Sử dụng getValidZnsToken để lấy token hợp lệ và refresh nếu cần
        const { accessToken, oaConfig: updatedConfig } = await getValidZnsToken(
          supabase,
          oaConfig.id,
        );

        // Nếu có accessToken, có nghĩa là token hợp lệ hoặc đã được refresh thành công
        return {
          isValid: true,
          accessToken,
          updatedConfig,
        };
      } catch (error) {
        console.error('Error validating ZNS token:', error);
        return { isValid: false };
      }
    },
    enabled: Boolean(oaConfig?.id),
    refetchInterval: 5 * 60 * 1000, // Kiểm tra lại mỗi 5 phút
  });

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = Boolean(tokenStatus?.isValid);

  // Fetch ZNS templates
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates', oaConfig?.id, isReallyConnected, tokenStatus?.accessToken],
    queryFn: async () => {
      if (!oaConfig?.id || !isReallyConnected || !tokenStatus?.accessToken) return [];

      try {
        // Lấy danh sách template từ Zalo API trực tiếp
        const apiResponse = await axios.get('https://business.openapi.zalo.me/template/all', {
          params: {
            offset: 0,
            limit: 100
          },
          headers: {
            'Content-Type': 'application/json',
            'access_token': tokenStatus.accessToken
          }
        });

        if (apiResponse.data.error !== 0) {
          console.error('Error fetching ZNS templates from API:', apiResponse.data);
          throw new Error(`Failed to get templates: ${apiResponse.data.message}`);
        }

        // Lấy danh sách template từ response
        // Dữ liệu template nằm trực tiếp trong data, không phải data.templates
        const apiTemplates = apiResponse.data.data || [];

        // Lấy danh sách template từ cơ sở dữ liệu
        const { data: dbTemplates, error: dbError } = await supabase
          .from('zns_templates')
          .select('*')
          .eq('oa_config_id', oaConfig.id);

        if (dbError) {
          console.error('Error fetching ZNS templates from DB:', dbError);
          throw dbError;
        }

        // Kết hợp dữ liệu từ API và cơ sở dữ liệu
        const combinedTemplates = apiTemplates.map(apiTemplate => {
          // Tìm template tương ứng trong cơ sở dữ liệu
          const dbTemplate = dbTemplates?.find(t => t.template_id === String(apiTemplate.templateId));

          // Xác định tag dựa trên tên template
          let tag = 'CUSTOMER_CARE';
          const templateName = apiTemplate.templateName?.toLowerCase() || '';

          if (templateName.includes('thanh toán') ||
              templateName.includes('payment') ||
              templateName.includes('đơn hàng') ||
              templateName.includes('order')) {
            tag = 'TRANSACTION';
          } else if (templateName.includes('khuyến mãi') ||
                     templateName.includes('promotion') ||
                     templateName.includes('giảm giá') ||
                     templateName.includes('discount')) {
            tag = 'PROMOTION';
          }

          return {
            id: dbTemplate?.id || `api-${apiTemplate.templateId}`,
            template_id: String(apiTemplate.templateId),
            event_type: dbTemplate?.event_type || 'custom',
            enabled: dbTemplate?.enabled || false,
            content: apiTemplate.templateName,
            params: apiTemplate.listParams || [],
            status: apiTemplate.status,
            tag: tag,
            price: apiTemplate.price || '0',
            timeout: apiTemplate.timeout || 0,
            preview_url: apiTemplate.previewUrl,
            oa_config_id: oaConfig.id,
            created_at: dbTemplate?.created_at || new Date(apiTemplate.createdTime || Date.now()).toISOString(),
            updated_at: dbTemplate?.updated_at || new Date().toISOString()
          };
        });

        return combinedTemplates;
      } catch (error) {
        console.error('Error fetching ZNS templates:', error);

        // Fallback: Lấy dữ liệu chỉ từ cơ sở dữ liệu
        const { data, error: dbError } = await supabase
          .from('zns_templates')
          .select('*')
          .eq('oa_config_id', oaConfig.id);

        if (dbError) throw dbError;
        return data || [];
      }
    },
    enabled: Boolean(oaConfig?.id) && isReallyConnected && Boolean(tokenStatus?.accessToken),
  });

  // Toggle ZNS integration
  const handleToggleEnabled = (enabled: boolean) => {
    if (!integration || !accountId) return;

    startTransition(async () => {
      try {
        await toggleIntegration(accountId, integration.id, enabled);
        toast.success(
          enabled
            ? t('integrations:zns.connect.enabledSuccess')
            : t('integrations:zns.connect.disabledSuccess'),
        );
        refetchIntegration();
      } catch (error) {
        toast.error(t('integrations:zns.connect.updateStatusError'));
      }
    });
  };

  const isConnected = isReallyConnected;
  const isEnabled = integration?.enabled || false;
  const isLoading =
    isLoadingIntegration || isLoadingOaConfig || isCheckingToken;
  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug}
        title={t('integrations:zns.title')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              connect: t('integrations:connect', 'Kết nối')
            }}
          />
        }
      />
      <PageBody data-testid="zns-page" className="w-full max-w-none px-0">
        <div className="w-full px-4 md:px-6 lg:px-8">
          {isLoading ? (
            <div className="grid w-full grid-cols-1 gap-6 lg:grid-cols-3">
              <div className="w-full lg:col-span-1">
                <Skeleton className="h-[600px] w-full rounded-xl shadow-md" />
              </div>
              <div className="w-full space-y-6 lg:col-span-2">
                <Skeleton className="h-[280px] w-full rounded-xl shadow-md" />
                <Skeleton className="h-[280px] w-full rounded-xl shadow-md" />
              </div>
            </div>
          ) : (
            <div className="grid w-full grid-cols-1 gap-6 lg:grid-cols-3">
              {/* Left column - Connection Status */}
              <div className="w-full lg:col-span-1">
                <ConnectionStatusCard
                  accountId={accountSlug}
                  isConnected={isConnected}
                  isEnabled={isEnabled}
                  isLoading={false}
                  isPending={isPending}
                  oaConfig={oaConfig}
                  integration={integration}
                  onToggleEnabled={handleToggleEnabled}
                />
              </div>

              {/* Right column - OA Information and Templates */}
              <div className="w-full space-y-6 lg:col-span-2">
                {isConnected && oaConfig ? (
                  <>
                    <OaInformationCard
                      oaConfig={oaConfig}
                      oaConfigType={oaConfigType}
                    />

                    <NotificationTemplatesCard
                      accountId={accountSlug}
                      templates={templates}
                      isLoading={isLoadingTemplates}
                    />
                  </>
                ) : (
                  <div className="rounded-xl border border-blue-100 bg-gradient-to-br from-blue-50 to-indigo-50 p-8 text-center shadow-sm">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-8 w-8 text-blue-600"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                        <circle cx="9" cy="7" r="4" />
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                        <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                      </svg>
                    </div>
                    <h3 className="mb-2 text-xl font-semibold text-blue-800">
                      {t(
                        'integrations:zns.connect.connectFirst',
                        'Kết nối ZNS trước',
                      )}
                    </h3>
                    <p className="mx-auto mb-6 max-w-md text-blue-600">
                      {t(
                        'integrations:zns.connect.connectDescription',
                        'Bạn cần kết nối với Zalo Notification Service để có thể gửi thông báo đến người dùng Zalo.',
                      )}
                    </p>
                    <div className="flex justify-center space-x-4">
                      <Button
                        variant="outline"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/zns/setup`,
                          )
                        }
                        className="border-blue-200 bg-white text-blue-700 shadow-sm hover:bg-blue-50"
                      >
                        {t(
                          'integrations:zns.connect.configureOa',
                          'Cấu hình OA',
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </PageBody>
    </>
  );
}
