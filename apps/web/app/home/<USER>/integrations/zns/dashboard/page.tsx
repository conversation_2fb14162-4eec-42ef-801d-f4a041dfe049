'use client';

import { useState, useTransition } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import {
  Activity,
  AlertCircle,
  BarChart,
  Info,
  MessageSquare,
  RefreshCw,
  Settings,
  Users,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Progress } from '@kit/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';
import { getValidZnsToken, useZnsEventHandler } from '@kit/zns';
import { MappingOverviewCard } from '../_components/mapping-overview-card';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { toggleIntegration } from '../../_lib/server/integrations.actions';
import ConnectionStatusCard from '../_components/connection-status-card';

export default function ZNSDashboardPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();
  const { account } = useTeamAccountWorkspace();

  // Sử dụng account ID trực tiếp từ hook useTeamAccountWorkspace
  const accountId = account?.id;

  const [isPending, startTransition] = useTransition();
  const [connectionDetails, setConnectionDetails] = useState<any>(null);
  const [templateStats, setTemplateStats] = useState({
    total: 0,
    enabled: 0,
    pending: 0,
    rejected: 0,
  });
  const [quotaStats, setQuotaStats] = useState({
    dailyQuota: 0,
    remainingQuota: 0,
    usedToday: 0,
    usagePercentage: 0,
  });
  const [healthStatus, setHealthStatus] = useState({
    apiLatency: 0,
    lastError: null as string | null,
    errorCount: 0,
    uptime: 100,
  });

  // Account usage statistics
  const [accountStats, setAccountStats] = useState({
    totalSent: 0,
    totalDelivered: 0,
    totalFailed: 0,
    deliveryRate: 0,
    lastSentAt: null as string | null,
    monthlySent: [] as { month: string; count: number }[],
  });

  // Fetch ZNS integration
  const {
    data: integration,
    isLoading: isLoadingIntegration,
    refetch: refetchIntegration,
  } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration if we have oa_config_id
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['oa-config', integration?.metadata?.oa_config_id, accountId],
    queryFn: async () => {
      try {
        // First try to get OA configuration from integration metadata
        if (integration?.metadata?.oa_config_id) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('id', integration.metadata.oa_config_id)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            return data;
          }
        }

        // If no OA config ID in metadata or not found, try to find account-specific configuration
        if (accountId) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('account_id', accountId)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by account ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            console.log('Loaded account-specific OA config:', data);
            return data;
          }

          // If no account-specific configuration exists, try to get system default
          const { data: systemData, error: systemError } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('is_system_default', true)
            .maybeSingle();

          if (systemError) {
            console.error(
              'Error fetching system default OA config:',
              JSON.stringify(systemError, null, 2),
            );
          } else if (systemData) {
            console.log('Loaded system default OA config:', systemData);
            return systemData;
          }
        }

        return null;
      } catch (err) {
        console.error(
          'Exception fetching OA configuration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: Boolean(accountId),
    onSuccess: (data) => {
      if (data) {
        setConnectionDetails(data);
      }
    },
  });

  // Kiểm tra token có còn hạn hay không và refresh nếu cần
  const { data: tokenStatus, isLoading: isCheckingToken } = useQuery({
    queryKey: ['zns-token-status', oaConfig?.id],
    queryFn: async () => {
      if (!oaConfig?.id) return { isValid: false };

      try {
        // Sử dụng getValidZnsToken để lấy token hợp lệ và refresh nếu cần
        const { accessToken, oaConfig: updatedConfig } = await getValidZnsToken(
          supabase,
          oaConfig.id,
        );

        // Nếu có accessToken, có nghĩa là token hợp lệ hoặc đã được refresh thành công
        return {
          isValid: true,
          accessToken,
          updatedConfig,
        };
      } catch (error) {
        console.error('Error validating ZNS token:', error);
        return { isValid: false };
      }
    },
    enabled: Boolean(oaConfig?.id),
    refetchInterval: 5 * 60 * 1000, // Kiểm tra lại mỗi 5 phút
  });

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = Boolean(tokenStatus?.isValid);

  // Tính toán isLoading
  const isLoading =
    isLoadingIntegration || isLoadingOaConfig || isCheckingToken;

  // Trạng thái enabled của integration
  const [isEnabled, setIsEnabled] = useState(false);

  // Cập nhật isEnabled khi integration thay đổi
  useQuery({
    queryKey: ['update-is-enabled', integration?.id],
    queryFn: () => {
      setIsEnabled(integration?.enabled || false);
      return null;
    },
    enabled: !!integration,
  });

  // Fetch quota information
  useQuery({
    queryKey: ['zns-quota', oaConfig?.id, isReallyConnected],
    queryFn: async () => {
      if (!oaConfig?.id || !isReallyConnected) return null;

      // Lấy thông tin quota từ oa_metadata
      if (oaConfig.oa_metadata?.daily_quota) {
        const dailyQuota = oaConfig.oa_metadata.daily_quota;
        const remainingQuota =
          oaConfig.oa_metadata.remaining_quota || dailyQuota;
        const usedToday = dailyQuota - remainingQuota;
        const usagePercentage = Math.round((usedToday / dailyQuota) * 100);

        setQuotaStats({
          dailyQuota,
          remainingQuota,
          usedToday,
          usagePercentage,
        });
      }

      // Lấy thông tin health status
      if (oaConfig.oa_metadata?.health_status) {
        setHealthStatus(oaConfig.oa_metadata.health_status);
      }

      return oaConfig.oa_metadata || null;
    },
    enabled: Boolean(oaConfig?.id) && isReallyConnected,
  });

  // Fetch message statistics - Sử dụng dữ liệu mẫu vì bảng zns_messages chưa tồn tại
  useQuery({
    queryKey: ['zns-messages', accountId, isReallyConnected],
    queryFn: async () => {
      if (!accountId || !isReallyConnected) return [];

      // Tạo dữ liệu mẫu thay vì truy vấn từ database
      // TODO: Khi bảng zns_messages được tạo, thay thế bằng truy vấn thực tế
      const sampleData = [];
      const now = new Date();

      // Tạo dữ liệu mẫu cho 6 tháng gần nhất
      for (let i = 0; i < 6; i++) {
        const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const messagesCount = Math.floor(Math.random() * 50) + 10; // 10-60 tin nhắn mỗi tháng

        for (let j = 0; j < messagesCount; j++) {
          const day = Math.floor(Math.random() * 28) + 1;
          const date = new Date(month.getFullYear(), month.getMonth(), day);
          const status =
            Math.random() > 0.2
              ? 'delivered'
              : Math.random() > 0.5
                ? 'failed'
                : 'error';

          sampleData.push({
            id: `sample-${i}-${j}`,
            status,
            created_at: date.toISOString(),
            account_id: accountId,
          });
        }
      }

      return sampleData;
    },
    enabled: Boolean(accountId) && isReallyConnected,
    onSuccess: (messagesData) => {
      if (!messagesData.length) return;

      // Tính toán thống kê
      const totalSent = messagesData.length;
      const totalDelivered = messagesData.filter(
        (msg) => msg.status === 'delivered',
      ).length;
      const totalFailed = messagesData.filter((msg) =>
        ['failed', 'error'].includes(msg.status),
      ).length;
      const deliveryRate =
        totalSent > 0 ? Math.round((totalDelivered / totalSent) * 100) : 0;

      // Lấy thời gian gửi gần nhất
      const sortedMessages = [...messagesData].sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      );
      const lastSentAt =
        sortedMessages.length > 0 ? sortedMessages[0].created_at : null;

      // Tính toán số lượng tin nhắn theo tháng (6 tháng gần nhất)
      const now = new Date();
      const monthlySent = [];

      for (let i = 0; i < 6; i++) {
        const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthStr = month.toLocaleString('default', {
          month: 'short',
          year: 'numeric',
        });
        const monthStart = new Date(
          month.getFullYear(),
          month.getMonth(),
          1,
        ).toISOString();
        const monthEnd = new Date(
          month.getFullYear(),
          month.getMonth() + 1,
          0,
        ).toISOString();

        const count = messagesData.filter(
          (msg) => msg.created_at >= monthStart && msg.created_at <= monthEnd,
        ).length;

        monthlySent.push({ month: monthStr, count });
      }

      // Cập nhật state
      setAccountStats({
        totalSent,
        totalDelivered,
        totalFailed,
        deliveryRate,
        lastSentAt,
        monthlySent: monthlySent.reverse(),
      });
    },
  });

  // Toggle ZNS integration
  const handleToggleEnabled = (enabled: boolean) => {
    if (!integration || !account) return;

    startTransition(async () => {
      try {
        await toggleIntegration(account as string, integration.id, enabled);
        toast.success(
          enabled
            ? t('integrations:zns.connect.enabledSuccess')
            : t('integrations:zns.connect.disabledSuccess'),
        );
        setIsEnabled(enabled);
      } catch (error) {
        toast.error(t('integrations:zns.connect.updateStatusError'));
      }
    });
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account as string}
        title={t('integrations:zns.title')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              dashboard: t('integrations:dashboard', 'Bảng điều khiển')
            }}
          />
        }
      />

      <PageBody
        data-testid="zns-dashboard-page"
        className="w-full max-w-none px-0"
      >
        <div className="w-full px-4 md:px-6 lg:px-8">
          {/* Main content with grid layout */}
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-3 xl:grid-cols-4">
            {/* Left column - Connection Status */}
            <div className="w-full lg:col-span-1">
              <ConnectionStatusCard
                accountId={account.slug as string}
                isConnected={isReallyConnected}
                isEnabled={isEnabled}
                isLoading={false}
                isPending={isPending}
                oaConfig={connectionDetails}
                integration={integration}
                onToggleEnabled={handleToggleEnabled}
              />
            </div>

            {/* Right column - Stats and Information */}
            <div className="space-y-4 lg:col-span-2 xl:col-span-3">
              {/* Stats Cards Grid */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Template Stats Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 pb-2 dark:from-green-950/30 dark:to-emerald-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <MessageSquare className="h-5 w-5 text-green-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.templateStats">
                          Thống kê mẫu
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.templateStatsTooltip">
                                Thống kê về các mẫu ZNS của bạn
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-green-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewTemplates">
                            Kết nối với ZNS để xem thống kê mẫu.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.totalTemplates">
                              Tổng số mẫu
                            </Trans>
                          </span>
                          <Badge variant="outline" className="px-2 py-1">
                            {templateStats.total}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.enabledTemplates">
                              Mẫu đã kích hoạt
                            </Trans>
                          </span>
                          <Badge variant="success" className="px-2 py-1">
                            {templateStats.enabled}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.pendingTemplates">
                              Đang chờ duyệt
                            </Trans>
                          </span>
                          <Badge
                            variant="outline"
                            className="border-amber-200 bg-amber-50 px-2 py-1 text-amber-700"
                          >
                            {templateStats.pending}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.rejectedTemplates">
                              Mẫu bị từ chối
                            </Trans>
                          </span>
                          <Badge variant="destructive" className="px-2 py-1">
                            {templateStats.rejected}
                          </Badge>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/templates`,
                              )
                            }
                            disabled={!isReallyConnected}
                          >
                            <MessageSquare className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.manageTemplates">
                              Quản lý mẫu
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Quota Usage Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 pb-2 dark:from-purple-950/30 dark:to-violet-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <BarChart className="h-5 w-5 text-purple-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.quotaUsage">
                          Quota Usage
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.quotaUsageTooltip">
                                Your daily ZNS message quota usage
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-purple-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewQuota">
                            Connect to ZNS to view quota usage.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <div className="mb-2 flex items-center justify-between">
                            <span className="text-sm font-medium">
                              <Trans i18nKey="integrations:zns.connect.dashboard.dailyQuota">
                                Daily Quota
                              </Trans>
                            </span>
                            <span className="text-sm">
                              {quotaStats.usedToday} / {quotaStats.dailyQuota}
                            </span>
                          </div>
                          <Progress
                            value={quotaStats.usagePercentage}
                            className="h-2"
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.remainingToday">
                              Remaining Today
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.remainingQuota}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.usedToday">
                              Used Today
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.usedToday}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.usagePercentage">
                              Usage Percentage
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.usagePercentage}%
                          </span>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/analytics`,
                              )
                            }
                            disabled={!isReallyConnected}
                          >
                            <BarChart className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.viewAnalytics">
                              View Analytics
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Account Usage Statistics Card */}
              <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 pb-2 dark:from-purple-950/30 dark:to-pink-950/30">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Users className="h-5 w-5 text-purple-500" />
                      <Trans i18nKey="integrations:zns.dashboard.accountUsage">
                        Thống kê sử dụng tài khoản
                      </Trans>
                    </CardTitle>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="text-muted-foreground h-4 w-4" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            <Trans i18nKey="integrations:zns.dashboard.accountUsageTooltip">
                              Thống kê sử dụng ZNS của tài khoản của bạn
                            </Trans>
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  {isLoading ? (
                    <div className="flex h-32 items-center justify-center">
                      <RefreshCw className="h-6 w-6 animate-spin text-purple-500" />
                    </div>
                  ) : !isReallyConnected ? (
                    <div className="flex flex-col items-center justify-center space-y-4 py-4">
                      <AlertCircle className="h-12 w-12 text-amber-500" />
                      <p className="text-center text-sm">
                        <Trans i18nKey="integrations:zns.dashboard.connectToViewStats">
                          Kết nối với ZNS để xem thống kê sử dụng tài khoản.
                        </Trans>
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
                        <div className="rounded-lg border border-purple-100 bg-purple-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-purple-500">
                            <Trans i18nKey="integrations:zns.dashboard.totalSent">
                              Tổng đã gửi
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-purple-700">
                            {accountStats.totalSent.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-green-100 bg-green-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-green-500">
                            <Trans i18nKey="integrations:zns.dashboard.delivered">
                              Đã nhận
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-green-700">
                            {accountStats.totalDelivered.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-red-100 bg-red-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-red-500">
                            <Trans i18nKey="integrations:zns.dashboard.failed">
                              Thất bại
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-red-700">
                            {accountStats.totalFailed.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-blue-100 bg-blue-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-blue-500">
                            <Trans i18nKey="integrations:zns.dashboard.deliveryRate">
                              Tỷ lệ gửi thành công
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-blue-700">
                            {accountStats.deliveryRate}%
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">
                          <Trans i18nKey="integrations:zns.dashboard.monthlyMessageVolume">
                            Lượng tin nhắn hàng tháng
                          </Trans>
                        </h4>
                        <div className="h-40 w-full">
                          {accountStats.monthlySent.length > 0 ? (
                            <div className="flex h-full items-end gap-1">
                              {accountStats.monthlySent.map((item, index) => {
                                const maxCount = Math.max(
                                  ...accountStats.monthlySent.map(
                                    (i) => i.count,
                                  ),
                                );
                                const height =
                                  maxCount > 0
                                    ? (item.count / maxCount) * 100
                                    : 0;
                                return (
                                  <div
                                    key={index}
                                    className="flex flex-1 flex-col items-center"
                                  >
                                    <div
                                      className="w-full rounded-t-sm bg-gradient-to-t from-purple-400 to-pink-400"
                                      style={{
                                        height: `${Math.max(height, 5)}%`,
                                      }}
                                    ></div>
                                    <div className="mt-1 w-full truncate text-center text-xs text-gray-500">
                                      {item.month}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            <div className="flex h-full items-center justify-center text-sm text-gray-500">
                              <Trans i18nKey="integrations:zns.dashboard.noDataAvailable">
                                Không có dữ liệu
                              </Trans>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="pt-2">
                        <div className="text-xs text-gray-500">
                          {accountStats.lastSentAt ? (
                            <span>
                              <Trans
                                i18nKey="integrations:zns.dashboard.lastMessageSent"
                                values={{
                                  time: formatDistanceToNow(
                                    new Date(accountStats.lastSentAt),
                                  ),
                                }}
                              >
                                Tin nhắn gửi gần nhất:{' '}
                                {formatDistanceToNow(
                                  new Date(accountStats.lastSentAt),
                                )}{' '}
                                trước
                              </Trans>
                            </span>
                          ) : (
                            <span>
                              <Trans i18nKey="integrations:zns.dashboard.noMessagesYet">
                                Chưa gửi tin nhắn nào
                              </Trans>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Health Status and Recent Activity Cards */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Health Status Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 pb-2 dark:from-amber-950/30 dark:to-yellow-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Zap className="h-5 w-5 text-amber-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.healthStatus">
                          Health Status
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.healthStatusTooltip">
                                Current health status of your ZNS connection
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-amber-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewHealth">
                            Connect to ZNS to view health status.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.apiLatency">
                              API Latency
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.apiLatency}ms
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.uptime">
                              Uptime
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.uptime}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.errorCount">
                              Error Count
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.errorCount}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.lastError">
                              Last Error
                            </Trans>
                          </span>
                          <span className="max-w-[150px] truncate text-sm">
                            {healthStatus.lastError || 'None'}
                          </span>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/connect`,
                              )
                            }
                          >
                            <Settings className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.manageConnection">
                              Manage Connection
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Mapping Overview Card */}
                <MappingOverviewCard
                  accountId={account?.slug as string}
                  teamAccountId={account?.id as string}
                />

                {/* Recent Activity Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 pb-2 dark:from-blue-950/30 dark:to-cyan-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Activity className="h-5 w-5 text-blue-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.recentActivity">
                          Recent Activity
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.recentActivityTooltip">
                                Recent ZNS notification activity
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewActivity">
                            Connect to ZNS to view recent activity.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {accountStats.lastSentAt ? (
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-blue-700">
                                <Trans i18nKey="integrations:zns.connect.dashboard.lastMessageSent">
                                  Last Message Sent
                                </Trans>
                              </span>
                              <span className="text-sm text-blue-600">
                                {formatDistanceToNow(
                                  new Date(accountStats.lastSentAt),
                                )}{' '}
                                ago
                              </span>
                            </div>
                            <div className="rounded-lg border border-blue-100 bg-blue-50 p-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-blue-700">
                                  <Trans i18nKey="integrations:zns.connect.dashboard.deliveryRate">
                                    Delivery Rate
                                  </Trans>
                                </span>
                                <Badge
                                  variant={
                                    accountStats.deliveryRate > 90
                                      ? 'success'
                                      : 'warning'
                                  }
                                  className="px-2 py-1"
                                >
                                  {accountStats.deliveryRate}%
                                </Badge>
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() =>
                                router.push(
                                  `/home/<USER>/integrations/zns/analytics`,
                                )
                              }
                            >
                              <BarChart className="mr-1.5 h-4 w-4" />
                              <Trans i18nKey="integrations:zns.connect.dashboard.viewAnalytics">
                                View Analytics
                              </Trans>
                            </Button>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center space-y-4 py-8">
                            <p className="text-center text-sm text-gray-500">
                              <Trans i18nKey="integrations:zns.connect.dashboard.noMessagesYet">
                                No messages sent yet
                              </Trans>
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </PageBody>
    </>
  );
}
