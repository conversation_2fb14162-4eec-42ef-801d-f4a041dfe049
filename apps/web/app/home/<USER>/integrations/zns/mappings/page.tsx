'use client';

import { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import {
  ArrowRight,
  Check,
  Code,
  Info,
  MessageSquare,
  Plus,
  Settings,
  Trash2,
  X,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Skeleton } from '@kit/ui/skeleton';
import { Switch } from '@kit/ui/switch';
import { Textarea } from '@kit/ui/textarea';
import { useZnsEventHandler } from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

// Đã import TeamAccountLayoutPageHeader ở trên

// Định nghĩa các module và event type
const modules = [
  {
    id: 'orders',
    name: 'Orders',
    events: [
      { id: 'created', name: 'Order Created' },
      { id: 'updated', name: 'Order Updated' },
      { id: 'status_updated', name: 'Status Updated' },
      { id: 'payment_received', name: 'Payment Received' },
    ],
  },
  {
    id: 'education',
    name: 'Education',
    events: [
      { id: 'tuition_created', name: 'Tuition Created' },
      { id: 'class_scheduled', name: 'Class Scheduled' },
    ],
  },
  {
    id: 'marketing',
    name: 'Marketing',
    events: [
      { id: 'promotion_created', name: 'Promotion Created' },
      { id: 'birthday_reminder', name: 'Birthday Reminder' },
    ],
  },
];

// Schema cho form tạo mapping
const createMappingSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  template_id: z.string().min(1, 'Template is required'),
  module: z.string().min(1, 'Module is required'),
  event_type: z.string().min(1, 'Event type is required'),
  parameter_mapping: z.record(z.string(), z.string()),
  recipient_path: z.string().default('customer.phone'),
});

type CreateMappingFormValues = z.infer<typeof createMappingSchema>;

export default function ZnsMappingsPage() {
  const { t } = useTranslation();
  const { account: accountSlug } = useParams();
  const { account } = useTeamAccountWorkspace();
  const queryClient = useQueryClient();
  // Không sử dụng activeTab nữa, thay bằng selectedModule
  const supabase = useSupabase();

  // Sử dụng hook useZnsEventHandler để quản lý ZNS Event Handler
  const { isInitialized } = useZnsEventHandler(supabase, account?.id);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [selectedModule, setSelectedModule] = useState('orders');

  // Form cho tạo mapping
  const form = useForm<CreateMappingFormValues>({
    resolver: zodResolver(createMappingSchema),
    defaultValues: {
      name: '',
      description: '',
      template_id: '',
      module: selectedModule,
      event_type: '',
      parameter_mapping: {},
      recipient_path: 'customer.phone',
    },
  });

  // Reset form khi mở dialog
  useEffect(() => {
    if (isCreateDialogOpen) {
      form.reset({
        name: '',
        description: '',
        template_id: '',
        module: selectedModule,
        event_type: '',
        parameter_mapping: {},
        recipient_path: 'customer.phone',
      });
    }
  }, [isCreateDialogOpen, selectedModule, form]);

  // Lấy danh sách template
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates', account?.id],
    queryFn: async () => {
      if (!account?.id) return [];

      const response = await axios.get(
        `/api/zns/templates?accountId=${account.id}`,
      );
      return response.data.data;
    },
    enabled: !!account?.id,
  });

  // Lấy danh sách mapping
  const { data: mappings, isLoading: isLoadingMappings } = useQuery({
    queryKey: ['zns-mappings', selectedModule, account?.id],
    queryFn: async () => {
      if (!account?.id) return [];

      const response = await axios.get(
        `/api/zns/mappings?module=${selectedModule}&teamAccountId=${account.id}`,
      );
      return response.data.data;
    },
    enabled: !!account?.id,
  });

  // Mutation để tạo mapping
  const createMappingMutation = useMutation({
    mutationFn: async (
      data: CreateMappingFormValues & { team_account_id: string },
    ) => {
      const response = await axios.post('/api/zns/mappings', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success(
        t(
          'integrations:zns.mappings.createSuccess',
          'Mapping created successfully',
        ),
      );
      setIsCreateDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['zns-mappings'] });
      form.reset();
    },
    onError: (error: any) => {
      toast.error(
        t('integrations:zns.mappings.createError', 'Failed to create mapping'),
      );
      console.error('Error creating mapping:', error);
    },
  });

  // Mutation để cập nhật trạng thái mapping
  const updateMappingStatusMutation = useMutation({
    mutationFn: async ({
      id,
      enabled,
      team_account_id,
    }: {
      id: string;
      enabled: boolean;
      team_account_id: string;
    }) => {
      const response = await axios.patch('/api/zns/mappings', {
        id,
        enabled,
        team_account_id,
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zns-mappings'] });
    },
    onError: (error: any) => {
      toast.error(
        t('integrations:zns.mappings.updateError', 'Failed to update mapping'),
      );
      console.error('Error updating mapping:', error);
    },
  });

  // Mutation để xóa mapping
  const deleteMappingMutation = useMutation({
    mutationFn: async ({
      id,
      team_account_id,
    }: {
      id: string;
      team_account_id: string;
    }) => {
      const response = await axios.delete(
        `/api/zns/mappings?id=${id}&teamAccountId=${team_account_id}`,
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success(
        t(
          'integrations:zns.mappings.deleteSuccess',
          'Mapping deleted successfully',
        ),
      );
      queryClient.invalidateQueries({ queryKey: ['zns-mappings'] });
    },
    onError: (error: any) => {
      toast.error(
        t('integrations:zns.mappings.deleteError', 'Failed to delete mapping'),
      );
      console.error('Error deleting mapping:', error);
    },
  });

  // Xử lý khi chọn template
  const handleTemplateChange = (templateId: string) => {
    console.log('Selected template ID:', templateId);
    const template = templates?.find((t: any) => t.id === templateId);
    console.log('Found template:', template);
    setSelectedTemplate(template);

    // Reset parameter mapping
    form.setValue('parameter_mapping', {});
  };

  // Xử lý khi chọn event
  const handleEventChange = (eventId: string) => {
    const module = modules.find((m) => m.id === selectedModule);
    const event = module?.events.find((e) => e.id === eventId);
    setSelectedEvent(event);
  };

  // Xử lý khi thay đổi module
  const handleModuleChange = (value: string) => {
    setSelectedModule(value);
    form.setValue('module', value);
    form.setValue('event_type', '');
  };

  // Xử lý khi submit form
  const onSubmit = (values: CreateMappingFormValues) => {
    if (!account?.id) {
      toast.error(
        t('integrations:zns.mappings.accountError', 'Account not found'),
      );
      return;
    }

    // Log dữ liệu để debug
    console.log('Submitting form with values:', values);

    // Kiểm tra các trường bắt buộc
    if (
      !values.name ||
      !values.template_id ||
      !values.module ||
      !values.event_type
    ) {
      toast.error(
        t(
          'integrations:zns.mappings.formError',
          'Please fill in all required fields',
        ),
      );
      return;
    }

    // Gọi mutation để tạo mapping
    createMappingMutation.mutate({
      ...values,
      team_account_id: account.id,
    });
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={accountSlug as string}
        title={t('integrations:zns.mappings.title', 'ZNS Mappings')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug as string]: account?.name || accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              mappings: t('integrations:zns.mappings.title', 'Mappings'),
            }}
          />
        }
      >
        <div className="flex w-full items-center justify-end">
          <Dialog
            open={isCreateDialogOpen}
            onOpenChange={(open) => {
              setIsCreateDialogOpen(open);
              if (!open) {
                // Reset form khi đóng dialog
                setSelectedTemplate(null);
                form.reset();
              }
            }}
          >
            <DialogTrigger asChild>
              <Button className="gap-1">
                <Plus className="h-4 w-4" />
                <Trans i18nKey="integrations:zns.mappings.createNew">
                  Create New Mapping
                </Trans>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {t(
                    'integrations:zns.mappings.createNew',
                    'Create New Mapping',
                  )}
                </DialogTitle>
                <DialogDescription>
                  {t(
                    'integrations:zns.mappings.createDescription',
                    'Configure how a ZNS template is used with a specific event',
                  )}
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('integrations:zns.mappings.form.name', 'Name')}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Order confirmation notification"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t(
                            'integrations:zns.mappings.form.description',
                            'Description',
                          )}
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Send notification when a new order is created"
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="module"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t(
                              'integrations:zns.mappings.form.module',
                              'Module',
                            )}
                          </FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              handleTabChange(value);
                            }}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select module" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {modules.map((module) => (
                                <SelectItem key={module.id} value={module.id}>
                                  {module.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="event_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t(
                              'integrations:zns.mappings.form.eventType',
                              'Event Type',
                            )}
                          </FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              handleEventChange(value);
                            }}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select event" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {modules
                                .find((m) => m.id === form.getValues('module'))
                                ?.events.map((event) => (
                                  <SelectItem key={event.id} value={event.id}>
                                    {event.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="template_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t(
                            'integrations:zns.mappings.form.template',
                            'Template',
                          )}
                        </FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            handleTemplateChange(value);
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select template" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {isLoadingTemplates ? (
                              <SelectItem value="loading" disabled>
                                Loading templates...
                              </SelectItem>
                            ) : templates?.length > 0 ? (
                              templates.map((template: any) => (
                                <SelectItem
                                  key={template.id}
                                  value={template.id}
                                >
                                  {template.template_name}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="none" disabled>
                                No templates available
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="recipient_path"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t(
                            'integrations:zns.mappings.form.recipientPath',
                            'Recipient Path',
                          )}
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="customer.phone" />
                        </FormControl>
                        <FormDescription>
                          {t(
                            'integrations:zns.mappings.form.recipientPathDescription',
                            'Path to the phone number in the event data',
                          )}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {selectedTemplate && selectedTemplate.metadata?.params && (
                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">
                        {t(
                          'integrations:zns.mappings.form.parameterMapping',
                          'Parameter Mapping',
                        )}
                      </h3>
                      <div className="space-y-2">
                        {selectedTemplate.metadata.params.map(
                          (param: any, index: number) => (
                            <div key={index} className="grid grid-cols-2 gap-2">
                              <div className="flex items-center gap-2">
                                <Label className="text-sm">{param.name}</Label>
                                <Badge variant="outline" className="text-xs">
                                  {param.type}
                                </Badge>
                              </div>
                              <Input
                                placeholder="customer.name"
                                value={
                                  form.getValues(
                                    `parameter_mapping.${param.name}`,
                                  ) || ''
                                }
                                onChange={(e) => {
                                  const mapping = {
                                    ...form.getValues('parameter_mapping'),
                                  };
                                  mapping[param.name] = e.target.value;
                                  form.setValue('parameter_mapping', mapping);
                                }}
                              />
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  )}

                  <DialogFooter>
                    <Button
                      type="submit"
                      disabled={createMappingMutation.isPending}
                    >
                      {createMappingMutation.isPending ? (
                        <span className="flex items-center gap-1">
                          <Skeleton className="h-4 w-4 rounded-full" />
                          {t('common:creating')}
                        </span>
                      ) : (
                        t('common:create')
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </TeamAccountLayoutPageHeader>

      <PageBody>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="w-64">
              <Label htmlFor="module-select">
                {t('integrations:zns.mappings.form.module', 'Module')}
              </Label>
              <Select value={selectedModule} onValueChange={handleModuleChange}>
                <SelectTrigger id="module-select" className="mt-1">
                  <SelectValue placeholder="Select module" />
                </SelectTrigger>
                <SelectContent>
                  {modules.map((module) => (
                    <SelectItem key={module.id} value={module.id}>
                      {t(
                        `integrations:zns.mappings.modules.${module.id}`,
                        module.name,
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            {isLoadingMappings ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardHeader>
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-64" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : mappings?.length > 0 ? (
              <div className="space-y-4">
                {mappings
                  .filter((mapping: any) => mapping.module === selectedModule)
                  .map((mapping: any) => (
                    <Card key={mapping.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle>{mapping.name}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={mapping.enabled}
                              onCheckedChange={(checked) => {
                                if (!account?.id) return;
                                updateMappingStatusMutation.mutate({
                                  id: mapping.id,
                                  enabled: checked,
                                  team_account_id: account.id,
                                });
                              }}
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                if (!account?.id) return;
                                if (
                                  confirm(
                                    t(
                                      'integrations:zns.mappings.deleteConfirm',
                                      'Are you sure you want to delete this mapping?',
                                    ),
                                  )
                                ) {
                                  deleteMappingMutation.mutate({
                                    id: mapping.id,
                                    team_account_id: account.id,
                                  });
                                }
                              }}
                            >
                              <Trash2 className="text-destructive h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>{mapping.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-1">
                              <Label className="text-muted-foreground flex items-center gap-1 text-sm font-medium">
                                <MessageSquare className="h-3.5 w-3.5" />
                                {t(
                                  'integrations:zns.mappings.template',
                                  'Template',
                                )}
                              </Label>
                              <div className="text-sm">
                                {mapping.template?.template_name ||
                                  'Unknown template'}
                              </div>
                            </div>
                            <div className="space-y-1">
                              <Label className="text-muted-foreground flex items-center gap-1 text-sm font-medium">
                                <Code className="h-3.5 w-3.5" />
                                {t('integrations:zns.mappings.event', 'Event')}
                              </Label>
                              <div className="text-sm">
                                {module.events.find(
                                  (e) => e.id === mapping.event_type,
                                )?.name || mapping.event_type}
                              </div>
                            </div>
                          </div>

                          <div className="space-y-1">
                            <Label className="text-muted-foreground flex items-center gap-1 text-sm font-medium">
                              <Settings className="h-3.5 w-3.5" />
                              {t(
                                'integrations:zns.mappings.parameterMapping',
                                'Parameter Mapping',
                              )}
                            </Label>
                            <div className="bg-muted/50 rounded-md p-3 font-mono text-xs">
                              {Object.entries(
                                mapping.parameter_mapping || {},
                              ).map(([key, value]) => (
                                <div
                                  key={key}
                                  className="flex items-center gap-2"
                                >
                                  <span className="text-blue-600">{key}</span>
                                  <ArrowRight className="text-muted-foreground h-3 w-3" />
                                  <span className="text-green-600">
                                    {String(value)}
                                  </span>
                                </div>
                              ))}
                              {Object.keys(mapping.parameter_mapping || {})
                                .length === 0 && (
                                <span className="text-muted-foreground">
                                  No parameters mapped
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="space-y-1">
                            <Label className="text-muted-foreground flex items-center gap-1 text-sm font-medium">
                              <Info className="h-3.5 w-3.5" />
                              {t('integrations:zns.mappings.status', 'Status')}
                            </Label>
                            <div className="flex items-center gap-2">
                              {mapping.enabled ? (
                                <Badge className="border-green-200 bg-green-100 text-green-800 hover:bg-green-200">
                                  <Check className="mr-1 h-3 w-3" />
                                  {t(
                                    'integrations:zns.mappings.enabled',
                                    'Enabled',
                                  )}
                                </Badge>
                              ) : (
                                <Badge
                                  variant="outline"
                                  className="border-amber-200 bg-amber-50 text-amber-800 hover:bg-amber-100"
                                >
                                  <X className="mr-1 h-3 w-3" />
                                  {t(
                                    'integrations:zns.mappings.disabled',
                                    'Disabled',
                                  )}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>
                    {t(
                      'integrations:zns.mappings.noMappings',
                      'No mappings found',
                    )}
                  </CardTitle>
                  <CardDescription>
                    {t(
                      'integrations:zns.mappings.noMappingsDescription',
                      'Create a new mapping to configure how ZNS templates are used with this module',
                    )}
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <div className="flex justify-center">
                    <Button onClick={() => setIsCreateDialogOpen(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      <Trans i18nKey="integrations:zns.mappings.createNew">
                        Create New Mapping
                      </Trans>
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            )}
          </div>
        </div>
      </PageBody>
    </>
  );
}
