'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import {
  Calendar,
  TrendingUp,
  TrendingDown,
  MessageSquare,
  Users,
  Target,
  Clock,
  Download,
  Filter,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';

import { createClient } from '@kit/supabase/client';
import { useTeamAccountWorkspace } from '~/home/<USER>/_components/team-account-workspace-provider';
import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '~/components/app-breadcrumbs';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function ZnsAnalyticsPage() {
  const params = useParams();
  const { account } = useTeamAccountWorkspace();
  const supabase = createClient();

  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('messages');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch analytics data
  const { data: analyticsData, refetch, isLoading: isAnalyticsLoading, error } = useQuery({
    queryKey: ['zns-analytics', account?.id, timeRange],
    queryFn: async () => {
      if (!account?.id) return null;

      const now = new Date();
      let startDate: Date;

      switch (timeRange) {
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      // Fetch usage data
      const { data: usageData, error } = await supabase
        .from('zns_usage')
        .select(`
          *,
          mapping:zns_mappings(name, module, event_type),
          template:zns_templates(template_name)
        `)
        .eq('account_id', account.id)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching analytics:', error);
        return null;
      }

      return usageData || [];
    },
    enabled: !!account?.id,
  });

  // Process analytics data
  const processedData = analyticsData ? processAnalyticsData(analyticsData, timeRange) : null;

  const handleRefresh = async () => {
    setIsLoading(true);
    await refetch();
    setIsLoading(false);
  };

  const handleExport = () => {
    if (!analyticsData) return;

    const csvContent = convertToCSV(analyticsData);
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `zns-analytics-${timeRange}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account}
        title="ZNS Analytics"
        description={
          <AppBreadcrumbs
            items={[
              { label: 'Trang chủ', href: `/home/<USER>
              { label: 'Tích hợp', href: `/home/<USER>/integrations` },
              { label: 'ZNS', href: `/home/<USER>/integrations/zns` },
              { label: 'Analytics' },
            ]}
          />
        }
      />

      <div className="container mx-auto space-y-6 p-6">
        {/* Controls */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="24h">24 giờ qua</SelectItem>
                <SelectItem value="7d">7 ngày qua</SelectItem>
                <SelectItem value="30d">30 ngày qua</SelectItem>
                <SelectItem value="90d">90 ngày qua</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-[180px]">
                <BarChart3 className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="messages">Tin nhắn</SelectItem>
                <SelectItem value="success_rate">Tỷ lệ thành công</SelectItem>
                <SelectItem value="templates">Templates</SelectItem>
                <SelectItem value="mappings">Mappings</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Làm mới
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={!analyticsData?.length}
            >
              <Download className="mr-2 h-4 w-4" />
              Xuất CSV
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isAnalyticsLoading && (
          <div className="flex h-64 items-center justify-center">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
              <span className="text-lg">Đang tải dữ liệu analytics...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="flex items-center gap-2 p-6">
              <AlertCircle className="h-6 w-6 text-red-500" />
              <div>
                <h3 className="font-semibold text-red-800">Lỗi tải dữ liệu</h3>
                <p className="text-sm text-red-600">Không thể tải dữ liệu analytics. Vui lòng thử lại.</p>
              </div>
              <Button variant="outline" onClick={handleRefresh} className="ml-auto">
                Thử lại
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {!isAnalyticsLoading && !error && (!analyticsData || analyticsData.length === 0) && (
          <Card className="border-gray-200 bg-gray-50">
            <CardContent className="flex flex-col items-center gap-4 p-12 text-center">
              <BarChart3 className="h-16 w-16 text-gray-400" />
              <div>
                <h3 className="text-lg font-semibold text-gray-800">Chưa có dữ liệu analytics</h3>
                <p className="text-sm text-gray-600">
                  Bắt đầu gửi tin nhắn ZNS để xem thống kê và phân tích chi tiết.
                </p>
              </div>
              <Button
                onClick={() => window.open(`/home/<USER>/integrations/zns/send`, '_blank')}
                className="mt-2"
              >
                Gửi tin nhắn đầu tiên
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Summary Cards */}
        {!isAnalyticsLoading && !error && processedData && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm dark:from-blue-950/30 dark:to-blue-900/30">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Tổng tin nhắn
                </CardTitle>
                <MessageSquare className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {processedData.summary.totalMessages.toLocaleString()}
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  {processedData.summary.messagesTrend > 0 ? (
                    <span className="flex items-center">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      +{processedData.summary.messagesTrend}% so với kỳ trước
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <TrendingDown className="mr-1 h-3 w-3" />
                      {processedData.summary.messagesTrend}% so với kỳ trước
                    </span>
                  )}
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100 shadow-sm dark:from-green-950/30 dark:to-green-900/30">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
                  Tỷ lệ thành công
                </CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {processedData.summary.successRate}%
                </div>
                <p className="text-xs text-green-600 dark:text-green-400">
                  {processedData.summary.successMessages} thành công / {processedData.summary.totalMessages} tổng
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 shadow-sm dark:from-purple-950/30 dark:to-purple-900/30">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">
                  Templates sử dụng
                </CardTitle>
                <Target className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  {processedData.summary.uniqueTemplates}
                </div>
                <p className="text-xs text-purple-600 dark:text-purple-400">
                  Templates được sử dụng
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 shadow-sm dark:from-orange-950/30 dark:to-orange-900/30">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">
                  Thời gian phản hồi
                </CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                  {processedData.summary.avgResponseTime}ms
                </div>
                <p className="text-xs text-orange-600 dark:text-orange-400">
                  Thời gian phản hồi trung bình
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Charts */}
        {!isAnalyticsLoading && !error && processedData && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="performance">Hiệu suất</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="mappings">Mappings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Messages Over Time */}
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Tin nhắn theo thời gian
                  </CardTitle>
                  <CardDescription>
                    Số lượng tin nhắn được gửi theo thời gian
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={processedData?.timeSeriesData || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Area
                        type="monotone"
                        dataKey="messages"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Status Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChartIcon className="h-5 w-5" />
                    Phân bố trạng thái
                  </CardTitle>
                  <CardDescription>
                    Tỷ lệ thành công/thất bại
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={processedData?.statusData || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(processedData?.statusData || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Top Modules */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Module phổ biến
                  </CardTitle>
                  <CardDescription>
                    Modules được sử dụng nhiều nhất
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={processedData?.moduleData || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Success Rate Over Time */}
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Tỷ lệ thành công theo thời gian
                  </CardTitle>
                  <CardDescription>
                    Xu hướng tỷ lệ thành công gửi tin nhắn
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={processedData?.successRateData || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => [`${value}%`, 'Tỷ lệ thành công']} />
                      <Line
                        type="monotone"
                        dataKey="successRate"
                        stroke="#00C49F"
                        strokeWidth={2}
                        dot={{ fill: '#00C49F' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Error Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <XCircle className="h-5 w-5" />
                    Phân tích lỗi
                  </CardTitle>
                  <CardDescription>
                    Các loại lỗi phổ biến
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {processedData?.errorAnalysis?.map((error, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          <span className="text-sm">{error.type}</span>
                        </div>
                        <Badge variant="destructive">{error.count}</Badge>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">Không có lỗi trong khoảng thời gian này</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Peak Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Giờ cao điểm
                  </CardTitle>
                  <CardDescription>
                    Thời gian gửi tin nhiều nhất
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={processedData?.hourlyData || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#FFBB28" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Template Usage */}
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Sử dụng Template
                  </CardTitle>
                  <CardDescription>
                    Templates được sử dụng nhiều nhất
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={processedData?.templateUsage || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884D8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Template Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Hiệu suất Template
                  </CardTitle>
                  <CardDescription>
                    Tỷ lệ thành công theo template
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {processedData?.templatePerformance?.map((template, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{template.name}</span>
                          <Badge variant={template.successRate > 90 ? "default" : "secondary"}>
                            {template.successRate}%
                          </Badge>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${template.successRate}%` }}
                          ></div>
                        </div>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">Chưa có dữ liệu template</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Template Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Xu hướng Template
                  </CardTitle>
                  <CardDescription>
                    Templates đang tăng/giảm sử dụng
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {processedData?.templateTrends?.map((trend, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{trend.name}</span>
                        <div className="flex items-center gap-2">
                          {trend.change > 0 ? (
                            <TrendingUp className="h-4 w-4 text-green-500" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-500" />
                          )}
                          <span className={`text-sm ${trend.change > 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {Math.abs(trend.change)}%
                          </span>
                        </div>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">Chưa đủ dữ liệu để phân tích xu hướng</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="mappings" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Mapping Usage */}
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Sử dụng Mapping
                  </CardTitle>
                  <CardDescription>
                    Mappings được trigger nhiều nhất
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={processedData?.mappingUsage || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#FF8042" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Event Type Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChartIcon className="h-5 w-5" />
                    Phân bố Event Type
                  </CardTitle>
                  <CardDescription>
                    Các loại sự kiện được sử dụng
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={processedData?.eventTypeData || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(processedData?.eventTypeData || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Mapping Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Hiệu suất Mapping
                  </CardTitle>
                  <CardDescription>
                    Tỷ lệ thành công theo mapping
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {processedData?.mappingPerformance?.map((mapping, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{mapping.name}</span>
                          <Badge variant={mapping.successRate > 90 ? "default" : "secondary"}>
                            {mapping.successRate}%
                          </Badge>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${mapping.successRate}%` }}
                          ></div>
                        </div>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">Chưa có dữ liệu mapping</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        )}
      </div>
    </>
  );
}

// Helper functions
function processAnalyticsData(data: any[], timeRange: string) {
  // Process data for charts and summary
  const totalMessages = data.length;
  const successMessages = data.filter(d => d.status === 'success').length;
  const failedMessages = data.filter(d => d.status === 'failed').length;
  const errorMessages = data.filter(d => d.status === 'error').length;
  const successRate = totalMessages > 0 ? Math.round((successMessages / totalMessages) * 100) : 0;

  // Time series data
  const timeSeriesData = groupByTime(data, timeRange);

  // Success rate over time
  const successRateData = calculateSuccessRateOverTime(data, timeRange);

  // Status distribution
  const statusData = [
    { name: 'Thành công', value: successMessages },
    { name: 'Thất bại', value: failedMessages },
    { name: 'Lỗi', value: errorMessages },
  ].filter(item => item.value > 0);

  // Module distribution
  const moduleData = groupByModule(data);

  // Template analysis
  const templateUsage = groupByTemplate(data);
  const templatePerformance = calculateTemplatePerformance(data);
  const templateTrends = calculateTemplateTrends(data);

  // Mapping analysis
  const mappingUsage = groupByMapping(data);
  const mappingPerformance = calculateMappingPerformance(data);
  const eventTypeData = groupByEventType(data);

  // Error analysis
  const errorAnalysis = analyzeErrors(data);

  // Hourly distribution
  const hourlyData = groupByHour(data);

  // Unique templates
  const uniqueTemplates = new Set(data.map(d => d.template_id)).size;

  return {
    summary: {
      totalMessages,
      successMessages,
      successRate,
      uniqueTemplates,
      messagesTrend: 0, // TODO: Calculate trend
      avgResponseTime: 150, // TODO: Calculate from actual data
    },
    timeSeriesData,
    successRateData,
    statusData,
    moduleData,
    templateUsage,
    templatePerformance,
    templateTrends,
    mappingUsage,
    mappingPerformance,
    eventTypeData,
    errorAnalysis,
    hourlyData,
  };
}

function groupByTime(data: any[], timeRange: string) {
  // Group data by time intervals
  const grouped = new Map();

  data.forEach(item => {
    const date = new Date(item.created_at);
    let key: string;

    if (timeRange === '24h') {
      key = `${date.getHours()}:00`;
    } else {
      key = date.toLocaleDateString('vi-VN');
    }

    if (!grouped.has(key)) {
      grouped.set(key, 0);
    }
    grouped.set(key, grouped.get(key) + 1);
  });

  return Array.from(grouped.entries()).map(([date, messages]) => ({
    date,
    messages,
  }));
}

function groupByModule(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const module = item.mapping?.module || 'Unknown';
    if (!grouped.has(module)) {
      grouped.set(module, 0);
    }
    grouped.set(module, grouped.get(module) + 1);
  });

  return Array.from(grouped.entries()).map(([name, count]) => ({
    name,
    count,
  }));
}

function calculateSuccessRateOverTime(data: any[], timeRange: string) {
  const grouped = new Map();

  data.forEach(item => {
    const date = new Date(item.created_at);
    let key: string;

    if (timeRange === '24h') {
      key = `${date.getHours()}:00`;
    } else {
      key = date.toLocaleDateString('vi-VN');
    }

    if (!grouped.has(key)) {
      grouped.set(key, { total: 0, success: 0 });
    }

    const stats = grouped.get(key);
    stats.total++;
    if (item.status === 'success') {
      stats.success++;
    }
  });

  return Array.from(grouped.entries()).map(([date, stats]) => ({
    date,
    successRate: stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0,
  }));
}

function groupByTemplate(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const templateName = item.template?.template_name || 'Unknown Template';
    if (!grouped.has(templateName)) {
      grouped.set(templateName, 0);
    }
    grouped.set(templateName, grouped.get(templateName) + 1);
  });

  return Array.from(grouped.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10); // Top 10
}

function calculateTemplatePerformance(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const templateName = item.template?.template_name || 'Unknown Template';
    if (!grouped.has(templateName)) {
      grouped.set(templateName, { total: 0, success: 0 });
    }

    const stats = grouped.get(templateName);
    stats.total++;
    if (item.status === 'success') {
      stats.success++;
    }
  });

  return Array.from(grouped.entries())
    .map(([name, stats]) => ({
      name,
      successRate: stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0,
      total: stats.total,
    }))
    .filter(item => item.total >= 5) // Only templates with at least 5 messages
    .sort((a, b) => b.successRate - a.successRate);
}

function calculateTemplateTrends(data: any[]) {
  // Simple trend calculation - compare last week vs previous week
  const now = new Date();
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

  const lastWeekData = data.filter(d => new Date(d.created_at) >= lastWeek);
  const previousWeekData = data.filter(d => {
    const date = new Date(d.created_at);
    return date >= twoWeeksAgo && date < lastWeek;
  });

  const lastWeekTemplates = groupByTemplate(lastWeekData);
  const previousWeekTemplates = groupByTemplate(previousWeekData);

  return lastWeekTemplates.map(template => {
    const previousCount = previousWeekTemplates.find(t => t.name === template.name)?.count || 0;
    const change = previousCount > 0 ? Math.round(((template.count - previousCount) / previousCount) * 100) : 0;

    return {
      name: template.name,
      change,
    };
  }).slice(0, 5);
}

function groupByMapping(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const mappingName = item.mapping?.name || 'Unknown Mapping';
    if (!grouped.has(mappingName)) {
      grouped.set(mappingName, 0);
    }
    grouped.set(mappingName, grouped.get(mappingName) + 1);
  });

  return Array.from(grouped.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10); // Top 10
}

function calculateMappingPerformance(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const mappingName = item.mapping?.name || 'Unknown Mapping';
    if (!grouped.has(mappingName)) {
      grouped.set(mappingName, { total: 0, success: 0 });
    }

    const stats = grouped.get(mappingName);
    stats.total++;
    if (item.status === 'success') {
      stats.success++;
    }
  });

  return Array.from(grouped.entries())
    .map(([name, stats]) => ({
      name,
      successRate: stats.total > 0 ? Math.round((stats.success / stats.total) * 100) : 0,
      total: stats.total,
    }))
    .filter(item => item.total >= 3) // Only mappings with at least 3 messages
    .sort((a, b) => b.successRate - a.successRate);
}

function groupByEventType(data: any[]) {
  const grouped = new Map();

  data.forEach(item => {
    const eventType = item.mapping?.event_type || 'unknown';
    if (!grouped.has(eventType)) {
      grouped.set(eventType, 0);
    }
    grouped.set(eventType, grouped.get(eventType) + 1);
  });

  return Array.from(grouped.entries()).map(([name, value]) => ({ name, value }));
}

function analyzeErrors(data: any[]) {
  const errorData = data.filter(d => d.status === 'failed' || d.status === 'error');
  const grouped = new Map();

  errorData.forEach(item => {
    // Simple error categorization based on status
    const errorType = item.status === 'failed' ? 'Gửi thất bại' : 'Lỗi hệ thống';
    if (!grouped.has(errorType)) {
      grouped.set(errorType, 0);
    }
    grouped.set(errorType, grouped.get(errorType) + 1);
  });

  return Array.from(grouped.entries()).map(([type, count]) => ({ type, count }));
}

function groupByHour(data: any[]) {
  const grouped = new Map();

  // Initialize all hours
  for (let i = 0; i < 24; i++) {
    grouped.set(i, 0);
  }

  data.forEach(item => {
    const hour = new Date(item.created_at).getHours();
    grouped.set(hour, grouped.get(hour) + 1);
  });

  return Array.from(grouped.entries()).map(([hour, count]) => ({
    hour: `${hour}:00`,
    count,
  }));
}

function convertToCSV(data: any[]) {
  const headers = ['Date', 'Status', 'Module', 'Event Type', 'Template', 'Mapping'];
  const rows = data.map(item => [
    new Date(item.created_at).toLocaleString('vi-VN'),
    item.status,
    item.mapping?.module || '',
    item.mapping?.event_type || '',
    item.template?.template_name || '',
    item.mapping?.name || '',
  ]);

  return [headers, ...rows].map(row => row.join(',')).join('\n');
}
