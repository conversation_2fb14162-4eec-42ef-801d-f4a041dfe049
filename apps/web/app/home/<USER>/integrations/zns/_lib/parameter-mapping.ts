/**
 * ZNS Parameter Mapping Logic
 * Tự động map các parameters phổ biến và chỉ cho phép user config những field cần thiết
 */

export interface ParameterConfig {
  name: string;
  type: string;
  isAutoMapped: boolean;
  autoPath?: string; // Đường dẫn tự động trong data
  description: string;
  placeholder?: string;
  category: 'customer' | 'order' | 'payment' | 'system' | 'custom';
}

/**
 * Danh sách các parameters có thể tự động map
 */
export const AUTO_MAPPABLE_PARAMETERS: Record<string, ParameterConfig> = {
  // Customer parameters
  'name': {
    name: 'name',
    type: '1', // CUSTOMER_NAME
    isAutoMapped: true,
    autoPath: 'customer.name',
    description: 'Tên khách hàng',
    category: 'customer',
  },
  'customer_name': {
    name: 'customer_name',
    type: '1', // CUSTOMER_NAME
    isAutoMapped: true,
    autoPath: 'customer.name',
    description: 'Tên khách hàng',
    category: 'customer',
  },
  'phone': {
    name: 'phone',
    type: '2', // PHONE_NUMBER
    isAutoMapped: true,
    autoPath: 'customer.phone',
    description: 'Số điện thoại khách hàng',
    category: 'customer',
  },
  'customer_phone': {
    name: 'customer_phone',
    type: '2', // PHONE_NUMBER
    isAutoMapped: true,
    autoPath: 'customer.phone',
    description: 'Số điện thoại khách hàng',
    category: 'customer',
  },
  'email': {
    name: 'email',
    type: '3', // EMAIL
    isAutoMapped: true,
    autoPath: 'customer.email',
    description: 'Email khách hàng',
    category: 'customer',
  },

  // Order parameters
  'order_id': {
    name: 'order_id',
    type: '4', // ORDER_CODE
    isAutoMapped: true,
    autoPath: 'order.id',
    description: 'Mã đơn hàng',
    category: 'order',
  },
  'order_code': {
    name: 'order_code',
    type: '4', // ORDER_CODE
    isAutoMapped: true,
    autoPath: 'order.code',
    description: 'Mã đơn hàng',
    category: 'order',
  },
  'order_total': {
    name: 'order_total',
    type: '5', // PRICE
    isAutoMapped: true,
    autoPath: 'order.total',
    description: 'Tổng tiền đơn hàng',
    category: 'order',
  },
  'total_amount': {
    name: 'total_amount',
    type: '5', // PRICE
    isAutoMapped: true,
    autoPath: 'order.total',
    description: 'Tổng tiền',
    category: 'order',
  },
  'order_date': {
    name: 'order_date',
    type: '6', // DATE
    isAutoMapped: true,
    autoPath: 'order.created_at',
    description: 'Ngày đặt hàng',
    category: 'order',
  },
  'delivery_date': {
    name: 'delivery_date',
    type: '6', // DATE
    isAutoMapped: true,
    autoPath: 'order.delivery_date',
    description: 'Ngày giao hàng',
    category: 'order',
  },

  // Payment parameters
  'payment_method': {
    name: 'payment_method',
    type: '7', // PAYMENT_METHOD
    isAutoMapped: true,
    autoPath: 'payment.method',
    description: 'Phương thức thanh toán',
    category: 'payment',
  },
  'transaction_id': {
    name: 'transaction_id',
    type: '8', // TRANSACTION_ID
    isAutoMapped: true,
    autoPath: 'payment.transaction_id',
    description: 'Mã giao dịch',
    category: 'payment',
  },

  // System parameters
  'company_name': {
    name: 'company_name',
    type: '9', // COMPANY_NAME
    isAutoMapped: true,
    autoPath: 'system.company_name',
    description: 'Tên công ty',
    category: 'system',
  },
  'website': {
    name: 'website',
    type: '10', // WEBSITE
    isAutoMapped: true,
    autoPath: 'system.website',
    description: 'Website',
    category: 'system',
  },
};

/**
 * Các parameters cần user tự config (không thể auto-map)
 */
export const MANUAL_PARAMETERS: Record<string, ParameterConfig> = {
  'bank_name': {
    name: 'bank_name',
    type: '15', // BANK_TRANSFER_NOTE
    isAutoMapped: false,
    description: 'Tên ngân hàng',
    placeholder: 'Vietcombank',
    category: 'custom',
  },
  'bank_account': {
    name: 'bank_account',
    type: '15', // BANK_TRANSFER_NOTE
    isAutoMapped: false,
    description: 'Số tài khoản ngân hàng',
    placeholder: '**********',
    category: 'custom',
  },
  'support_phone': {
    name: 'support_phone',
    type: '2', // PHONE_NUMBER
    isAutoMapped: false,
    description: 'Số điện thoại hỗ trợ',
    placeholder: '1900-xxxx',
    category: 'custom',
  },
  'support_email': {
    name: 'support_email',
    type: '3', // EMAIL
    isAutoMapped: false,
    description: 'Email hỗ trợ',
    placeholder: '<EMAIL>',
    category: 'custom',
  },
  'store_address': {
    name: 'store_address',
    type: '11', // ADDRESS
    isAutoMapped: false,
    description: 'Địa chỉ cửa hàng',
    placeholder: '123 Đường ABC, Quận XYZ',
    category: 'custom',
  },
  'promotion_code': {
    name: 'promotion_code',
    type: '12', // PROMOTION_CODE
    isAutoMapped: false,
    description: 'Mã khuyến mãi',
    placeholder: 'SALE20',
    category: 'custom',
  },
  'custom_message': {
    name: 'custom_message',
    type: '13', // CUSTOM_LABEL
    isAutoMapped: false,
    description: 'Thông điệp tùy chỉnh',
    placeholder: 'Cảm ơn bạn đã mua hàng',
    category: 'custom',
  },
};

/**
 * Phân loại parameter thành auto hoặc manual
 */
export function categorizeParameter(paramName: string): ParameterConfig | null {
  const normalizedName = paramName.toLowerCase().trim();
  
  // Kiểm tra auto-mappable parameters
  if (AUTO_MAPPABLE_PARAMETERS[normalizedName]) {
    return AUTO_MAPPABLE_PARAMETERS[normalizedName];
  }
  
  // Kiểm tra manual parameters
  if (MANUAL_PARAMETERS[normalizedName]) {
    return MANUAL_PARAMETERS[normalizedName];
  }
  
  // Kiểm tra pattern matching cho auto parameters
  for (const [key, config] of Object.entries(AUTO_MAPPABLE_PARAMETERS)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return { ...config, name: paramName }; // Giữ tên gốc
    }
  }
  
  // Nếu không match, coi như manual parameter
  return {
    name: paramName,
    type: '13', // CUSTOM_LABEL
    isAutoMapped: false,
    description: `Tham số tùy chỉnh: ${paramName}`,
    placeholder: `Nhập giá trị cho ${paramName}`,
    category: 'custom',
  };
}

/**
 * Tạo parameter mapping tự động cho template
 */
export function generateAutoParameterMapping(templateParams: any[]): Record<string, string> {
  const mapping: Record<string, string> = {};
  
  templateParams.forEach((param) => {
    const config = categorizeParameter(param.name);
    if (config && config.isAutoMapped && config.autoPath) {
      mapping[param.name] = config.autoPath;
    }
  });
  
  return mapping;
}

/**
 * Lấy danh sách parameters cần user config
 */
export function getManualParameters(templateParams: any[]): ParameterConfig[] {
  return templateParams
    .map((param) => categorizeParameter(param.name))
    .filter((config): config is ParameterConfig => config !== null && !config.isAutoMapped);
}

/**
 * Lấy danh sách parameters đã được auto-map
 */
export function getAutoMappedParameters(templateParams: any[]): ParameterConfig[] {
  return templateParams
    .map((param) => categorizeParameter(param.name))
    .filter((config): config is ParameterConfig => config !== null && config.isAutoMapped);
}

/**
 * Validate parameter mapping
 */
export function validateParameterMapping(
  templateParams: any[],
  parameterMapping: Record<string, string>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  templateParams.forEach((param) => {
    const config = categorizeParameter(param.name);
    if (!config) return;
    
    if (!config.isAutoMapped) {
      // Manual parameter phải có giá trị
      if (!parameterMapping[param.name] || parameterMapping[param.name].trim() === '') {
        errors.push(`Tham số "${param.name}" cần được cấu hình`);
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
