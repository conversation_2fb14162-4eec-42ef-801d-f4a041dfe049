'use client';

import { useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { PageBody } from '@kit/ui/page';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

export default function ZnsPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);

  // Redirect to dashboard
  useEffect(() => {
    router.push(`/home/<USER>/integrations/zns/dashboard`);
  }, [accountSlug, router]);

  // Render loading state while redirecting
  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t('integrations:zns.title')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS'
            }}
          />
        }
        account={accountSlug}
      />
      <PageBody data-testid="zns-page">
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center gap-4">
            <div className="animate-spin">
              <RefreshCw className="h-8 w-8 text-primary" />
            </div>
            <p className="text-muted-foreground">
              {t('common:redirecting')}
            </p>
          </div>
        </div>
      </PageBody>
    </>
  );
}
