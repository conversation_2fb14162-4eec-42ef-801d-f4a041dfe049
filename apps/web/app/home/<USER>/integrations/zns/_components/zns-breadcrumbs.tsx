import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ZNSBreadcrumbsProps {
  accountSlug: string;
}

export function ZNSBreadcrumbs({ accountSlug }: ZNSBreadcrumbsProps) {
  const { t } = useTranslation(['integrations']);
  const pathname = usePathname();

  // Determine current page
  const isConnect = pathname.includes('/connect');
  const isDashboard = pathname.includes('/dashboard');
  const isTemplates = pathname.includes('/templates');
  const isTemplateView = pathname.includes('/templates/view');
  const isTemplateEdit = pathname.includes('/templates/edit');
  const isTemplateCreate = pathname.includes('/templates/create');
  const isSend = pathname.includes('/send');
  const isAnalytics = pathname.includes('/analytics');

  return (
    <div className="flex items-center text-sm text-muted-foreground">
      <Link
        href={`/home/<USER>/integrations`}
        className="hover:text-foreground"
      >
        {t('integrations:integrations')}
      </Link>
      <ChevronRight className="mx-1 h-4 w-4" />
      <Link
        href={`/home/<USER>/integrations/zns/dashboard`}
        className="hover:text-foreground"
      >
        {t('integrations:zns.name')}
      </Link>
      <ChevronRight className="mx-1 h-4 w-4" />
      {isConnect && (
        <span className="font-medium text-foreground">
          {t('integrations:zns.connect.title')}
        </span>
      )}
      {isDashboard && (
        <span className="font-medium text-foreground">
          {t('integrations:zns.dashboard.title')}
        </span>
      )}
      {isTemplates && !isTemplateView && !isTemplateEdit && !isTemplateCreate && (
        <span className="font-medium text-foreground">
          {t('integrations:zns.templates.title')}
        </span>
      )}
      {isTemplateView && (
        <>
          <Link
            href={`/home/<USER>/integrations/zns/templates`}
            className="hover:text-foreground"
          >
            {t('integrations:zns.templates.title')}
          </Link>
          <ChevronRight className="mx-1 h-4 w-4" />
          <span className="font-medium text-foreground">
            {t('integrations:zns.templates.viewTemplate')}
          </span>
        </>
      )}
      {isTemplateEdit && (
        <>
          <Link
            href={`/home/<USER>/integrations/zns/templates`}
            className="hover:text-foreground"
          >
            {t('integrations:zns.templates.title')}
          </Link>
          <ChevronRight className="mx-1 h-4 w-4" />
          <span className="font-medium text-foreground">
            {t('integrations:zns.templates.editTemplate')}
          </span>
        </>
      )}
      {isTemplateCreate && (
        <>
          <Link
            href={`/home/<USER>/integrations/zns/templates`}
            className="hover:text-foreground"
          >
            {t('integrations:zns.templates.title')}
          </Link>
          <ChevronRight className="mx-1 h-4 w-4" />
          <span className="font-medium text-foreground">
            {t('integrations:zns.templates.createTemplate')}
          </span>
        </>
      )}
      {isSend && (
        <span className="font-medium text-foreground">
          {t('integrations:zns.send.title')}
        </span>
      )}
      {isAnalytics && (
        <span className="font-medium text-foreground">
          {t('integrations:zns.analytics.title')}
        </span>
      )}
    </div>
  );
}
