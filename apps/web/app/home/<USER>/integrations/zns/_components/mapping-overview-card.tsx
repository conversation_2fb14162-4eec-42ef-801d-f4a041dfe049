'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { PlusCircle, Layers } from 'lucide-react';
import axios from 'axios';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Skeleton } from '@kit/ui/skeleton';
import { Switch } from '@kit/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Badge } from '@kit/ui/badge';

import { CreateMappingDialog } from './create-mapping-dialog';

interface MappingOverviewCardProps {
  accountId: string;
  teamAccountId: string;
}

export function MappingOverviewCard({ accountId, teamAccountId }: MappingOverviewCardProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const supabase = useSupabase();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Fetch top mappings
  const { data: topMappings, isLoading } = useQuery({
    queryKey: ['zns-top-mappings', teamAccountId],
    queryFn: async () => {
      // Fetch mappings with usage count
      const { data: mappings, error } = await supabase
        .from('zns_mappings')
        .select(`
          id, 
          name, 
          module, 
          event_type, 
          enabled,
          template:zns_templates(template_name)
        `)
        .eq('team_account_id', teamAccountId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      // Fetch usage count for each mapping
      const mappingsWithUsage = await Promise.all(
        mappings.map(async (mapping) => {
          const { count } = await supabase
            .from('zns_usage')
            .select('id', { count: 'exact', head: true })
            .eq('mapping_id', mapping.id);

          return {
            ...mapping,
            usage_count: count || 0
          };
        })
      );

      return mappingsWithUsage;
    },
    enabled: !!teamAccountId,
  });

  // Mutation to update mapping status
  const updateMappingStatusMutation = useMutation({
    mutationFn: async ({ id, enabled }: { id: string; enabled: boolean }) => {
      const response = await axios.patch('/api/zns/mappings', {
        id,
        enabled,
        team_account_id: teamAccountId,
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['zns-top-mappings'] });
      toast.success(t('integrations:zns.mappings.updateSuccess', 'Cập nhật mapping thành công'));
    },
    onError: (error: any) => {
      toast.error(t('integrations:zns.mappings.updateError', 'Không thể cập nhật mapping'));
      console.error('Error updating mapping:', error);
    },
  });

  const handleViewAll = () => {
    router.push(`/home/<USER>/integrations/zns/mappings`);
  };

  const getModuleLabel = (module: string) => {
    const moduleLabels: Record<string, string> = {
      orders: t('integrations:zns.mappings.modules.orders', 'Đơn hàng'),
      education: t('integrations:zns.mappings.modules.education', 'Giáo dục'),
      marketing: t('integrations:zns.mappings.modules.marketing', 'Marketing'),
    };
    return moduleLabels[module] || module;
  };

  const getEventLabel = (module: string, event: string) => {
    const eventLabels: Record<string, Record<string, string>> = {
      orders: {
        created: t('integrations:zns.mappings.events.orders.created', 'Tạo đơn hàng'),
        updated: t('integrations:zns.mappings.events.orders.updated', 'Cập nhật đơn hàng'),
        status_updated: t('integrations:zns.mappings.events.orders.status_updated', 'Cập nhật trạng thái'),
        payment_received: t('integrations:zns.mappings.events.orders.payment_received', 'Nhận thanh toán'),
      },
      education: {
        tuition_created: t('integrations:zns.mappings.events.education.tuition_created', 'Tạo học phí'),
        class_scheduled: t('integrations:zns.mappings.events.education.class_scheduled', 'Lịch học'),
      },
      marketing: {
        promotion_created: t('integrations:zns.mappings.events.marketing.promotion_created', 'Tạo khuyến mãi'),
        birthday_reminder: t('integrations:zns.mappings.events.marketing.birthday_reminder', 'Nhắc sinh nhật'),
      },
    };
    return eventLabels[module]?.[event] || event;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-xl flex items-center gap-2">
            <Layers className="h-5 w-5 text-primary" />
            {t('integrations:zns.mappings.overviewCard.title', 'Mapping Overview')}
          </CardTitle>
          <CardDescription>
            {t('integrations:zns.mappings.overviewCard.description', 'Các mapping gần đây')}
          </CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={handleViewAll}>
          {t('integrations:zns.mappings.overviewCard.viewAll', 'Xem tất cả')}
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <div className="flex items-center gap-4">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-10 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        ) : topMappings && topMappings.length > 0 ? (
          <div className="space-y-4">
            {topMappings.map((mapping) => (
              <div key={mapping.id} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="font-medium">{mapping.name}</div>
                  <div className="text-xs text-muted-foreground flex items-center gap-1">
                    <Badge variant="outline" className="text-xs px-1 py-0">
                      {getModuleLabel(mapping.module)}
                    </Badge>
                    <span>•</span>
                    <span>{getEventLabel(mapping.module, mapping.event_type)}</span>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-sm text-right">
                    <div>{mapping.usage_count}</div>
                    <div className="text-xs text-muted-foreground">
                      {t('integrations:zns.mappings.overviewCard.messages', 'tin nhắn')}
                    </div>
                  </div>
                  <Switch
                    checked={mapping.enabled}
                    onCheckedChange={(checked) => {
                      updateMappingStatusMutation.mutate({
                        id: mapping.id,
                        enabled: checked,
                      });
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-8 text-center">
            <p className="text-muted-foreground">
              {t('integrations:zns.mappings.overviewCard.noMappings', 'Chưa có mapping nào')}
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="w-full">
              <PlusCircle className="mr-2 h-4 w-4" />
              {t('integrations:zns.mappings.overviewCard.createNew', 'Tạo mapping mới')}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>
                {t('integrations:zns.mappings.createNew', 'Tạo mapping mới')}
              </DialogTitle>
              <DialogDescription>
                {t('integrations:zns.mappings.createDescription', 'Cấu hình cách sử dụng mẫu tin ZNS với một sự kiện cụ thể')}
              </DialogDescription>
            </DialogHeader>
            <CreateMappingDialog 
              teamAccountId={teamAccountId} 
              onSuccess={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
}
