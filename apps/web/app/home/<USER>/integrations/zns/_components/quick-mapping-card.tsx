'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { PlusCircle, Layers } from 'lucide-react';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Skeleton } from '@kit/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';

import { CreateMappingDialog } from './create-mapping-dialog';

interface QuickMappingCardProps {
  accountId: string;
  teamAccountId: string;
}

export function QuickMappingCard({ accountId, teamAccountId }: QuickMappingCardProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const supabase = useSupabase();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Fetch mapping statistics
  const { data: mappingStats, isLoading } = useQuery({
    queryKey: ['zns-mapping-stats', teamAccountId],
    queryFn: async () => {
      // Fetch all mappings
      const { data: mappings, error } = await supabase
        .from('zns_mappings')
        .select('id, enabled, module')
        .eq('team_account_id', teamAccountId);

      if (error) throw error;

      // Calculate statistics
      const totalCount = mappings?.length || 0;
      const activeCount = mappings?.filter(m => m.enabled).length || 0;
      const modules = new Set(mappings?.map(m => m.module));
      const moduleCount = modules.size;

      return { totalCount, activeCount, moduleCount };
    },
    enabled: !!teamAccountId,
  });

  const handleViewAll = () => {
    router.push(`/home/<USER>/integrations/zns/mappings`);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <div className="flex-1">
          <CardTitle className="text-xl flex items-center gap-2">
            <Layers className="h-5 w-5 text-primary" />
            {t('integrations:zns.mappings.quickCard.title', 'ZNS Mappings')}
          </CardTitle>
          <CardDescription>
            {t('integrations:zns.mappings.quickCard.description', 'Kết nối mẫu tin ZNS với các sự kiện trong hệ thống')}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">
                {mappingStats?.activeCount} / {mappingStats?.totalCount}
              </div>
            )}
            <div className="text-sm text-muted-foreground">
              {t('integrations:zns.mappings.quickCard.activeMappings', 'Mapping đang hoạt động')}
            </div>
          </div>
          <div className="space-y-2">
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{mappingStats?.moduleCount}</div>
            )}
            <div className="text-sm text-muted-foreground">
              {t('integrations:zns.mappings.quickCard.connectedModules', 'Module được kết nối')}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handleViewAll}>
          {t('integrations:zns.mappings.quickCard.viewAll', 'Xem tất cả')}
        </Button>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              {t('integrations:zns.mappings.quickCard.createNew', 'Tạo mapping mới')}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {t('integrations:zns.mappings.createNew', 'Tạo mapping mới')}
              </DialogTitle>
              <DialogDescription>
                {t('integrations:zns.mappings.createDescription', 'Cấu hình cách sử dụng mẫu tin ZNS với một sự kiện cụ thể')}
              </DialogDescription>
            </DialogHeader>
            <CreateMappingDialog
              teamAccountId={teamAccountId}
              onSuccess={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
}
