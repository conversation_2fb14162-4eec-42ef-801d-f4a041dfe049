'use client';

import { useParams, usePathname } from 'next/navigation';
import Link from 'next/link';
import { MessageSquare, Settings, Send, PlusCircle, Layers } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
} from '@kit/ui/shadcn-sidebar';

export function ZnsSidebar() {
  const { account } = useParams();
  const pathname = usePathname();
  const { t } = useTranslation(['integrations', 'common']);

  const basePath = `/home/<USER>/integrations/zns`;
  const menuItems = [
    {
      path: `${basePath}/connect`,
      label: t('integrations:zns.sidebar.connect', 'Connect'),
      icon: <Settings className="h-4 w-4" />,
    },
    {
      path: `${basePath}/templates`,
      label: t('integrations:zns.sidebar.templates', 'Templates'),
      icon: <MessageSquare className="h-4 w-4" />,
    },
    {
      path: `${basePath}/templates/create`,
      label: t('integrations:zns.sidebar.createTemplate', 'Create Template'),
      icon: <PlusCircle className="h-4 w-4" />,
    },
    {
      path: `${basePath}/mappings`,
      label: t('integrations:zns.sidebar.mappings', 'Mappings'),
      icon: <Layers className="h-4 w-4" />,
    },
    {
      path: `${basePath}/send`,
      label: t('integrations:zns.sidebar.send', 'Send Message'),
      icon: <Send className="h-4 w-4" />,
    },
  ];

  return (
    <Sidebar collapsible="icon" className="border-r">
      <SidebarHeader className="p-4">
        <h2 className="text-lg font-semibold">
          {t('integrations:zns.title', 'Zalo Notification Service')}
        </h2>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>
            {t('integrations:zns.sidebar.menu', 'Menu')}
          </SidebarGroupLabel>

          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuButton
                  key={item.path}
                  isActive={pathname === item.path}
                  asChild
                >
                  <Link className="flex gap-2.5" href={item.path}>
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                </SidebarMenuButton>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
