'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import axios from 'axios';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { AlertCircle, CheckCircle, Lock, Settings } from 'lucide-react';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Label } from '@kit/ui/label';
import { Skeleton } from '@kit/ui/skeleton';

import {
  categorizeParameter,
  generateAutoParameterMapping,
  getManualParameters,
  getAutoMappedParameters,
  validateParameterMapping,
  type ParameterConfig,
} from '../_lib/parameter-mapping';

import { ZNS_EVENT_TYPES, type ZnsEventType } from '@kit/zns';

// Định nghĩa các module và event type
const modules = [
  {
    id: 'orders',
    name: 'Orders',
    events: [
      { id: 'created', name: 'Order Created' },
      { id: 'updated', name: 'Order Updated' },
      { id: 'status_updated', name: 'Status Updated' },
      { id: 'payment_received', name: 'Payment Received' }
    ]
  },
  {
    id: 'education',
    name: 'Education',
    events: [
      { id: 'tuition_created', name: 'Tuition Created' },
      { id: 'class_scheduled', name: 'Class Scheduled' }
    ]
  },
  {
    id: 'marketing',
    name: 'Marketing',
    events: [
      { id: 'promotion_created', name: 'Promotion Created' },
      { id: 'birthday_reminder', name: 'Birthday Reminder' }
    ]
  }
];

// Schema cho form tạo mapping
const createMappingSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  template_id: z.string().min(1, 'Template is required'),
  module: z.string().min(1, 'Module is required'),
  event_type: z.string().min(1, 'Event type is required'),
  parameter_mapping: z.record(z.string(), z.string()),
  recipient_path: z.string().default('customer.phone'),
});

type CreateMappingFormValues = z.infer<typeof createMappingSchema>;

interface CreateMappingDialogProps {
  teamAccountId: string;
  onSuccess?: () => void;
}

export function CreateMappingDialog({ teamAccountId, onSuccess }: CreateMappingDialogProps) {
  const { t } = useTranslation();
  const supabase = useSupabase();
  const queryClient = useQueryClient();
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [autoParameters, setAutoParameters] = useState<ParameterConfig[]>([]);
  const [manualParameters, setManualParameters] = useState<ParameterConfig[]>([]);

  // Form cho tạo mapping
  const form = useForm<CreateMappingFormValues>({
    resolver: zodResolver(createMappingSchema),
    defaultValues: {
      name: '',
      description: '',
      template_id: '',
      module: 'orders',
      event_type: '',
      parameter_mapping: {},
      recipient_path: 'customer.phone',
    },
  });

  // Lấy danh sách template (chỉ enabled templates cho mapping)
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates-for-mapping', teamAccountId],
    queryFn: async () => {
      if (!teamAccountId) return [];

      const response = await axios.get(
        `/api/zns/templates?accountId=${teamAccountId}&forMapping=true`,
      );
      return response.data.data;
    },
    enabled: !!teamAccountId,
  });

  // Mutation để tạo mapping
  const createMappingMutation = useMutation({
    mutationFn: async (data: CreateMappingFormValues & { team_account_id: string }) => {
      const response = await axios.post('/api/zns/mappings', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success(t('integrations:zns.mappings.createSuccess', 'Mapping created successfully'));
      queryClient.invalidateQueries({ queryKey: ['zns-mapping-stats'] });
      queryClient.invalidateQueries({ queryKey: ['zns-top-mappings'] });
      form.reset();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(t('integrations:zns.mappings.createError', 'Failed to create mapping'));
      console.error('Error creating mapping:', error);
    },
  });

  // Xử lý khi chọn template
  const handleTemplateChange = (templateId: string) => {
    const template = templates?.find((t: any) => t.id === templateId);
    setSelectedTemplate(template);

    if (template?.metadata?.params) {
      // Phân loại parameters thành auto và manual
      const autoParams = getAutoMappedParameters(template.metadata.params);
      const manualParams = getManualParameters(template.metadata.params);

      setAutoParameters(autoParams);
      setManualParameters(manualParams);

      // Tự động tạo mapping cho auto parameters
      const autoMapping = generateAutoParameterMapping(template.metadata.params);

      // Reset và set parameter mapping với auto mapping
      form.setValue('parameter_mapping', autoMapping);

      console.log('Template parameters categorized:', {
        total: template.metadata.params.length,
        auto: autoParams.length,
        manual: manualParams.length,
        autoMapping,
      });
    } else {
      // Reset nếu không có parameters
      setAutoParameters([]);
      setManualParameters([]);
      form.setValue('parameter_mapping', {});
    }
  };

  // Xử lý khi submit form
  const onSubmit = (values: CreateMappingFormValues) => {
    // Validate parameter mapping
    if (selectedTemplate?.metadata?.params) {
      const validation = validateParameterMapping(
        selectedTemplate.metadata.params,
        values.parameter_mapping
      );

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          toast.error(error);
        });
        return;
      }
    }

    createMappingMutation.mutate({
      ...values,
      team_account_id: teamAccountId,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Thông tin cơ bản</h3>

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.name', 'Tên')}
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Order confirmation notification" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.description', 'Mô tả')}
                </FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Send notification when a new order is created"
                    value={field.value || ''}
                    rows={2}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Event Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Cấu hình sự kiện</h3>

          <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="module"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.module', 'Module')}
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue('event_type', '');
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select module" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {modules.map((module) => (
                      <SelectItem key={module.id} value={module.id}>
                        {t(`integrations:zns.mappings.modules.${module.id}`, module.name)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.eventType', 'Loại sự kiện')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select event" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {modules
                      .find((m) => m.id === form.getValues('module'))
                      ?.events.map((event) => (
                        <SelectItem key={event.id} value={event.id}>
                          {t(`integrations:zns.mappings.events.${form.getValues('module')}.${event.id}`, event.name)}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          </div>
        </div>

        {/* Template Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Cấu hình template</h3>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="template_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('integrations:zns.mappings.form.template', 'Mẫu tin')}
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleTemplateChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingTemplates ? (
                        <SelectItem value="loading" disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates?.length > 0 ? (
                        templates.map((template: any) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.template_name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No templates available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="recipient_path"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('integrations:zns.mappings.form.recipientPath', 'Đường dẫn đến số điện thoại')}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="customer.phone" />
                  </FormControl>
                  <FormDescription className="text-xs">
                    {t('integrations:zns.mappings.form.recipientPathDescription', 'Đường dẫn đến số điện thoại trong dữ liệu sự kiện')}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {selectedTemplate && (autoParameters.length > 0 || manualParameters.length > 0) && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Parameter Mapping
            </h3>

            {/* Auto-mapped Parameters */}
            {autoParameters.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <h4 className="text-sm font-medium text-green-700">
                    Tự động ánh xạ ({autoParameters.length})
                  </h4>
                </div>
                <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                  {autoParameters.map((param, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                      <div className="flex items-center gap-2">
                        <Lock className="h-3.5 w-3.5 text-green-600" />
                        <span className="text-sm font-medium">{param.name}</span>
                        <Badge variant="outline" className="text-xs bg-green-100 text-green-700 border-green-300">
                          {param.type}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <code className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                          {param.autoPath}
                        </code>
                        <Badge variant="outline" className="text-xs bg-green-100 text-green-600 border-green-300">
                          Auto
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Manual Parameters */}
            {manualParameters.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <h4 className="text-sm font-medium text-orange-700">
                    Cần cấu hình thủ công ({manualParameters.length})
                  </h4>
                </div>
                <div className="grid grid-cols-1 gap-3">
                  {manualParameters.map((param, index) => (
                    <div key={index} className="p-3 bg-orange-50 rounded border border-orange-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Settings className="h-3.5 w-3.5 text-orange-600" />
                        <Label className="text-sm font-medium">{param.name}</Label>
                        <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-300">
                          {param.type}
                        </Badge>
                        <Badge variant="outline" className="text-xs bg-orange-100 text-orange-600 border-orange-300">
                          Manual
                        </Badge>
                      </div>
                      <Input
                        placeholder={param.placeholder || `Nhập giá trị cho ${param.name}`}
                        value={form.getValues(`parameter_mapping.${param.name}`) || ''}
                        onChange={(e) => {
                          const mapping = { ...form.getValues('parameter_mapping') };
                          mapping[param.name] = e.target.value;
                          form.setValue('parameter_mapping', mapping);
                        }}
                        className="border-orange-300 focus:border-orange-500 mb-1"
                      />
                      <p className="text-xs text-orange-600">{param.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Summary */}
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <h4 className="text-sm font-medium text-blue-700">Tóm tắt</h4>
              </div>
              <div className="text-xs text-blue-600 grid grid-cols-2 gap-2">
                <p>• <strong>{autoParameters.length}</strong> tham số tự động</p>
                <p>• <strong>{manualParameters.length}</strong> tham số thủ công</p>
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end pt-4 border-t">
          <Button type="submit" disabled={createMappingMutation.isPending} size="lg">
            {createMappingMutation.isPending ? (
              <span className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                {t('common:creating')}
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                {t('common:create')}
              </span>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
