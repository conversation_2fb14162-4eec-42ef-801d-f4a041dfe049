'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import axios from 'axios';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Label } from '@kit/ui/label';
import { Skeleton } from '@kit/ui/skeleton';

// Đ<PERSON>nh nghĩa các module và event type
const modules = [
  {
    id: 'orders',
    name: 'Orders',
    events: [
      { id: 'created', name: 'Order Created' },
      { id: 'updated', name: 'Order Updated' },
      { id: 'status_updated', name: 'Status Updated' },
      { id: 'payment_received', name: 'Payment Received' }
    ]
  },
  {
    id: 'education',
    name: 'Education',
    events: [
      { id: 'tuition_created', name: 'Tuition Created' },
      { id: 'class_scheduled', name: 'Class Scheduled' }
    ]
  },
  {
    id: 'marketing',
    name: 'Marketing',
    events: [
      { id: 'promotion_created', name: 'Promotion Created' },
      { id: 'birthday_reminder', name: 'Birthday Reminder' }
    ]
  }
];

// Schema cho form tạo mapping
const createMappingSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  template_id: z.string().min(1, 'Template is required'),
  module: z.string().min(1, 'Module is required'),
  event_type: z.string().min(1, 'Event type is required'),
  parameter_mapping: z.record(z.string(), z.string()),
  recipient_path: z.string().default('customer.phone'),
});

type CreateMappingFormValues = z.infer<typeof createMappingSchema>;

interface CreateMappingDialogProps {
  teamAccountId: string;
  onSuccess?: () => void;
}

export function CreateMappingDialog({ teamAccountId, onSuccess }: CreateMappingDialogProps) {
  const { t } = useTranslation();
  const supabase = useSupabase();
  const queryClient = useQueryClient();
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  // Form cho tạo mapping
  const form = useForm<CreateMappingFormValues>({
    resolver: zodResolver(createMappingSchema),
    defaultValues: {
      name: '',
      description: '',
      template_id: '',
      module: 'orders',
      event_type: '',
      parameter_mapping: {},
      recipient_path: 'customer.phone',
    },
  });

  // Lấy danh sách template
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates', teamAccountId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('zns_templates')
        .select('*')
        .eq('account_id', teamAccountId)
        .in('status', ['ENABLE', 'PENDING_REVIEW']) // Cho phép cả template đã approved và đang pending để test
        .eq('enabled', true); // Chỉ lấy template đã được enable

      if (error) throw error;
      return data;
    },
    enabled: !!teamAccountId,
  });

  // Mutation để tạo mapping
  const createMappingMutation = useMutation({
    mutationFn: async (data: CreateMappingFormValues & { team_account_id: string }) => {
      const response = await axios.post('/api/zns/mappings', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success(t('integrations:zns.mappings.createSuccess', 'Mapping created successfully'));
      queryClient.invalidateQueries({ queryKey: ['zns-mapping-stats'] });
      queryClient.invalidateQueries({ queryKey: ['zns-top-mappings'] });
      form.reset();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(t('integrations:zns.mappings.createError', 'Failed to create mapping'));
      console.error('Error creating mapping:', error);
    },
  });

  // Xử lý khi chọn template
  const handleTemplateChange = (templateId: string) => {
    const template = templates?.find((t: any) => t.id === templateId);
    setSelectedTemplate(template);

    // Reset parameter mapping
    form.setValue('parameter_mapping', {});
  };

  // Xử lý khi submit form
  const onSubmit = (values: CreateMappingFormValues) => {
    createMappingMutation.mutate({
      ...values,
      team_account_id: teamAccountId,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t('integrations:zns.mappings.form.name', 'Tên')}
              </FormLabel>
              <FormControl>
                <Input {...field} placeholder="Order confirmation notification" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t('integrations:zns.mappings.form.description', 'Mô tả')}
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Send notification when a new order is created"
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="module"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.module', 'Module')}
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue('event_type', '');
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select module" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {modules.map((module) => (
                      <SelectItem key={module.id} value={module.id}>
                        {t(`integrations:zns.mappings.modules.${module.id}`, module.name)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.eventType', 'Loại sự kiện')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select event" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {modules
                      .find((m) => m.id === form.getValues('module'))
                      ?.events.map((event) => (
                        <SelectItem key={event.id} value={event.id}>
                          {t(`integrations:zns.mappings.events.${form.getValues('module')}.${event.id}`, event.name)}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="template_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t('integrations:zns.mappings.form.template', 'Mẫu tin')}
              </FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  handleTemplateChange(value);
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select template" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {isLoadingTemplates ? (
                    <SelectItem value="loading" disabled>
                      Loading templates...
                    </SelectItem>
                  ) : templates?.length > 0 ? (
                    templates.map((template: any) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.template_name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No templates available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="recipient_path"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t('integrations:zns.mappings.form.recipientPath', 'Đường dẫn đến số điện thoại')}
              </FormLabel>
              <FormControl>
                <Input {...field} placeholder="customer.phone" />
              </FormControl>
              <FormDescription>
                {t('integrations:zns.mappings.form.recipientPathDescription', 'Đường dẫn đến số điện thoại trong dữ liệu sự kiện')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {selectedTemplate && selectedTemplate.metadata?.params && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">
              {t('integrations:zns.mappings.form.parameterMapping', 'Parameter Mapping')}
            </h3>
            <div className="space-y-2">
              {selectedTemplate.metadata.params.map((param: any, index: number) => (
                <div key={index} className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm">{param.name}</Label>
                    <Badge variant="outline" className="text-xs">
                      {param.type}
                    </Badge>
                  </div>
                  <Input
                    placeholder="customer.name"
                    value={form.getValues(`parameter_mapping.${param.name}`) || ''}
                    onChange={(e) => {
                      const mapping = { ...form.getValues('parameter_mapping') };
                      mapping[param.name] = e.target.value;
                      form.setValue('parameter_mapping', mapping);
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button type="submit" disabled={createMappingMutation.isPending}>
            {createMappingMutation.isPending ? (
              <span className="flex items-center gap-1">
                <Skeleton className="h-4 w-4 rounded-full" />
                {t('common:creating')}
              </span>
            ) : (
              t('common:create')
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
