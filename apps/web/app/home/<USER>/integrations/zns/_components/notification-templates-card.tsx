'use client';

import React, { memo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Info, Plus, Settings, MessageSquare, CheckCircle, AlertCircle, Copy, Check } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Label } from '@kit/ui/label';
import { Skeleton } from '@kit/ui/skeleton';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';

interface NotificationTemplatesCardProps {
  accountId: string;
  templates: any[] | null;
  isLoading: boolean;
}

const NotificationTemplatesCard = ({
  accountId,
  templates,
  isLoading
}: NotificationTemplatesCardProps) => {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  // Nhóm các template theo tag (TRANSACTION, CUSTOMER_CARE, PROMOTION)
  const groupedTemplates = React.useMemo(() => {
    if (!templates || templates.length === 0) return {};

    return templates.reduce((acc, template) => {
      const tag = template.tag || 'CUSTOMER_CARE';
      if (!acc[tag]) acc[tag] = [];
      acc[tag].push(template);
      return acc;
    }, {});
  }, [templates]);

  // Lấy danh sách các tag
  const templateTags = React.useMemo(() => {
    return Object.keys(groupedTemplates);
  }, [groupedTemplates]);

  const [activeTab, setActiveTab] = useState<string | null>(
    templateTags.length > 0 ? templateTags[0] : null
  );

  // Format template tag for display
  const formatTemplateTag = (tag: string) => {
    const key = `integrations:zns.templates.tags.${tag.toLowerCase()}`;
    const translated = t(key);

    // If translation exists, use it, otherwise format the string
    if (translated !== key) {
      return translated;
    }

    // Fallback translations
    const fallbackTranslations = {
      'TRANSACTION': 'Giao dịch',
      'CUSTOMER_CARE': 'Chăm sóc khách hàng',
      'PROMOTION': 'Khuyến mãi',
      'OTP': 'Mã OTP',
      'CONFIRM': 'Xác nhận',
      'REMINDER': 'Nhắc nhở'
    };

    return fallbackTranslations[tag] || tag
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
        delay: 0.2
      }
    }
  };

  const handleConfigureTemplates = () => {
    router.push(`/home/<USER>/integrations/zns/templates`);
  };

  // State for copy button
  const [copiedTemplateId, setCopiedTemplateId] = useState<string | null>(null);

  // Copy to clipboard function
  const copyToClipboard = (text: string, templateId: string) => {
    navigator.clipboard.writeText(text);
    setCopiedTemplateId(templateId);
    setTimeout(() => setCopiedTemplateId(null), 2000);
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={cardVariants}
    >
      <Card className="overflow-hidden border-2 border-transparent transition-all hover:border-primary/10 shadow-sm hover:shadow-md">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-primary/10 via-primary/5 to-transparent">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              {t('integrations:zns.connect.templates.title')}
              <Badge variant="outline" className="ml-2 bg-primary/10 text-primary border-primary/20">
                {templates?.length || 0}
              </Badge>
            </CardTitle>
            <CardDescription className="mt-1">
              {t('integrations:zns.connect.templates.description')}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleConfigureTemplates}
            className="gap-1 shadow-sm hover:shadow"
          >
            <Settings className="h-4 w-4" />
            {t('integrations:zns.connect.templates.configureTemplates')}
          </Button>
        </CardHeader>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full rounded-md" />
              <Skeleton className="h-40 w-full rounded-md" />
            </div>
          ) : templates && templates.length > 0 ? (
            <Tabs
              defaultValue={templateTags[0]}
              value={activeTab || undefined}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="mb-4 w-full justify-start overflow-x-auto bg-muted/50">
                {templateTags.map((tag) => (
                  <TabsTrigger
                    key={tag}
                    value={tag}
                    className="min-w-max data-[state=active]:bg-background"
                  >
                    <span className="flex items-center gap-2">
                      {formatTemplateTag(tag)}
                      <Badge variant="outline" className="ml-1 bg-primary/10 text-primary border-primary/20 text-xs">
                        {groupedTemplates[tag]?.length || 0}
                      </Badge>
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>

              {templateTags.map((tag) => (
                <TabsContent
                  key={tag}
                  value={tag}
                  className="border-none p-0 animate-in fade-in-50 data-[state=active]:animate-in data-[state=active]:fade-in-0"
                >
                  <div className="space-y-4">
                    {groupedTemplates[tag]?.map((template) => (
                      <div key={template.id} className="space-y-6 rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                          <div className="space-y-1">
                            <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                              <MessageSquare className="h-3.5 w-3.5" />
                              {t('integrations:zns.templates.form.templateId')}
                            </Label>
                            <div className="flex items-center gap-2">
                              <code className="rounded bg-muted px-2 py-1 text-sm font-mono">
                                {template.template_id}
                              </code>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 rounded-full"
                                onClick={() => copyToClipboard(template.template_id, template.id)}
                              >
                                {copiedTemplateId === template.id ? (
                                  <Check className="h-3.5 w-3.5 text-green-600" />
                                ) : (
                                  <Copy className="h-3.5 w-3.5 text-muted-foreground" />
                                )}
                              </Button>
                            </div>
                          </div>
                          <div className="flex flex-col gap-2 items-end">
                            <Badge
                              variant="outline"
                              className="bg-blue-50 text-blue-800 hover:bg-blue-100 border-blue-200"
                            >
                              {template.status || 'PENDING'}
                            </Badge>
                            <Badge
                              variant={template.enabled ? "default" : "outline"}
                              className={template.enabled ?
                                "bg-green-100 text-green-800 hover:bg-green-200 border-green-200" :
                                "bg-amber-50 text-amber-800 hover:bg-amber-100 border-amber-200"}
                            >
                              {template.enabled ?
                                t('integrations:zns.templates.enabled') :
                                t('integrations:zns.templates.disabled')}
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                            <Info className="h-3.5 w-3.5" />
                            {t('integrations:zns.templates.form.content')}
                          </Label>
                          <div className="rounded-md bg-muted/50 p-4 text-sm border shadow-inner">
                            {template.content || t('common:notAvailable')}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                            <Settings className="h-3.5 w-3.5" />
                            {t('integrations:zns.templates.form.params')}
                          </Label>
                          <pre className="max-h-40 overflow-auto rounded-md bg-muted/50 p-4 text-xs border shadow-inner font-mono">
                            {JSON.stringify(template.params, null, 2)}
                          </pre>
                        </div>

                        <div className="flex justify-between items-center">
                          {template.preview_url && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(template.preview_url, '_blank')}
                              className="gap-1"
                            >
                              <ExternalLink className="h-3.5 w-3.5" />
                              {t('integrations:zns.templates.viewPreview')}
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/home/<USER>/integrations/zns/templates/view/${template.template_id}`)}
                            className="gap-1 ml-auto"
                          >
                            <Settings className="h-3.5 w-3.5" />
                            {t('integrations:zns.templates.viewTemplate')}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          ) : (
            <div className="space-y-4">
              <Alert variant="default" className="bg-blue-50 text-blue-800 border-blue-200 shadow-sm">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertTitle>{t('integrations:zns.connect.templates.noTemplates')}</AlertTitle>
                <AlertDescription>
                  {t('integrations:zns.connect.templates.noTemplatesDescription')}
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleConfigureTemplates}
                className="w-full gap-2 shadow-sm hover:shadow"
              >
                <Plus className="h-4 w-4" />
                {t('integrations:zns.connect.templates.addTemplates')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(NotificationTemplatesCard);
