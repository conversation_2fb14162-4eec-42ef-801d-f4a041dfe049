'use client';

import React, { memo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Info, Plus, Settings, MessageSquare, CheckCircle, AlertCircle, Copy, Check } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Label } from '@kit/ui/label';
import { Skeleton } from '@kit/ui/skeleton';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';

interface NotificationTemplatesCardProps {
  accountId: string;
  templates: any[] | null;
  isLoading: boolean;
}

const NotificationTemplatesCard = ({
  accountId,
  templates,
  isLoading
}: NotificationTemplatesCardProps) => {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  // Nhóm các template theo tag (TRANSACTION, CUSTOMER_CARE, PROMOTION)
  const groupedTemplates = React.useMemo(() => {
    if (!templates || templates.length === 0) return {};

    return templates.reduce((acc, template) => {
      const tag = template.tag || 'CUSTOMER_CARE';
      if (!acc[tag]) acc[tag] = [];
      acc[tag].push(template);
      return acc;
    }, {});
  }, [templates]);

  // Lấy danh sách các tag
  const templateTags = React.useMemo(() => {
    return Object.keys(groupedTemplates);
  }, [groupedTemplates]);

  const [activeTab, setActiveTab] = useState<string | null>(
    templateTags.length > 0 ? templateTags[0] : null
  );

  // Format template tag for display
  const formatTemplateTag = (tag: string) => {
    const key = `integrations:zns.templates.tags.${tag.toLowerCase()}`;
    const translated = t(key);

    // If translation exists, use it, otherwise format the string
    if (translated !== key) {
      return translated;
    }

    // Fallback translations
    const fallbackTranslations = {
      'TRANSACTION': 'Giao dịch',
      'CUSTOMER_CARE': 'Chăm sóc khách hàng',
      'PROMOTION': 'Khuyến mãi',
      'OTP': 'Mã OTP',
      'CONFIRM': 'Xác nhận',
      'REMINDER': 'Nhắc nhở'
    };

    return fallbackTranslations[tag] || tag
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
        delay: 0.2
      }
    }
  };

  const handleConfigureTemplates = () => {
    router.push(`/home/<USER>/integrations/zns/templates`);
  };

  // State for copy button
  const [copiedTemplateId, setCopiedTemplateId] = useState<string | null>(null);

  // Copy to clipboard function
  const copyToClipboard = (text: string, templateId: string) => {
    navigator.clipboard.writeText(text);
    setCopiedTemplateId(templateId);
    setTimeout(() => setCopiedTemplateId(null), 2000);
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={cardVariants}
    >
      <Card className="overflow-hidden border-2 border-transparent transition-all hover:border-primary/10 shadow-sm hover:shadow-md">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-primary/10 via-primary/5 to-transparent">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              {t('integrations:zns.connect.templates.title')}
              <Badge variant="outline" className="ml-2 bg-primary/10 text-primary border-primary/20">
                {templates?.length || 0}
              </Badge>
            </CardTitle>
            <CardDescription className="mt-1">
              {t('integrations:zns.connect.templates.description')}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleConfigureTemplates}
            className="gap-1 shadow-sm hover:shadow"
          >
            <Settings className="h-4 w-4" />
            {t('integrations:zns.connect.templates.configureTemplates')}
          </Button>
        </CardHeader>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full rounded-md" />
              <Skeleton className="h-40 w-full rounded-md" />
            </div>
          ) : templates && templates.length > 0 ? (
            <div className="space-y-6">
              {/* Thống kê tổng quan */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Tổng số template */}
                <div className="rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">{templates?.length || 0}</h3>
                      <p className="text-sm text-muted-foreground">{t('integrations:zns.templates.totalTemplates')}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <MessageSquare className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                </div>

                {/* Template theo trạng thái */}
                <div className="rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">
                        {templates?.filter(t => t.status === 'APPROVED').length || 0}
                        <span className="text-sm text-muted-foreground"> / {templates?.length || 0}</span>
                      </h3>
                      <p className="text-sm text-muted-foreground">{t('integrations:zns.templates.approvedTemplates')}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </div>

                {/* Template đang chờ duyệt */}
                <div className="rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">
                        {templates?.filter(t => t.status === 'PENDING_REVIEW').length || 0}
                        <span className="text-sm text-muted-foreground"> / {templates?.length || 0}</span>
                      </h3>
                      <p className="text-sm text-muted-foreground">{t('integrations:zns.templates.pendingTemplates')}</p>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-amber-600" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Danh sách loại template */}
              <div className="rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md">
                <h3 className="text-md font-medium mb-4">{t('integrations:zns.templates.templateTypes')}</h3>
                <div className="space-y-3">
                  {templateTags.map((tag) => (
                    <div key={tag} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <MessageSquare className="h-4 w-4 text-primary" />
                        </div>
                        <span>{formatTemplateTag(tag)}</span>
                      </div>
                      <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                        {groupedTemplates[tag]?.length || 0}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Nút xem tất cả template */}
              <div className="flex justify-center">
                <Button
                  onClick={() => router.push(`/home/<USER>/integrations/zns/templates`)}
                  className="gap-2 shadow-sm hover:shadow"
                >
                  <Settings className="h-4 w-4" />
                  {t('integrations:zns.templates.viewAllTemplates')}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert variant="default" className="bg-blue-50 text-blue-800 border-blue-200 shadow-sm">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertTitle>{t('integrations:zns.connect.templates.noTemplates')}</AlertTitle>
                <AlertDescription>
                  {t('integrations:zns.connect.templates.noTemplatesDescription')}
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleConfigureTemplates}
                className="w-full gap-2 shadow-sm hover:shadow"
              >
                <Plus className="h-4 w-4" />
                {t('integrations:zns.connect.templates.addTemplates')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(NotificationTemplatesCard);
