'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery } from '@tanstack/react-query';
import {
  ArrowLeft,
  Send,
  AlertCircle,
  CheckCircle,
  Info,
  Loader2,
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import * as z from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { Skeleton } from '@kit/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Textarea } from '@kit/ui/textarea';
import {
  getZnsTemplateDetail,
  getZnsTemplateSampleData,
  sendZnsMessage,
  sendZnsMessageWithHashPhone,
  sendZnsMessageInDevelopmentMode,
  ZnsSendingMode,
} from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { ZNSBreadcrumbs } from '../_components/zns-breadcrumbs';

// Define form schema
const sendZnsSchema = z.object({
  phone: z.string().min(1, 'Phone number is required'),
  templateId: z.string().min(1, 'Template ID is required'),
  sendingMode: z.enum(['normal', 'exceed_quota', 'development']),
  useHashPhone: z.boolean().default(false),
  trackingId: z.string().optional(),
  params: z.record(z.string().optional()),
});

type SendZnsFormValues = z.infer<typeof sendZnsSchema>;

export default function ZnsSendPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const searchParams = useSearchParams();
  const templateIdParam = searchParams.get('templateId');
  const { account, user } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();
  const [isSending, setIsSending] = useState(false);
  const [sendResult, setSendResult] = useState<any>(null);

  // Form
  const form = useForm<SendZnsFormValues>({
    resolver: zodResolver(sendZnsSchema),
    defaultValues: {
      phone: '',
      templateId: templateIdParam || '',
      sendingMode: 'normal',
      useHashPhone: false,
      trackingId: `zns_${Date.now()}`,
      params: {},
    },
  });

  // Fetch ZNS integration
  const {
    data: integration,
    isLoading: isLoadingIntegration,
  } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching ZNS integration:', JSON.stringify(error, null, 2));
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error('Exception fetching ZNS integration:', err instanceof Error ? err.message : JSON.stringify(err, null, 2));
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration
  const {
    data: oaConfig,
    isLoading: isLoadingOaConfig,
  } = useQuery({
    queryKey: ['zns-oa-config', integration?.metadata?.oa_config_id],
    queryFn: async () => {
      if (!integration?.metadata?.oa_config_id) return null;

      try {
        const { data, error } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .maybeSingle();

        if (error) {
          console.error('Error fetching OA config:', JSON.stringify(error, null, 2));
          return null;
        }

        return data;
      } catch (err) {
        console.error('Exception fetching OA config:', err instanceof Error ? err.message : JSON.stringify(err, null, 2));
        return null;
      }
    },
    enabled: !!integration?.metadata?.oa_config_id,
  });

  // Check if token is valid
  const hasValidToken =
    oaConfig?.access_token &&
    oaConfig?.token_expires_at &&
    new Date(oaConfig.token_expires_at) > new Date();

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = integration && hasValidToken;

  // Fetch template details
  const {
    data: templateDetail,
    isLoading: isLoadingTemplateDetail,
  } = useQuery({
    queryKey: ['template-detail', oaConfig?.id, form.watch('templateId')],
    queryFn: async () => {
      if (!oaConfig?.id || !isReallyConnected || !form.watch('templateId')) return null;

      try {
        const result = await getZnsTemplateDetail(
          supabase,
          oaConfig.id,
          form.watch('templateId')
        );
        return result;
      } catch (error) {
        console.error('Error fetching template detail:', error);
        toast.error(t('integrations:zns.send.fetchTemplateError'));
        return null;
      }
    },
    enabled: Boolean(oaConfig?.id) && isReallyConnected && Boolean(form.watch('templateId')),
  });

  // Fetch template sample data
  const {
    data: templateSampleData,
    isLoading: isLoadingTemplateSampleData,
  } = useQuery({
    queryKey: ['template-sample-data', oaConfig?.id, form.watch('templateId')],
    queryFn: async () => {
      if (!oaConfig?.id || !isReallyConnected || !form.watch('templateId')) return null;

      try {
        const result = await getZnsTemplateSampleData(
          supabase,
          oaConfig.id,
          form.watch('templateId')
        );
        return result;
      } catch (error) {
        console.error('Error fetching template sample data:', error);
        toast.error(t('integrations:zns.send.fetchSampleDataError'));
        return null;
      }
    },
    enabled: Boolean(oaConfig?.id) && isReallyConnected && Boolean(form.watch('templateId')),
  });

  // Update form params when template detail changes
  useEffect(() => {
    if (templateDetail?.listParams && templateDetail.listParams.length > 0) {
      const initialParams: Record<string, string> = {};
      templateDetail.listParams.forEach((param) => {
        initialParams[param.name] = '';
      });
      form.setValue('params', initialParams);
    }
  }, [templateDetail, form]);

  // Update form params with sample data
  const handleUseSampleData = () => {
    if (templateSampleData) {
      form.setValue('params', templateSampleData);
      toast.success(t('integrations:zns.send.sampleDataApplied'));
    }
  };

  // Handle form submission
  const onSubmit = async (values: SendZnsFormValues) => {
    if (!oaConfig?.id || !isReallyConnected) {
      toast.error(t('integrations:zns.send.notConnected'));
      return;
    }

    setIsSending(true);
    setSendResult(null);

    try {
      let result;

      // Filter out empty params
      const filteredParams: Record<string, string> = {};
      Object.entries(values.params).forEach(([key, value]) => {
        if (value) {
          filteredParams[key] = value;
        }
      });

      if (values.sendingMode === 'development') {
        // Send in development mode
        result = await sendZnsMessageInDevelopmentMode(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId
        );
      } else if (values.useHashPhone) {
        // Send with hash phone
        result = await sendZnsMessageWithHashPhone(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId
        );
      } else {
        // Send normal message
        result = await sendZnsMessage(
          supabase,
          oaConfig.id,
          values.phone,
          values.templateId,
          filteredParams,
          values.trackingId,
          values.sendingMode === 'exceed_quota' ? ZnsSendingMode.EXCEED_QUOTA : ZnsSendingMode.NORMAL
        );
      }

      setSendResult(result);
      toast.success(t('integrations:zns.send.success'));

      // Save to zns_usage table
      await supabase.from('zns_usage').insert({
        account_id: accountId,
        oa_config_id: oaConfig.id,
        status: 'success',
        oa_type: oaConfig.oa_type,
        event_type: 'manual',
        metadata: {
          msg_id: result.msg_id,
          sent_time: result.sent_time,
          template_id: values.templateId,
          phone: values.phone,
          params: filteredParams,
          tracking_id: values.trackingId,
          sending_mode: values.sendingMode,
          use_hash_phone: values.useHashPhone,
        },
      });
    } catch (error: any) {
      console.error('Error sending ZNS:', error);
      toast.error(error.message || t('integrations:zns.send.error'));

      // Save to zns_usage table
      await supabase.from('zns_usage').insert({
        account_id: accountId,
        oa_config_id: oaConfig.id,
        status: 'failed',
        oa_type: oaConfig.oa_type,
        event_type: 'manual',
        metadata: {
          error: error.message,
          template_id: values.templateId,
          phone: values.phone,
          params: values.params,
          tracking_id: values.trackingId,
          sending_mode: values.sendingMode,
          use_hash_phone: values.useHashPhone,
        },
      });
    } finally {
      setIsSending(false);
    }
  };

  // Loading state
  const isLoading =
    isLoadingIntegration ||
    isLoadingOaConfig ||
    isLoadingTemplateDetail ||
    isLoadingTemplateSampleData;

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t('integrations:zns.send.title')}
        description={<ZNSBreadcrumbs accountSlug={accountSlug} />}
        account={accountSlug}
      />
      <PageBody data-testid="zns-send-page">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/home/<USER>/integrations/zns/templates`)}
              className="gap-1"
            >
              <ArrowLeft className="h-3.5 w-3.5" />
              {t('common:back')}
            </Button>
          </div>

          {!isReallyConnected && !isLoading && (
            <Alert variant="destructive" className="border-l-4 border-l-destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{t('integrations:zns.send.notConnected')}</AlertTitle>
              <AlertDescription>
                {t('integrations:zns.send.notConnectedDescription')}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => router.push(`/home/<USER>/integrations/zns/connect`)}
              >
                {t('integrations:zns.send.connectNow')}
              </Button>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>{t('integrations:zns.send.formTitle')}</CardTitle>
              <CardDescription>
                {t('integrations:zns.send.formDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-40 w-full" />
                </div>
              ) : (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="templateId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('integrations:zns.send.templateId')}</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormDescription>
                              {t('integrations:zns.send.templateIdDescription')}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('integrations:zns.send.phone')}</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="84987654321" />
                            </FormControl>
                            <FormDescription>
                              {t('integrations:zns.send.phoneDescription')}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="sendingMode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('integrations:zns.send.sendingMode')}</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('integrations:zns.send.selectSendingMode')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="normal">
                                  {t('integrations:zns.send.sendingModes.normal')}
                                </SelectItem>
                                <SelectItem value="exceed_quota">
                                  {t('integrations:zns.send.sendingModes.exceedQuota')}
                                </SelectItem>
                                <SelectItem value="development">
                                  {t('integrations:zns.send.sendingModes.development')}
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              {t('integrations:zns.send.sendingModeDescription')}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="useHashPhone"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                {t('integrations:zns.send.useHashPhone')}
                              </FormLabel>
                              <FormDescription>
                                {t('integrations:zns.send.useHashPhoneDescription')}
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="trackingId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('integrations:zns.send.trackingId')}</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormDescription>
                              {t('integrations:zns.send.trackingIdDescription')}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {templateDetail && templateDetail.listParams && templateDetail.listParams.length > 0 && (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-medium">
                            {t('integrations:zns.send.parameters')}
                          </h3>
                          {templateSampleData && Object.keys(templateSampleData).length > 0 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleUseSampleData}
                            >
                              {t('integrations:zns.send.useSampleData')}
                            </Button>
                          )}
                        </div>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                          {templateDetail.listParams.map((param, index) => (
                            <FormField
                              key={index}
                              control={form.control}
                              name={`params.${param.name}`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>
                                    {param.name}
                                    {param.require && <span className="text-destructive ml-1">*</span>}
                                  </FormLabel>
                                  <FormControl>
                                    <Input {...field} />
                                  </FormControl>
                                  <FormDescription>
                                    {t('integrations:zns.send.paramTypeDescription', {
                                      type: param.type,
                                      min: param.minLength,
                                      max: param.maxLength,
                                    })}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSending || !isReallyConnected}
                        className="gap-1"
                      >
                        {isSending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Send className="h-4 w-4" />
                        )}
                        {t('integrations:zns.send.sendButton')}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>

          {sendResult && (
            <Card>
              <CardHeader>
                <CardTitle>{t('integrations:zns.send.result')}</CardTitle>
                <CardDescription>
                  {t('integrations:zns.send.resultDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert variant="success" className="border-l-4 border-l-green-500">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertTitle>{t('integrations:zns.send.success')}</AlertTitle>
                  <AlertDescription>
                    <div className="mt-2 space-y-2">
                      <div>
                        <span className="font-medium">{t('integrations:zns.send.messageId')}:</span>{' '}
                        <code className="rounded bg-muted px-1 py-0.5 text-sm font-mono">
                          {sendResult.msg_id}
                        </code>
                      </div>
                      <div>
                        <span className="font-medium">{t('integrations:zns.send.sentTime')}:</span>{' '}
                        {new Date(parseInt(sendResult.sent_time)).toLocaleString()}
                      </div>
                      {sendResult.quota && (
                        <div>
                          <span className="font-medium">{t('integrations:zns.send.remainingQuota')}:</span>{' '}
                          {sendResult.quota.remainingQuota}/{sendResult.quota.dailyQuota}
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}
        </div>
      </PageBody>
    </>
  );
}
