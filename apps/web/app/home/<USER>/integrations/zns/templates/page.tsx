'use client';

import { useEffect, useState } from 'react';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Filter,
  Plus,
  RefreshCcw,
  Search,
  XCircle,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Skeleton } from '@kit/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { ZnsTemplateStatus, getZnsTemplateList } from '@kit/zns';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

export default function ZnsTemplatesPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const { account, user } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Fetch ZNS integration
  const {
    data: integration,
    isLoading: isLoadingIntegration,
    refetch: refetchIntegration,
  } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration
  const {
    data: oaConfig,
    isLoading: isLoadingOaConfig,
    refetch: refetchOaConfig,
  } = useQuery({
    queryKey: ['zns-oa-config', integration?.metadata?.oa_config_id],
    queryFn: async () => {
      if (!integration?.metadata?.oa_config_id) return null;

      try {
        const { data, error } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .maybeSingle();

        if (error) {
          console.error(
            'Error fetching OA config:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        return data;
      } catch (err) {
        console.error(
          'Exception fetching OA config:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!integration?.metadata?.oa_config_id,
  });

  // Check if token is valid
  const hasValidToken =
    oaConfig?.access_token &&
    oaConfig?.token_expires_at &&
    new Date(oaConfig.token_expires_at) > new Date();

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const [isReallyConnected, setIsReallyConnected] = useState(false);

  // Cập nhật trạng thái kết nối khi integration hoặc token thay đổi
  useEffect(() => {
    setIsReallyConnected(Boolean(integration && hasValidToken));
  }, [integration, hasValidToken]);

  // Fetch ZNS templates from Zalo API
  const {
    data: zaloTemplates,
    isLoading: isLoadingZaloTemplates,
    refetch: refetchZaloTemplates,
  } = useQuery({
    queryKey: [
      'zalo-templates',
      oaConfig?.id,
      isReallyConnected,
      page,
      pageSize,
    ],
    queryFn: async () => {
      if (!oaConfig?.id || !isReallyConnected)
        return { data: [], metadata: { total: 0 } };

      try {
        const result = await getZnsTemplateList(
          supabase,
          oaConfig.id,
          page * pageSize,
          pageSize,
          statusFilter !== 'all'
            ? (Number(statusFilter) as ZnsTemplateStatus)
            : undefined,
        );
        return result;
      } catch (error: any) {
        console.error('Error fetching Zalo templates:', error);

        // Kiểm tra lỗi token
        if (error.message && error.message.includes('Access token invalid')) {
          toast.error(t('integrations:zns.templates.tokenInvalid', 'Token has expired or is invalid. Please reconnect your Zalo account.'));
          // Cập nhật trạng thái kết nối
          setIsReallyConnected(false);
        } else {
          toast.error(t('integrations:zns.templates.fetchError', 'Error fetching templates'));
        }

        return { data: [], metadata: { total: 0 } };
      }
    },
    enabled: Boolean(oaConfig?.id) && isReallyConnected,
  });

  // Fetch local ZNS templates
  const {
    data: localTemplates,
    isLoading: isLoadingLocalTemplates,
    refetch: refetchLocalTemplates,
  } = useQuery({
    queryKey: ['local-templates', oaConfig?.id],
    queryFn: async () => {
      if (!oaConfig?.id) return [];

      const { data, error } = await supabase
        .from('zns_templates')
        .select('*')
        .eq('oa_config_id', oaConfig.id);

      if (error) throw error;
      return data || [];
    },
    enabled: Boolean(oaConfig?.id),
  });

  // Filter templates based on search term
  const filteredTemplates = zaloTemplates?.data.filter((template) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    const templateName = template.templateName?.toLowerCase() || '';
    const templateId = String(template.templateId || '').toLowerCase();

    return (
      templateName.includes(searchLower) ||
      templateId.includes(searchLower)
    );
  });

  // Handle refresh
  const handleRefresh = () => {
    refetchZaloTemplates();
    refetchLocalTemplates();
    toast.success(t('integrations:zns.templates.refreshSuccess', 'Templates refreshed successfully'));
  };

  // Handle create template
  const handleCreateTemplate = () => {
    router.push(`/home/<USER>/integrations/zns/templates/create`);
  };

  // Handle edit template
  const handleEditTemplate = (templateId: string) => {
    router.push(
      `/home/<USER>/integrations/zns/templates/edit/${templateId}`,
    );
  };

  // Handle view template details
  const handleViewTemplate = (templateId: string) => {
    router.push(
      `/home/<USER>/integrations/zns/templates/view/${templateId}`,
    );
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ENABLE':
        return (
          <Badge
            variant="outline"
            className="border-green-200 bg-green-50 text-green-700"
          >
            <CheckCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.enabled', 'Enabled')}
          </Badge>
        );
      case 'PENDING_REVIEW':
        return (
          <Badge
            variant="outline"
            className="border-yellow-200 bg-yellow-50 text-yellow-700"
          >
            <Clock className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.pendingReview', 'Pending Review')}
          </Badge>
        );
      case 'REJECT':
        return (
          <Badge
            variant="outline"
            className="border-red-200 bg-red-50 text-red-700"
          >
            <XCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.rejected', 'Rejected')}
          </Badge>
        );
      case 'DISABLE':
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            <AlertCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.disabled', 'Disabled')}
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            {status}
          </Badge>
        );
    }
  };

  // Get tag badge
  const getTagBadge = (tag: number) => {
    switch (tag) {
      case 1:
        return (
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-700"
          >
            {t('integrations:zns.templates.tag.transaction', 'Transaction')}
          </Badge>
        );
      case 2:
        return (
          <Badge
            variant="outline"
            className="border-purple-200 bg-purple-50 text-purple-700"
          >
            {t('integrations:zns.templates.tag.customerCare', 'Customer Care')}
          </Badge>
        );
      case 3:
        return (
          <Badge
            variant="outline"
            className="border-pink-200 bg-pink-50 text-pink-700"
          >
            {t('integrations:zns.templates.tag.promotion', 'Promotion')}
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            {t('integrations:zns.templates.tag.unknown', 'Unknown')}
          </Badge>
        );
    }
  };

  // Loading state
  const isLoading =
    isLoadingIntegration ||
    isLoadingOaConfig ||
    isLoadingZaloTemplates ||
    isLoadingLocalTemplates;

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t('integrations:zns.templates.title')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              templates: t('integrations:zns.templates.title', 'Mẫu')
            }}
          />
        }
        account={accountSlug}
      />
      <PageBody data-testid="zns-templates-page">
        <div className="space-y-6">
          {!isReallyConnected && !isLoading && (
            <Alert
              variant="destructive"
              className="border-l-destructive border-l-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t('integrations:zns.templates.notConnected')}
              </AlertTitle>
              <AlertDescription>
                {t('integrations:zns.templates.notConnectedDescription')}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/zns/connect`)
                }
              >
                {t('integrations:zns.templates.connectNow')}
              </Button>
            </Alert>
          )}

          {/* Thông báo khi token không hợp lệ */}
          {integration && !hasValidToken && !isLoading && (
            <Alert
              variant="destructive"
              className="border-l-destructive border-l-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{t('integrations:zns.tokenExpired')}</AlertTitle>
              <AlertDescription>
                {t('integrations:zns.tokenExpiredDescription')}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/zns/connect`)
                }
              >
                {t('integrations:zns.reconnect')}
              </Button>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>{t('integrations:zns.templates.list', 'Template List')}</CardTitle>
                  <CardDescription>
                    {t('integrations:zns.templates.listDescription', 'Manage your ZNS templates')}
                  </CardDescription>
                </div>
                <div className="mt-4 flex flex-col gap-2 sm:mt-0 sm:flex-row">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={!isReallyConnected || isLoading}
                    className="gap-1"
                  >
                    <RefreshCcw className="h-3.5 w-3.5" />
                    {t('common:refresh')}
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleCreateTemplate}
                    disabled={!isReallyConnected}
                    className="gap-1"
                  >
                    <Plus className="h-3.5 w-3.5" />
                    {t('integrations:zns.templates.createTemplate', 'Create New Template')}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="relative flex-1">
                    <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                    <Input
                      placeholder={t('integrations:zns.templates.search', 'Search templates...')}
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <Filter className="mr-2 h-3.5 w-3.5" />
                      <SelectValue
                        placeholder={t(
                          'integrations:zns.templates.filterByStatus', 'Filter by status'
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        {t('integrations:zns.templates.status.all', 'All Statuses')}
                      </SelectItem>
                      <SelectItem value="1">
                        {t('integrations:zns.templates.status.enabled', 'Enabled')}
                      </SelectItem>
                      <SelectItem value="2">
                        {t('integrations:zns.templates.status.pendingReview', 'Pending Review')}
                      </SelectItem>
                      <SelectItem value="3">
                        {t('integrations:zns.templates.status.rejected', 'Rejected')}
                      </SelectItem>
                      <SelectItem value="4">
                        {t('integrations:zns.templates.status.disabled', 'Disabled')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ) : filteredTemplates && filteredTemplates.length > 0 ? (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>
                            {t('integrations:zns.templates.templateId', 'Template ID')}
                          </TableHead>
                          <TableHead>
                            {t('integrations:zns.templates.templateName', 'Template Name')}
                          </TableHead>
                          <TableHead>
                            {t('integrations:zns.templates.status', 'Status')}
                          </TableHead>
                          <TableHead>
                            {t('integrations:zns.templates.tag', 'Tag')}
                          </TableHead>
                          <TableHead>
                            {t('integrations:zns.templates.quality', 'Quality')}
                          </TableHead>
                          <TableHead className="text-right">
                            Hành động
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTemplates.map((template) => (
                          <TableRow key={template.templateId}>
                            <TableCell className="font-mono text-xs">
                              {template.templateId}
                            </TableCell>
                            <TableCell>{template.templateName}</TableCell>
                            <TableCell>
                              {getStatusBadge(template.status)}
                            </TableCell>
                            <TableCell>
                              {getTagBadge(Number(template.templateTag))}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={
                                  template.templateQuality === 'HIGH'
                                    ? 'border-green-200 bg-green-50 text-green-700'
                                    : template.templateQuality === 'MEDIUM'
                                      ? 'border-yellow-200 bg-yellow-50 text-yellow-700'
                                      : template.templateQuality === 'LOW'
                                        ? 'border-red-200 bg-red-50 text-red-700'
                                        : 'border-gray-200 bg-gray-50 text-gray-700'
                                }
                              >
                                {template.templateQuality}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  handleViewTemplate(template.templateId)
                                }
                              >
                                {t('common:view')}
                              </Button>
                              {template.status === 'REJECT' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleEditTemplate(template.templateId)
                                  }
                                >
                                  {t('common:edit')}
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex h-32 items-center justify-center rounded-md border">
                    <div className="text-center">
                      <p className="text-muted-foreground">
                        {searchTerm
                          ? t('integrations:zns.templates.noTemplatesFound', 'No templates found matching your search')
                          : t('integrations:zns.templates.noTemplates', 'No templates available')}
                      </p>
                      {!searchTerm && (
                        <Button
                          variant="link"
                          onClick={handleCreateTemplate}
                          disabled={!isReallyConnected}
                          className="mt-2"
                        >
                          {t('integrations:zns.templates.createFirstTemplate', 'Create your first template')}
                        </Button>
                      )}
                    </div>
                  </div>
                )}

                {/* Pagination */}
                {zaloTemplates &&
                  zaloTemplates.metadata &&
                  zaloTemplates.metadata.total > 0 && (
                    <div className="flex items-center justify-between">
                      <div className="text-muted-foreground text-sm">
                        {t('integrations:zns.templates.showing', {
                          start: page * pageSize + 1,
                          end: Math.min(
                            (page + 1) * pageSize,
                            zaloTemplates.metadata.total,
                          ),
                          total: zaloTemplates.metadata.total,
                        }, 'Showing {{start}}-{{end}} of {{total}} templates')}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(Math.max(0, page - 1))}
                          disabled={page === 0}
                        >
                          {t('common:previous')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setPage(page + 1)}
                          disabled={
                            (page + 1) * pageSize >=
                            zaloTemplates.metadata.total
                          }
                        >
                          {t('common:next')}
                        </Button>
                      </div>
                    </div>
                  )}
              </div>
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
