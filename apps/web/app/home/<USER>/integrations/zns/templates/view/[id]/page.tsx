'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Copy,
  Database,
  Edit,
  ExternalLink,
  FileText,
  ListTree,
  XCircle,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Label } from '@kit/ui/label';
import { PageBody } from '@kit/ui/page';
import { Skeleton } from '@kit/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { getZnsTemplateDetail, ZnsApiError } from '@kit/zns';
import { getZnsTemplateDetail as getZnsTemplateDetailOriginal } from '@kit/zns/lib/get-templates';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

export default function ZnsTemplateViewPage() {
  const { account: accountSlug, id: templateId } = useParams<{
    account: string;
    id: string;
  }>();
  const { account, user } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  // Fetch ZNS integration
  const { data: integration, isLoading: isLoadingIntegration } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['zns-oa-config', integration?.metadata?.oa_config_id],
    queryFn: async () => {
      if (!integration?.metadata?.oa_config_id) return null;

      try {
        const { data, error } = await supabase
          .from('oa_configurations')
          .select('*')
          .eq('id', integration.metadata.oa_config_id)
          .maybeSingle();

        if (error) {
          console.error(
            'Error fetching OA config:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        return data;
      } catch (err) {
        console.error(
          'Exception fetching OA config:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!integration?.metadata?.oa_config_id,
  });

  // Check if token is valid
  const hasValidToken =
    oaConfig?.access_token &&
    oaConfig?.token_expires_at &&
    new Date(oaConfig.token_expires_at) > new Date();

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = integration && hasValidToken;

  // Fetch template details
  const { data: templateDetail, isLoading: isLoadingTemplateDetail } = useQuery(
    {
      queryKey: ['template-detail', oaConfig?.id, templateId],
      queryFn: async () => {
        if (!oaConfig?.id || !isReallyConnected || !templateId) return null;

        try {
          const result = await getZnsTemplateDetail(
            supabase,
            oaConfig.id,
            templateId,
          );
          return result;
        } catch (error) {
          console.error('Error fetching template detail:', error);
          toast.error(t('integrations:zns.templates.fetchDetailError'));
          return null;
        }
      },
      enabled:
        Boolean(oaConfig?.id) && isReallyConnected && Boolean(templateId),
    },
  );

  // Fetch template sample data
  const { data: templateSampleData, isLoading: isLoadingTemplateSampleData } =
    useQuery({
      queryKey: ['template-sample-data', oaConfig?.id, templateId],
      queryFn: async () => {
        if (!oaConfig?.id || !isReallyConnected || !templateId) return null;

        try {
          // Thay vì lấy dữ liệu mẫu, chúng ta sẽ sử dụng chi tiết template
          // Điều này tránh lỗi "ZNS template not approved"
          const result = await getZnsTemplateDetailOriginal(
            supabase,
            oaConfig.id,
            templateId,
          );

          // Tạo dữ liệu mẫu từ chi tiết template
          return {
            html: '<div class="zns-template-preview">Template preview not available</div>',
            data: result,
            parameters: result.params?.map(param => ({
              name: param.name,
              value: param.sample_value || `Sample ${param.name}`
            })) || []
          };
        } catch (error: any) {
          console.error('Error fetching template sample data:', error);

          // Kiểm tra lỗi cụ thể
          if (error instanceof ZnsApiError) {
            // Lỗi token hết hạn
            if (error.code === -124 || error.message.includes('not approved')) {
              // Không hiển thị thông báo lỗi cho lỗi template chưa được phê duyệt
              console.log('Template not approved, skipping error message');
            } else if (error.message.includes('Access token invalid')) {
              toast.error(
                t(
                  'integrations:zns.tokenInvalid',
                  'Token has expired or is invalid. Please reconnect your Zalo account.',
                ),
              );
            } else {
              toast.error(
                t(
                  'integrations:zns.templates.fetchSampleDataError',
                  'Error fetching sample data',
                ),
              );
            }
          } else if (error.message && error.message.includes('Access token invalid')) {
            toast.error(
              t(
                'integrations:zns.tokenInvalid',
                'Token has expired or is invalid. Please reconnect your Zalo account.',
              ),
            );
          } else if (
            error.message &&
            !error.message.includes('ZNS template not approved') &&
            !error.message.includes('not approved')
          ) {
            // Không hiển thị thông báo lỗi cho lỗi template chưa được phê duyệt
            toast.error(
              t(
                'integrations:zns.templates.fetchSampleDataError',
                'Error fetching sample data',
              ),
            );
          }

          return null;
        }
      },
      enabled:
        Boolean(oaConfig?.id) && isReallyConnected && Boolean(templateId),
    });

  // Handle copy to clipboard
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t('common:copied'));
  };

  // Handle edit template
  const handleEditTemplate = () => {
    if (templateDetail?.status === 'REJECT') {
      router.push(
        `/home/<USER>/integrations/zns/templates/edit/${templateId}`,
      );
    } else {
      toast.info(
        t(
          'integrations:zns.templates.cannotEditTemplate',
          'Only templates with REJECT status can be edited',
        ),
      );
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ENABLE':
        return (
          <Badge
            variant="outline"
            className="border-green-200 bg-green-50 text-green-700"
          >
            <CheckCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.enabled', 'Enabled')}
          </Badge>
        );
      case 'PENDING_REVIEW':
        return (
          <Badge
            variant="outline"
            className="border-yellow-200 bg-yellow-50 text-yellow-700"
          >
            <Clock className="mr-1 h-3.5 w-3.5" />
            {t(
              'integrations:zns.templates.status.pendingReview',
              'Pending Review',
            )}
          </Badge>
        );
      case 'REJECT':
        return (
          <Badge
            variant="outline"
            className="border-red-200 bg-red-50 text-red-700"
          >
            <XCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.rejected', 'Rejected')}
          </Badge>
        );
      case 'DISABLE':
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            <AlertCircle className="mr-1 h-3.5 w-3.5" />
            {t('integrations:zns.templates.status.disabled', 'Disabled')}
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            {status}
          </Badge>
        );
    }
  };

  // Get tag badge
  const getTagBadge = (tag: number) => {
    switch (tag) {
      case 1:
        return (
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-700"
          >
            {t('integrations:zns.templates.tag.transaction', 'Transaction')}
          </Badge>
        );
      case 2:
        return (
          <Badge
            variant="outline"
            className="border-purple-200 bg-purple-50 text-purple-700"
          >
            {t('integrations:zns.templates.tag.customerCare', 'Customer Care')}
          </Badge>
        );
      case 3:
        return (
          <Badge
            variant="outline"
            className="border-pink-200 bg-pink-50 text-pink-700"
          >
            {t('integrations:zns.templates.tag.promotion', 'Promotion')}
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-200 bg-gray-50 text-gray-700"
          >
            {t('integrations:zns.templates.tag.unknown', 'Unknown')}
          </Badge>
        );
    }
  };

  // Loading state
  const isLoading =
    isLoadingIntegration ||
    isLoadingOaConfig ||
    isLoadingTemplateDetail ||
    isLoadingTemplateSampleData;

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={t('integrations:zns.templates.viewTemplate')}
        description={
          <div className="flex items-center space-x-2 text-sm">
            <a href="/home" className="hover:underline">
              {t('common:routes.home', 'Trang chủ')}
            </a>
            <span>/</span>
            <a href={`/home/<USER>"hover:underline">
              {accountSlug}
            </a>
            <span>/</span>
            <a
              href={`/home/<USER>/integrations`}
              className="hover:underline"
            >
              {t('common:routes.integrations', 'Tích hợp')}
            </a>
            <span>/</span>
            <a
              href={`/home/<USER>/integrations/zns`}
              className="hover:underline"
            >
              ZNS
            </a>
            <span>/</span>
            <a
              href={`/home/<USER>/integrations/zns/templates`}
              className="hover:underline"
            >
              {t('integrations:zns.templates.title', 'Mẫu')}
            </a>
            <span>/</span>
            <span>
              {t('integrations:zns.templates.viewTemplate', 'Xem mẫu')}
            </span>
          </div>
        }
        account={accountSlug}
      />
      <PageBody data-testid="zns-template-view-page">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                router.push(`/home/<USER>/integrations/zns/templates`)
              }
              className="gap-1"
            >
              <ArrowLeft className="h-3.5 w-3.5" />
              {t('common:back')}
            </Button>
            {templateDetail?.status === 'REJECT' && (
              <Button
                variant="default"
                size="sm"
                onClick={handleEditTemplate}
                className="gap-1"
              >
                <Edit className="h-3.5 w-3.5" />
                {t('common:edit')}
              </Button>
            )}
          </div>

          {!isReallyConnected && !isLoading && (
            <Alert
              variant="destructive"
              className="border-l-destructive border-l-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t('integrations:zns.templates.notConnected', 'Not Connected')}
              </AlertTitle>
              <AlertDescription>
                {t(
                  'integrations:zns.templates.notConnectedDescription',
                  'Your Zalo OA is not connected. Please connect to view template details.',
                )}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/zns/connect`)
                }
              >
                {t('integrations:zns.templates.connectNow', 'Connect Now')}
              </Button>
            </Alert>
          )}

          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-40 w-full" />
              <Skeleton className="h-40 w-full" />
            </div>
          ) : templateDetail ? (
            <div className="space-y-6">
              <Card>
                <CardHeader className="rounded-t-lg border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 pb-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                      <CardTitle className="text-2xl font-bold text-blue-800">
                        {templateDetail.templateName}
                      </CardTitle>
                      <CardDescription className="mt-2 flex items-center gap-2">
                        <span className="rounded-md bg-blue-100 px-2 py-1 font-mono text-sm text-blue-700">
                          {templateDetail.templateId}
                        </span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 rounded-full bg-blue-100 p-0 text-blue-700 hover:bg-blue-200"
                          onClick={() => handleCopy(templateDetail.templateId)}
                        >
                          <Copy className="h-3.5 w-3.5" />
                        </Button>
                      </CardDescription>
                    </div>
                    <div className="mt-4 flex flex-col gap-2 sm:mt-0 sm:flex-row">
                      {getStatusBadge(templateDetail.status)}
                      {getTagBadge(Number(templateDetail.templateTag))}
                      <Badge
                        variant="outline"
                        className={
                          templateDetail.templateQuality === 'HIGH'
                            ? 'border-green-200 bg-green-50 font-medium text-green-700 shadow-sm'
                            : templateDetail.templateQuality === 'MEDIUM'
                              ? 'border-yellow-200 bg-yellow-50 font-medium text-yellow-700 shadow-sm'
                              : templateDetail.templateQuality === 'LOW'
                                ? 'border-red-200 bg-red-50 font-medium text-red-700 shadow-sm'
                                : 'border-gray-200 bg-gray-50 font-medium text-gray-700 shadow-sm'
                        }
                      >
                        <span className="flex items-center gap-1">
                          {templateDetail.templateQuality === 'HIGH' && (
                            <CheckCircle className="h-3.5 w-3.5" />
                          )}
                          {templateDetail.templateQuality === 'MEDIUM' && (
                            <Clock className="h-3.5 w-3.5" />
                          )}
                          {templateDetail.templateQuality === 'LOW' && (
                            <AlertCircle className="h-3.5 w-3.5" />
                          )}
                          {templateDetail.templateQuality}
                        </span>
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="details" className="w-full">
                    <TabsList className="mb-6 grid w-full grid-cols-3 rounded-lg bg-gray-100 p-1">
                      <TabsTrigger
                        value="details"
                        className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-md"
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        {t('integrations:zns.templates.details', 'Details')}
                      </TabsTrigger>
                      <TabsTrigger
                        value="parameters"
                        className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-md"
                      >
                        <ListTree className="mr-2 h-4 w-4" />
                        {t(
                          'integrations:zns.templates.parameters',
                          'Parameters',
                        )}
                      </TabsTrigger>
                      <TabsTrigger
                        value="sampleData"
                        className="data-[state=active]:bg-white data-[state=active]:text-blue-700 data-[state=active]:shadow-md"
                      >
                        <Database className="mr-2 h-4 w-4" />
                        {t(
                          'integrations:zns.templates.sampleData',
                          'Sample Data',
                        )}
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="details" className="space-y-6">
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div className="rounded-lg border border-blue-100 bg-blue-50 p-5 shadow-sm transition-all hover:shadow-md">
                          <div className="mb-2 flex items-center gap-2">
                            <div className="rounded-full bg-blue-100 p-1.5">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-blue-700"
                              >
                                <circle cx="12" cy="12" r="10" />
                                <path d="M12 6v6l4 2" />
                              </svg>
                            </div>
                            <Label className="text-sm font-medium text-blue-700">
                              {t('integrations:zns.templates.price', 'Price')}
                            </Label>
                          </div>
                          <div className="mt-1 text-lg font-bold text-blue-800">
                            {templateDetail.price.toLocaleString()} VND
                          </div>
                        </div>
                        <div className="rounded-lg border border-indigo-100 bg-indigo-50 p-5 shadow-sm transition-all hover:shadow-md">
                          <div className="mb-2 flex items-center gap-2">
                            <div className="rounded-full bg-indigo-100 p-1.5">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-indigo-700"
                              >
                                <path d="M10 2h4" />
                                <path d="M12 14v-4" />
                                <circle cx="12" cy="14" r="8" />
                              </svg>
                            </div>
                            <Label className="text-sm font-medium text-indigo-700">
                              {t(
                                'integrations:zns.templates.timeout',
                                'Timeout',
                              )}
                            </Label>
                          </div>
                          <div className="mt-1 text-lg font-bold text-indigo-800">
                            {templateDetail.timeout / 1000}{' '}
                            {t('common:seconds')}
                          </div>
                        </div>
                      </div>

                      {templateDetail.reason && (
                        <Alert
                          variant={
                            templateDetail.status === 'REJECT'
                              ? 'destructive'
                              : 'default'
                          }
                          className={
                            templateDetail.status === 'REJECT'
                              ? 'border-l-destructive border-l-4 bg-red-50 shadow-md'
                              : 'border border-blue-100 bg-blue-50 shadow-md'
                          }
                        >
                          {templateDetail.status === 'REJECT' ? (
                            <XCircle className="h-5 w-5 text-red-600" />
                          ) : (
                            <CheckCircle className="h-5 w-5 text-blue-600" />
                          )}
                          <AlertTitle
                            className={
                              templateDetail.status === 'REJECT'
                                ? 'font-bold text-red-700'
                                : 'font-bold text-blue-700'
                            }
                          >
                            {templateDetail.status === 'REJECT'
                              ? t(
                                  'integrations:zns.templates.rejectionReason',
                                  'Rejection Reason',
                                )
                              : t(
                                  'integrations:zns.templates.statusReason',
                                  'Status Reason',
                                )}
                          </AlertTitle>
                          <AlertDescription
                            className={
                              templateDetail.status === 'REJECT'
                                ? 'text-red-600'
                                : 'text-blue-600'
                            }
                          >
                            {templateDetail.reason}
                          </AlertDescription>
                          {templateDetail.status === 'REJECT' && (
                            <div className="mt-3">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={handleEditTemplate}
                                className="gap-1 border-red-200 bg-white text-red-600 hover:bg-red-50"
                              >
                                <Edit className="h-3.5 w-3.5" />
                                {t('common:edit')}
                              </Button>
                            </div>
                          )}
                        </Alert>
                      )}

                      {templateDetail.previewUrl && (
                        <div className="rounded-lg border border-purple-100 bg-purple-50 p-5 shadow-sm transition-all hover:shadow-md">
                          <div className="mb-2 flex items-center gap-2">
                            <div className="rounded-full bg-purple-100 p-1.5">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-purple-700"
                              >
                                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                                <polyline points="15 3 21 3 21 9" />
                                <line x1="10" y1="14" x2="21" y2="3" />
                              </svg>
                            </div>
                            <Label className="text-sm font-medium text-purple-700">
                              {t(
                                'integrations:zns.templates.preview',
                                'Preview',
                              )}
                            </Label>
                          </div>
                          <div className="mt-2 flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                window.open(templateDetail.previewUrl, '_blank')
                              }
                              className="gap-1 border-purple-200 bg-white text-purple-700 shadow-sm hover:bg-purple-50"
                            >
                              <ExternalLink className="h-3.5 w-3.5" />
                              {t(
                                'integrations:zns.templates.openPreview',
                                'Open Preview',
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                handleCopy(templateDetail.previewUrl)
                              }
                              className="gap-1 text-purple-700"
                            >
                              <Copy className="h-3.5 w-3.5" />
                              {t('common:copyLink', 'Copy Link')}
                            </Button>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                    <TabsContent value="parameters" className="space-y-6">
                      {templateDetail.listParams &&
                      templateDetail.listParams.length > 0 ? (
                        <div className="overflow-hidden rounded-lg border border-blue-100 shadow-md">
                          <div className="border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-3">
                            <h3 className="flex items-center gap-2 font-medium text-blue-700">
                              <ListTree className="h-4 w-4 text-blue-500" />
                              {t(
                                'integrations:zns.templates.parametersList',
                                'Parameters List',
                              )}
                            </h3>
                          </div>
                          <Table>
                            <TableHeader className="bg-gray-50">
                              <TableRow>
                                <TableHead className="font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramName',
                                    'Parameter Name',
                                  )}
                                </TableHead>
                                <TableHead className="font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramType',
                                    'Parameter Type',
                                  )}
                                </TableHead>
                                <TableHead className="font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramRequired',
                                    'Required',
                                  )}
                                </TableHead>
                                <TableHead className="font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramLength',
                                    'Length',
                                  )}
                                </TableHead>
                                <TableHead className="font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramAcceptNull',
                                    'Accept Null',
                                  )}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {templateDetail.listParams.map((param, index) => (
                                <TableRow
                                  key={index}
                                  className="hover:bg-blue-50"
                                >
                                  <TableCell className="font-mono font-medium text-blue-700">
                                    {param.name}
                                  </TableCell>
                                  <TableCell>
                                    <Badge
                                      variant="outline"
                                      className="border-indigo-200 bg-indigo-50 font-medium text-indigo-700"
                                    >
                                      {param.type}
                                    </Badge>
                                  </TableCell>
                                  <TableCell>
                                    {param.require ? (
                                      <Badge
                                        variant="outline"
                                        className="border-green-200 bg-green-50 font-medium text-green-700 shadow-sm"
                                      >
                                        <CheckCircle className="mr-1 h-3 w-3" />
                                        {t('common:yes')}
                                      </Badge>
                                    ) : (
                                      <Badge
                                        variant="outline"
                                        className="border-gray-200 bg-gray-50 font-medium text-gray-700 shadow-sm"
                                      >
                                        <XCircle className="mr-1 h-3 w-3" />
                                        {t('common:no')}
                                      </Badge>
                                    )}
                                  </TableCell>
                                  <TableCell className="font-mono text-gray-700">
                                    {param.minLength}-{param.maxLength}
                                  </TableCell>
                                  <TableCell>
                                    {param.acceptNull ? (
                                      <Badge
                                        variant="outline"
                                        className="border-green-200 bg-green-50 font-medium text-green-700 shadow-sm"
                                      >
                                        <CheckCircle className="mr-1 h-3 w-3" />
                                        {t('common:yes')}
                                      </Badge>
                                    ) : (
                                      <Badge
                                        variant="outline"
                                        className="border-gray-200 bg-gray-50 font-medium text-gray-700 shadow-sm"
                                      >
                                        <XCircle className="mr-1 h-3 w-3" />
                                        {t('common:no')}
                                      </Badge>
                                    )}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <div className="flex h-32 items-center justify-center rounded-md border">
                          <div className="text-center">
                            <p className="text-muted-foreground">
                              {t(
                                'integrations:zns.templates.noParameters',
                                'No parameters available',
                              )}
                            </p>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                    <TabsContent value="sampleData" className="space-y-6">
                      {templateSampleData &&
                      templateSampleData.parameters &&
                      templateSampleData.parameters.length > 0 ? (
                        <div className="overflow-hidden rounded-lg border border-purple-100 shadow-md">
                          <div className="border-b border-purple-100 bg-gradient-to-r from-purple-50 to-pink-50 p-3">
                            <h3 className="flex items-center gap-2 font-medium text-purple-700">
                              <Database className="h-4 w-4 text-purple-500" />
                              {t(
                                'integrations:zns.templates.sampleDataList',
                                'Sample Data List',
                              )}
                            </h3>
                          </div>
                          <Table>
                            <TableHeader className="bg-gray-50">
                              <TableRow>
                                <TableHead className="w-1/3 font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.paramName',
                                    'Parameter Name',
                                  )}
                                </TableHead>
                                <TableHead className="w-2/3 font-semibold text-gray-700">
                                  {t(
                                    'integrations:zns.templates.sampleValue',
                                    'Sample Value',
                                  )}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {templateSampleData.parameters.map(
                                (param: any) => (
                                  <TableRow
                                    key={param.name}
                                    className="hover:bg-purple-50"
                                  >
                                    <TableCell className="font-mono font-medium text-purple-700">
                                      {param.name}
                                    </TableCell>
                                    <TableCell className="m-1 rounded-md border border-gray-100 bg-gray-50 p-2 font-mono">
                                      {String(param.value || '')}
                                    </TableCell>
                                  </TableRow>
                                ),
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      ) : (
                        <div className="flex h-40 items-center justify-center rounded-lg border border-purple-100 bg-purple-50">
                          <div className="p-6 text-center">
                            <div className="mb-3 flex justify-center">
                              {templateDetail?.status !== 'ENABLE' ? (
                                <Clock className="h-10 w-10 text-purple-300" />
                              ) : (
                                <Database className="h-10 w-10 text-purple-300" />
                              )}
                            </div>
                            <p className="font-medium text-purple-700">
                              {templateDetail?.status !== 'ENABLE'
                                ? t(
                                    'integrations:zns.connect.templates.sampleDataNotAvailable',
                                    'Sample data is only available for approved templates',
                                  )
                                : t(
                                    'integrations:zns.connect.templates.noSampleData',
                                    'No sample data available',
                                  )}
                            </p>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
                <CardFooter className="flex justify-between border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/zns/templates`,
                      )
                    }
                    className="flex items-center gap-1 border-blue-200 text-blue-700 shadow-sm hover:bg-blue-50"
                  >
                    <ArrowLeft className="h-3.5 w-3.5" />
                    {t('common:back')}
                  </Button>
                  {templateDetail.status === 'ENABLE' && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/home/<USER>/integrations/zns/send?templateId=${templateId}`,
                        )
                      }
                      className="flex items-center gap-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md transition-all hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-3.5 w-3.5"
                      >
                        <path d="m22 2-7 20-4-9-9-4Z" />
                        <path d="M22 2 11 13" />
                      </svg>
                      {t(
                        'integrations:zns.templates.useTemplate',
                        'Use Template',
                      )}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </div>
          ) : (
            <Alert
              variant="destructive"
              className="border-l-destructive border-l-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t(
                  'integrations:zns.templates.templateNotFound',
                  'Template Not Found',
                )}
              </AlertTitle>
              <AlertDescription>
                {t(
                  'integrations:zns.templates.templateNotFoundDescription',
                  'The template you are looking for does not exist or has been deleted.',
                )}
              </AlertDescription>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/zns/templates`)
                }
              >
                {t('common:back')}
              </Button>
            </Alert>
          )}
        </div>
      </PageBody>
    </>
  );
}
