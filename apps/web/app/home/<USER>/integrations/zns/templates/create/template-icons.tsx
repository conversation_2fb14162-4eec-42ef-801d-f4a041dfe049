import React from 'react';

export const CustomTemplateIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="12" fill="#0F2BE6"></rect>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99996 15.2929L17.6464 7.64648L18.3535 8.35359L9.99996 16.7072L5.64641 12.3536L6.35352 11.6465L9.99996 15.2929Z"
      fill="white"
    ></path>
  </svg>
);

export const AuthenticationTemplateIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="ZNS OTP">
      <g id="Union">
        <path
          d="M6 10.5C5.17157 10.5 4.5 11.1716 4.5 12C4.5 12.8284 5.17157 13.5 6 13.5C6.82843 13.5 7.5 12.8284 7.5 12C7.5 11.1716 6.82843 10.5 6 10.5Z"
          fill="#989BAE"
        ></path>
        <path
          d="M10.5 12C10.5 11.1716 11.1716 10.5 12 10.5C12.8284 10.5 13.5 11.1716 13.5 12C13.5 12.8284 12.8284 13.5 12 13.5C11.1716 13.5 10.5 12.8284 10.5 12Z"
          fill="#989BAE"
        ></path>
        <path
          d="M18 10.5C17.1716 10.5 16.5 11.1716 16.5 12C16.5 12.8284 17.1716 13.5 18 13.5C18.8284 13.5 19.5 12.8284 19.5 12C19.5 11.1716 18.8284 10.5 18 10.5Z"
          fill="#989BAE"
        ></path>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.5 7.5H21V1.5H3V7.5H1.5V16.5H3V22.5H21V16.5H22.5V7.5ZM4.5 3H19.5V7.5H4.5V3ZM4.5 21V16.5H19.5V21H4.5ZM21 9H3V15H21V9Z"
          fill="#989BAE"
        ></path>
      </g>
    </g>
  </svg>
);

export const RatingTemplateIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="ZNS Rating">
      <g id="Union">
        <path
          d="M13.347 8.6459L11.9999 4.5L10.6528 8.6459H6.29355L9.82027 11.2082L8.47318 15.3541L11.9999 12.7918L15.5266 15.3541L14.1795 11.2082L17.7062 8.6459H13.347Z"
          fill="#989BAE"
        ></path>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3 22.5H21V1.5H3V22.5ZM19.5 3H4.5V18H19.5V3ZM19.5 21V19.5H4.5V21H19.5Z"
          fill="#989BAE"
        ></path>
      </g>
    </g>
  </svg>
);

export const PaymentTemplateIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="ZNS Payment">
      <path
        id="Union"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.4985 1.5H6.82031V16.5781H4.0173C2.62709 16.5781 1.5 17.7052 1.5 19.0954V22.4997H13.6303V19.4648C13.6303 18.6841 14.2563 18.0656 15.1029 18.0648V18.0652C15.8963 18.0652 16.5769 18.606 16.5769 19.5392C16.5769 21.1742 17.9027 22.5 19.5377 22.5C21.1727 22.5 22.4985 21.1742 22.4985 19.5392V1.5ZM15.1044 16.5784C16.605 16.5792 18.0637 17.6765 18.0637 19.5392C18.0637 20.3531 18.7238 21.0133 19.5377 21.0133C20.3516 21.0133 21.0118 20.3531 21.0118 19.5392V2.98673H8.30704V16.5781H15.1044V16.5784ZM10.3691 5.49121H18.9516V6.97794H10.3691V5.49121ZM10.3691 9.03906H18.9516V10.5258H10.3691V9.03906ZM18.9516 12.5869H10.3691V14.0736H18.9516V12.5869ZM12.5029 18.0648H4.0173C3.44819 18.0648 2.98673 18.5263 2.98673 19.0954V21.013H12.1436V19.4648C12.1436 18.9473 12.2745 18.473 12.5029 18.0648Z"
        fill="#989BAE"
      ></path>
    </g>
  </svg>
);

export const VoucherTemplateIcon = () => (
  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="ZNS Voucher">
      <path
        id="Union"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 22.5H21V1.5H3V22.5ZM19.5 3H4.5V18H19.5V3ZM19.5 21V19.5H4.5V21H19.5ZM7.5 7.53837V8.05982C7.5 8.42551 7.58126 8.76185 7.74379 9.06885C7.90632 9.37585 8.1456 9.6219 8.46163 9.807C8.78217 9.9921 9.1772 10.0847 9.64673 10.0847C10.1117 10.0847 10.5023 9.9921 10.8183 9.807C11.1343 9.6219 11.3713 9.37585 11.5293 9.06885C11.6919 8.76185 11.7731 8.42551 11.7731 8.05982V7.53837C11.7731 7.16366 11.6919 6.8228 11.5293 6.5158C11.3713 6.2088 11.1321 5.96275 10.8115 5.77765C10.4955 5.59255 10.1005 5.5 9.62641 5.5C9.16591 5.5 8.77765 5.59255 8.46163 5.77765C8.1456 5.96275 7.90632 6.2088 7.74379 6.5158C7.58126 6.8228 7.5 7.16366 7.5 7.53837ZM8.79345 8.05982V7.53837C8.79345 7.37133 8.8228 7.21783 8.88149 7.07788C8.94469 6.93341 9.0395 6.81828 9.16591 6.73251C9.29233 6.64221 9.44582 6.59707 9.62641 6.59707C9.82054 6.59707 9.97856 6.64221 10.1005 6.73251C10.2269 6.81828 10.3194 6.93341 10.3781 7.07788C10.4413 7.21783 10.4729 7.37133 10.4729 7.53837V8.05982C10.4729 8.21783 10.4436 8.36907 10.3849 8.51354C10.3262 8.6535 10.2359 8.76862 10.114 8.85892C9.9921 8.94469 9.83409 8.98758 9.63995 8.98758C9.45485 8.98758 9.29684 8.94469 9.16591 8.85892C9.0395 8.76862 8.94469 8.6535 8.88149 8.51354C8.8228 8.36907 8.79345 8.21783 8.79345 8.05982ZM12.2133 13.0847V13.6061C12.2133 13.9763 12.2946 14.3149 12.4571 14.6219C12.6242 14.9289 12.8679 15.1749 13.1885 15.36C13.509 15.5451 13.9041 15.6377 14.3736 15.6377C14.8386 15.6377 15.2291 15.5451 15.5451 15.36C15.8612 15.1749 16.0982 14.9289 16.2562 14.6219C16.4187 14.3149 16.5 13.9763 16.5 13.6061V13.0847C16.5 12.7144 16.4187 12.3758 16.2562 12.0688C16.0937 11.7619 15.8544 11.5158 15.5384 11.3307C15.2223 11.1456 14.8296 11.053 14.36 11.053C13.895 11.053 13.5023 11.1456 13.1817 11.3307C12.8657 11.5158 12.6242 11.7619 12.4571 12.0688C12.2946 12.3758 12.2133 12.7144 12.2133 13.0847ZM13.5135 13.6061V13.0847C13.5135 12.9221 13.5451 12.7709 13.6084 12.6309C13.6716 12.4865 13.7641 12.3713 13.886 12.2856C14.0124 12.1953 14.1704 12.1501 14.36 12.1501C14.5497 12.1501 14.7054 12.1953 14.8273 12.2856C14.9537 12.3713 15.0485 12.4865 15.1117 12.6309C15.1749 12.7709 15.2065 12.9221 15.2065 13.0847V13.6061C15.2065 13.7686 15.1817 13.9221 15.1321 14.0666C15.0869 14.2111 15.0034 14.3262 14.8815 14.412C14.7596 14.4977 14.5903 14.5406 14.3736 14.5406C14.202 14.5406 14.0508 14.4977 13.9199 14.412C13.7935 14.3262 13.6941 14.2111 13.6219 14.0666C13.5497 13.9221 13.5135 13.7686 13.5135 13.6061ZM10.026 14.7573L14.8409 7.05079L13.8928 6.54966L9.07788 14.2562L10.026 14.7573Z"
        fill="#989BAE"
      ></path>
    </g>
  </svg>
);

export const getTemplateIcon = (iconType: string) => {
  switch (iconType) {
    case 'custom':
      return <CustomTemplateIcon />;
    case 'authentication':
      return <AuthenticationTemplateIcon />;
    case 'rating':
      return <RatingTemplateIcon />;
    case 'payment':
      return <PaymentTemplateIcon />;
    case 'voucher':
      return <VoucherTemplateIcon />;
    default:
      return <CustomTemplateIcon />;
  }
};
