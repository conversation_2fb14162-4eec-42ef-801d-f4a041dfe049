import { enhance<PERSON>outeHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createTeamAccountsAuthApi } from '@kit/team-accounts/api';

import appConfig from '~/config/app.config';
import { createCorsResponse } from '~/lib/cors';

import { ZaloAuthSchema } from './schema';

const resolveAccountId = async (
  client: ReturnType<typeof getSupabaseServerAdminClient>,
  tempThemeId?: string | null,
  themeId?: string | null,
): Promise<string> => {
  if (tempThemeId) {
    const { data, error } = await client
      .from('temp_themes')
      .select('account_id, expires_at')
      .eq('id', tempThemeId)
      .single();

    if (error || !data) throw new Error('Invalid or expired temp_theme_id');
    if (new Date(data.expires_at) < new Date())
      throw new Error('Temporary theme has expired');
    return data.account_id;
  }

  if (themeId) {
    const { data, error } = await client
      .from('account_themes')
      .select('account_id')
      .eq('id', themeId)
      .single();

    if (error || !data) throw new Error('Invalid theme_id');
    return data.account_id;
  }

  const { data, error } = await client
    .from('accounts')
    .select('id')
    .eq('slug', 'makerkit')
    .single();

  if (error || !data) throw new Error('Default team not found');
  return data.id;
};

export const POST = enhanceRouteHandler(
  async ({ body, request }) => {
    const logger = await getLogger();
    const adminClient = getSupabaseServerAdminClient();
    const authApi = createTeamAccountsAuthApi(adminClient);

    try {
      const accountId = await resolveAccountId(
        adminClient,
        body.temp_theme_id,
        body.theme_id,
      );

      const customer = await authApi.getOrCreateCustomer(
        accountId,
        body.access_token,
      );
      if (!customer) throw new Error('Failed to authenticate customer');

      // Generate and verify magic link in one step
      const { data: linkData, error: linkError } =
        await adminClient.auth.admin.generateLink({
          type: 'magiclink',
          email: customer.email,
          options: {
            data: { account_id: accountId, role: customer.accountRole },
          },
        });

      if (linkError || !linkData?.properties?.hashed_token) {
        throw new Error('Failed to generate auth token');
      }

      const { data: sessionData, error: sessionError } =
        await adminClient.auth.verifyOtp({
          token_hash: linkData.properties.hashed_token,
          type: 'magiclink',
        });

      if (sessionError || !sessionData?.session) {
        throw new Error('Failed to create session');
      }

      logger.info(
        { userId: customer.userId, accountId },
        'Zalo auth successful',
      );

      const response = createCorsResponse(
        request,
        {
          success: true,
          user: {
            id: customer.userId,
            name: customer.name,
            picture: customer.picture,
            phone: customer.phone,
          },
          access_token: sessionData.session.access_token,
          refresh_token: sessionData.session.refresh_token,
          theme_id: body.theme_id || null,
          temp_theme_id: body.temp_theme_id || null,
        },
        200,
      );

      response.cookies.set({
        name: `sb-${process.env.NEXT_PUBLIC_SUPABASE_URL!.split('.')[0]}-auth-token`,
        value: JSON.stringify({
          access_token: sessionData.session.access_token,
          refresh_token: sessionData.session.refresh_token,
        }),
        httpOnly: true,
        secure: appConfig.production,
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });

      return response;
    } catch (error: any) {
      logger.error(
        { error: error.message, stack: error.stack, body },
        'Zalo auth failed',
      );
      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { schema: ZaloAuthSchema, auth: false },
);
