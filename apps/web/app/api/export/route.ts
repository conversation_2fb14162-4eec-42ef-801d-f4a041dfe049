import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

import { 
  exportDataAsCsv, 
  exportDataAsExcel, 
  getTemplates 
} from '~/home/<USER>/import-export/_lib/server/export-data';

// Schema cho query parameters
const ExportQuerySchema = z.object({
  resource: z.enum(['products', 'categories', 'orders', 'customers', 'vouchers', 'analytics']),
  template: z.string().uuid().optional(),
  format: z.enum(['csv', 'excel']).default('csv'),
  accountId: z.string().uuid(),
});

/**
 * GET /api/export
 * Xuất dữ liệu dựa trên template
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    // Lấy query parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    
    // Parse và validate query parameters
    const { resource, template, format, accountId } = ExportQuerySchema.parse({
      resource: searchParams.get('resource'),
      template: searchParams.get('template'),
      format: searchParams.get('format'),
      accountId: searchParams.get('accountId'),
    });
    
    // Nếu không có template, trả về lỗi
    if (!template) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }
    
    // Lấy thông tin template
    const templates = await getTemplates(resource, accountId);
    const templateData = templates.find(t => t.id === template);
    
    if (!templateData) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // Lấy danh sách fields từ template
    const fields = templateData.fields || Object.values(templateData.mapping || {});
    
    if (!fields || fields.length === 0) {
      return NextResponse.json(
        { error: 'No fields found in template' },
        { status: 400 }
      );
    }
    
    // Xuất dữ liệu theo định dạng
    if (format === 'csv') {
      const csvData = await exportDataAsCsv(
        resource,
        fields,
        accountId,
        { filters: templateData.filters }
      );
      
      // Trả về file CSV
      return new NextResponse(csvData, {
        headers: {
          'Content-Type': 'text/csv;charset=utf-8',
          'Content-Disposition': `attachment; filename=${resource}_export.csv`,
        },
      });
    } else {
      const excelData = await exportDataAsExcel(
        resource,
        fields,
        accountId,
        { filters: templateData.filters }
      );
      
      // Trả về dữ liệu JSON để client xử lý thành Excel
      return NextResponse.json(excelData);
    }
  } catch (error: any) {
    logger.error({ error }, 'Error exporting data');
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'Failed to export data' },
      { status: 500 }
    );
  }
}
