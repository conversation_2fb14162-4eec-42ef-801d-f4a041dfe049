import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from '@/lib/integrations/connectors/ipos/ipos-connector';

const logger = getLogger({ service: 'api-ipos-points-history' });

/**
 * API bắc cầu để lấy lịch sử tích điểm từ iPOS
 * @param request Request
 */
export async function GET(request: NextRequest) {
  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const membershipId = searchParams.get('membershipId');

    // Kiểm tra tham số bắt buộc
    if (!accountId || !membershipId) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // L<PERSON>y thông tin tích hợp iPOS
    const integration = await prisma.integrations.findFirst({
      where: {
        account_id: accountId,
        platform: 'ipos',
        status: 'active'
      }
    });

    if (!integration) {
      return NextResponse.json(
        { success: false, message: 'No active iPOS integration found' },
        { status: 404 }
      );
    }

    // Lấy cấu hình từ integration
    const config = integration.config as any;
    const credentials = {
      access_token: config.access_token,
      pos_parent: config.pos_parent,
      pos_id: config.pos_id,
      baseUrl: config.baseUrl || 'https://api.foodbook.vn'
    };

    // Khởi tạo connector
    const connector = new IPOSConnector(credentials, logger);

    // Gọi API iPOS để lấy lịch sử tích điểm
    const response = await connector.getMembershipLog(membershipId);

    // Trả về kết quả
    return NextResponse.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    logger.error({ error }, 'Error getting membership log from iPOS');
    return NextResponse.json(
      { success: false, message: (error as Error).message },
      { status: 500 }
    );
  }
}
