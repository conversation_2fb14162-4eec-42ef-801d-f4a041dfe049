import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Schema for mapping creation request
const CreateMappingSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  template_id: z.string().uuid(),
  module: z.string().min(1),
  event_type: z.string().min(1),
  parameter_mapping: z.record(z.string(), z.string()),
  recipient_path: z.string().optional(),
  conditions: z.any().optional(),
  team_account_id: z.string().uuid(),
  created_by: z.string().uuid().optional(),
});

// Schema for mapping update request
const UpdateMappingSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  template_id: z.string().uuid().optional(),
  module: z.string().min(1).optional(),
  event_type: z.string().min(1).optional(),
  parameter_mapping: z.record(z.string(), z.string()).optional(),
  recipient_path: z.string().optional(),
  conditions: z.any().optional(),
  enabled: z.boolean().optional(),
  team_account_id: z.string().uuid(),
});

/**
 * GET /api/zns/mappings
 * Lấy danh sách mapping
 */
export async function GET(request: NextRequest) {
  try {
    // Lấy query parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    const module = searchParams.get('module');
    const teamAccountId = searchParams.get('teamAccountId');
    
    if (!teamAccountId) {
      return NextResponse.json(
        { error: 'Team account ID is required' },
        { status: 400 }
      );
    }
    
    // Tạo Supabase client
    const supabase = getSupabaseServerClient();
    
    // Tạo query
    let query = supabase
      .from('zns_mappings')
      .select(`
        *,
        template:zns_templates(*)
      `)
      .eq('team_account_id', teamAccountId);
    
    // Thêm filter theo module nếu có
    if (module) {
      query = query.eq('module', module);
    }
    
    // Thực hiện query
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching ZNS mappings:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error handling ZNS mappings request:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/zns/mappings
 * Tạo mapping mới
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate request
    const validatedData = CreateMappingSchema.parse(body);
    
    // Tạo Supabase client
    const supabase = getSupabaseServerClient();
    
    // Kiểm tra template tồn tại và thuộc về team
    const { data: template, error: templateError } = await supabase
      .from('zns_templates')
      .select('*')
      .eq('id', validatedData.template_id)
      .single();
    
    if (templateError || !template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // Tạo mapping
    const { data, error } = await supabase
      .from('zns_mappings')
      .insert({
        name: validatedData.name,
        description: validatedData.description,
        template_id: validatedData.template_id,
        module: validatedData.module,
        event_type: validatedData.event_type,
        parameter_mapping: validatedData.parameter_mapping,
        recipient_path: validatedData.recipient_path || 'customer.phone',
        conditions: validatedData.conditions,
        team_account_id: validatedData.team_account_id,
        created_by: validatedData.created_by,
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating ZNS mapping:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error handling ZNS mapping creation:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/zns/mappings
 * Cập nhật mapping
 */
export async function PATCH(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate request
    const validatedData = UpdateMappingSchema.parse(body);
    
    // Tạo Supabase client
    const supabase = getSupabaseServerClient();
    
    // Kiểm tra mapping tồn tại và thuộc về team
    const { data: existingMapping, error: mappingError } = await supabase
      .from('zns_mappings')
      .select('*')
      .eq('id', validatedData.id)
      .eq('team_account_id', validatedData.team_account_id)
      .single();
    
    if (mappingError || !existingMapping) {
      return NextResponse.json(
        { error: 'Mapping not found or not authorized' },
        { status: 404 }
      );
    }
    
    // Chuẩn bị dữ liệu cập nhật
    const updateData: any = {};
    
    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.template_id !== undefined) updateData.template_id = validatedData.template_id;
    if (validatedData.module !== undefined) updateData.module = validatedData.module;
    if (validatedData.event_type !== undefined) updateData.event_type = validatedData.event_type;
    if (validatedData.parameter_mapping !== undefined) updateData.parameter_mapping = validatedData.parameter_mapping;
    if (validatedData.recipient_path !== undefined) updateData.recipient_path = validatedData.recipient_path;
    if (validatedData.conditions !== undefined) updateData.conditions = validatedData.conditions;
    if (validatedData.enabled !== undefined) updateData.enabled = validatedData.enabled;
    
    // Thêm updated_at
    updateData.updated_at = new Date().toISOString();
    
    // Cập nhật mapping
    const { data, error } = await supabase
      .from('zns_mappings')
      .update(updateData)
      .eq('id', validatedData.id)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating ZNS mapping:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error handling ZNS mapping update:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/zns/mappings
 * Xóa mapping
 */
export async function DELETE(request: NextRequest) {
  try {
    // Lấy query parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    const id = searchParams.get('id');
    const teamAccountId = searchParams.get('teamAccountId');
    
    if (!id || !teamAccountId) {
      return NextResponse.json(
        { error: 'Mapping ID and Team account ID are required' },
        { status: 400 }
      );
    }
    
    // Tạo Supabase client
    const supabase = getSupabaseServerClient();
    
    // Kiểm tra mapping tồn tại và thuộc về team
    const { data: existingMapping, error: mappingError } = await supabase
      .from('zns_mappings')
      .select('*')
      .eq('id', id)
      .eq('team_account_id', teamAccountId)
      .single();
    
    if (mappingError || !existingMapping) {
      return NextResponse.json(
        { error: 'Mapping not found or not authorized' },
        { status: 404 }
      );
    }
    
    // Xóa mapping
    const { error } = await supabase
      .from('zns_mappings')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Error deleting ZNS mapping:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error handling ZNS mapping deletion:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
