import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createCustomZnsTemplate, editZnsTemplate } from '@kit/zns';

// Schema for template creation request
const CreateTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
  teamAccountId: z.string().uuid().optional(),
});

// Schema for template editing request
const EditTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  templateId: z.string(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
  teamAccountId: z.string().uuid().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();

    // Create Supabase client
    const supabase = getSupabaseServerClient();

    // Check if this is a create or edit request
    const isEdit = request.nextUrl.searchParams.get('action') === 'edit';

    if (isEdit) {
      // Validate edit request
      const validatedData = EditTemplateSchema.parse(body);

      // Call edit template function
      const result = await editZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        {
          template_id: validatedData.templateId,
          template_name: validatedData.templateName,
          template_type: Number(validatedData.templateType),
          tag: Number(validatedData.templateTag),
          layout: validatedData.layout,
          params: validatedData.params || [],
          note: validatedData.templateNote,
          tracking_id: validatedData.trackingId || `template_edit_${Date.now()}`,
          team_account_id: validatedData.teamAccountId,
        },
        validatedData.teamAccountId
      );

      // Return success response
      return NextResponse.json({
        data: result,
        error: 0,
        message: 'Template edited successfully',
      });
    } else {
      // Validate create request
      const validatedData = CreateTemplateSchema.parse(body);

      // Call create template function
      const result = await createCustomZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        validatedData.templateName,
        Number(validatedData.templateTag),
        validatedData.layout,
        validatedData.params,
        validatedData.templateNote || 'Template for business notifications',
        validatedData.trackingId || `template_${Date.now()}`,
        validatedData.teamAccountId, // Pass teamAccountId
      );

      // Return success response
      return NextResponse.json({
        data: result,
        error: 0,
        message: 'Template created successfully',
      });
    }
  } catch (error: any) {
    console.error('Error handling template request:', error);

    // Return error response
    return NextResponse.json(
      {
        error: error.message,
        details: error.response?.data || 'Unknown error',
      },
      { status: error.status || 500 },
    );
  }
}
