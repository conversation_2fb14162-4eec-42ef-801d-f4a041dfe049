import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createCustomZnsTemplate, editZnsTemplate } from '@kit/zns';

// Schema for template creation request
const CreateTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
  teamAccountId: z.string().uuid().optional(),
});

// Schema for template editing request
const EditTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  templateId: z.string(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
  teamAccountId: z.string().uuid().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();

    // Create Supabase client
    const supabase = getSupabaseServerClient();

    // Check if this is a create or edit request
    const isEdit = request.nextUrl.searchParams.get('action') === 'edit';

    if (isEdit) {
      // Validate edit request
      const validatedData = EditTemplateSchema.parse(body);

      // Call edit template function
      const result = await editZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        {
          template_id: validatedData.templateId,
          template_name: validatedData.templateName,
          template_type: Number(validatedData.templateType),
          tag: Number(validatedData.templateTag),
          layout: validatedData.layout,
          params: validatedData.params || [],
          note: validatedData.templateNote,
          tracking_id: validatedData.trackingId || `template_edit_${Date.now()}`,
          team_account_id: validatedData.teamAccountId,
        },
        validatedData.teamAccountId
      );

      // Kiểm tra kết quả
      if (result.success === false) {
        // Trả về lỗi với mã 400 và thông báo chi tiết
        return NextResponse.json(
          {
            error: result.error.message,
            details: result.error.details,
            code: result.error.code,
            errorCode: result.error.error
          },
          { status: 400 }
        );
      }

      // Return success response
      return NextResponse.json({
        data: result.data,
        error: 0,
        message: 'Template edited successfully',
      });
    } else {
      // Validate create request
      const validatedData = CreateTemplateSchema.parse(body);

      // Call create template function
      const result = await createCustomZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        validatedData.templateName,
        Number(validatedData.templateTag),
        validatedData.layout,
        validatedData.params,
        validatedData.templateNote || 'Template for business notifications',
        validatedData.trackingId || `template_${Date.now()}`,
        validatedData.teamAccountId, // Pass teamAccountId
      );

      // Kiểm tra kết quả
      if (result.success === false) {
        // Trả về lỗi với mã 400 và thông báo chi tiết
        return NextResponse.json(
          {
            error: result.error.message,
            details: result.error.details,
            code: result.error.code,
            errorCode: result.error.error
          },
          { status: 400 }
        );
      }

      // Return success response
      return NextResponse.json({
        data: result.data,
        error: 0,
        message: 'Template created successfully',
      });
    }
  } catch (error: any) {
    console.error('Error handling template request:', error);

    // Kiểm tra xem lỗi có phải từ Zalo API không
    if (error.response && error.response.data) {
      const zaloError = error.response.data;

      // Lấy thông báo lỗi chi tiết
      let details = 'Lỗi không xác định';

      if (zaloError.error === -1131) {
        details = 'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
      } else if (zaloError.error === -1132) {
        details = 'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
      } else if (zaloError.error === -1137) {
        details = 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
      } else if (zaloError.error === -124) {
        details = 'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
      }

      return NextResponse.json(
        {
          error: zaloError.message || 'Lỗi từ Zalo API',
          details,
          code: `ZNS_ERROR_${zaloError.error}`,
          errorCode: zaloError.error
        },
        { status: 400 }
      );
    }

    // Return error response
    return NextResponse.json(
      {
        error: error.message,
        details: error.response?.data || 'Unknown error',
      },
      { status: error.status || 500 },
    );
  }
}
