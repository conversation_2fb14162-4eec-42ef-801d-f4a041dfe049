import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createCustomZnsTemplate, editZnsTemplate } from '@kit/zns';

// Schema for template creation request
const CreateTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  accountId: z.string().uuid(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
});

// Schema for template editing request
const EditTemplateSchema = z.object({
  oaConfigId: z.string().uuid(),
  accountId: z.string().uuid(),
  templateId: z.string(),
  templateName: z.string().min(1),
  templateTag: z.string().or(z.number()),
  templateType: z.string().or(z.number()),
  layout: z.any(),
  params: z.array(z.any()).optional(),
  templateNote: z.string().optional(),
  trackingId: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const forMapping = searchParams.get('forMapping'); // Tham số để lọc cho mapping

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = getSupabaseServerClient();

    // Build query
    let query = supabase
      .from('zns_templates')
      .select('*')
      .eq('account_id', accountId);

    // Nếu là cho mapping, chỉ lấy templates enabled và có status phù hợp
    if (forMapping === 'true') {
      query = query
        .eq('enabled', true)
        .in('status', ['ENABLE', 'PENDING_REVIEW']); // Cho phép cả approved và pending để test
    }

    // Fetch templates for the account
    const { data: templates, error } = await query
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching templates:', error);
      return NextResponse.json(
        { error: 'Failed to fetch templates' },
        { status: 500 }
      );
    }

    console.log('Templates fetched from database:', {
      accountId,
      forMapping,
      count: templates?.length || 0,
      templates: templates?.map(t => ({ id: t.id, template_id: t.template_id, template_name: t.template_name, enabled: t.enabled, status: t.status }))
    });

    return NextResponse.json({
      data: templates || [],
      error: 0,
      message: 'Templates fetched successfully',
    });
  } catch (error: any) {
    console.error('Error in GET /api/zns/templates:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();

    // Create Supabase client
    const supabase = getSupabaseServerClient();

    // Check if this is a create or edit request
    const isEdit = request.nextUrl.searchParams.get('action') === 'edit';

    if (isEdit) {
      // Validate edit request
      const validatedData = EditTemplateSchema.parse(body);

      // Call edit template function
      const result = await editZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        {
          template_id: validatedData.templateId,
          template_name: validatedData.templateName,
          template_type: Number(validatedData.templateType),
          tag: Number(validatedData.templateTag),
          layout: validatedData.layout,
          params: validatedData.params || [],
          note: validatedData.templateNote,
          tracking_id:
            validatedData.trackingId || `template_edit_${Date.now()}`,
        },
        validatedData.accountId,
      );

      // Kiểm tra kết quả
      if (result.success === false) {
        // Trả về lỗi với mã 400 và thông báo chi tiết
        return NextResponse.json(
          {
            error: result.error.message,
            details: result.error.details,
            code: result.error.code,
            errorCode: result.error.error,
          },
          { status: 400 },
        );
      }

      // Return success response
      return NextResponse.json({
        data: result.data,
        error: 0,
        message: 'Template edited successfully',
      });
    } else {
      // Validate create request
      const validatedData = CreateTemplateSchema.parse(body);

      // Call create template function
      const result = await createCustomZnsTemplate(
        supabase,
        validatedData.oaConfigId,
        validatedData.accountId,
        validatedData.templateName,
        Number(validatedData.templateTag),
        validatedData.layout,
        validatedData.params,
        validatedData.templateNote || 'Template for business notifications',
        validatedData.trackingId || `template_${Date.now()}`,
      );

      // Kiểm tra kết quả
      if (result.success === false) {
        // Trả về lỗi với mã 400 và thông báo chi tiết
        return NextResponse.json(
          {
            error: result.error.message,
            details: result.error.details,
            code: result.error.code,
            errorCode: result.error.error,
          },
          { status: 400 },
        );
      }

      // Return success response
      return NextResponse.json({
        data: result.data,
        error: 0,
        message: 'Template created successfully',
      });
    }
  } catch (error: any) {
    console.error('Error handling template request:', error);

    // Kiểm tra xem lỗi có phải từ Zalo API không
    if (error.response && error.response.data) {
      const zaloError = error.response.data;

      // Lấy thông báo lỗi chi tiết
      let details = 'Lỗi không xác định';

      if (zaloError.error === -1131) {
        details =
          'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
      } else if (zaloError.error === -1132) {
        details =
          'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
      } else if (zaloError.error === -1137) {
        details =
          'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
      } else if (zaloError.error === -124) {
        details =
          'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
      }

      return NextResponse.json(
        {
          error: zaloError.message || 'Lỗi từ Zalo API',
          details,
          code: `ZNS_ERROR_${zaloError.error}`,
          errorCode: zaloError.error,
        },
        { status: 400 },
      );
    }

    // Return error response
    return NextResponse.json(
      {
        error: error.message,
        details: error.response?.data || 'Unknown error',
      },
      { status: error.status || 500 },
    );
  }
}
