-- C<PERSON><PERSON> nh<PERSON>t bảng zns_templates
ALTER TABLE public.zns_templates
ADD COLUMN IF NOT EXISTS content TEXT,
ADD COLUMN IF NOT EXISTS template_name TEXT,
ADD COLUMN IF NOT EXISTS status TEXT,
ADD COLUMN IF NOT EXISTS tag TEXT,
ADD COLUMN IF NOT EXISTS preview_url TEXT,
ADD COLUMN IF NOT EXISTS team_account_id UUID REFERENCES public.team_accounts(id),
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;

-- Cập nhật bảng zns_usage
ALTER TABLE public.zns_usage
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES public.zns_templates(id),
ADD COLUMN IF NOT EXISTS mapping_id UUID,
ADD COLUMN IF NOT EXISTS recipient TEXT,
ADD COLUMN IF NOT EXISTS message_id TEXT,
ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Tạo bảng zns_mappings
CREATE TABLE IF NOT EXISTS public.zns_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  template_id UUID REFERENCES public.zns_templates(id),
  module TEXT NOT NULL, -- 'orders', 'education', 'marketing', etc.
  event_type TEXT NOT NULL, -- 'created', 'updated', 'payment_received', etc.
  parameter_mapping JSONB NOT NULL DEFAULT '{}'::jsonb, -- Mapping between event data and template parameters
  recipient_path TEXT NOT NULL DEFAULT 'customer.phone',
  conditions JSONB,
  enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),
  team_account_id UUID REFERENCES public.team_accounts(id)
);

-- Thêm RLS cho zns_mappings
ALTER TABLE public.zns_mappings ENABLE ROW LEVEL SECURITY;

-- Thêm policy cho zns_mappings
CREATE POLICY "Users can manage their team's ZNS mappings"
  ON public.zns_mappings
  FOR ALL
  USING (public.has_role_on_account(team_account_id))
  WITH CHECK (public.has_role_on_account(team_account_id));

-- Thêm index cho zns_mappings
CREATE INDEX IF NOT EXISTS idx_zns_mappings_template_id ON public.zns_mappings(template_id);
CREATE INDEX IF NOT EXISTS idx_zns_mappings_module_event ON public.zns_mappings(module, event_type);
CREATE INDEX IF NOT EXISTS idx_zns_mappings_team_account_id ON public.zns_mappings(team_account_id);
