
// <PERSON>h sách template mẫu
export const templateSamples = [
  {
    industryName: 'Thương mại điện tử',
    industryId: 1,
    presetTemplate: [
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 246510,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Xem đơn hàng đã đặt',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Xác nhận đơn hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cảm ơn <name> đã mua hàng tại cửa hàng. Đơn hàng của bạn đã được xác nhận với chi tiết như sau:',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<order_code>',
                      key: 'Mã đơn',
                    },
                    {
                      type: '0',
                      value: '<phone_number>',
                      key: 'Điện thoại',
                    },
                    {
                      type: '0',
                      value: '<price>',
                      key: 'Giá tiền',
                    },
                    {
                      type: '0',
                      value: '<status>',
                      key: 'Trạng thái',
                    },
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Ngày đặt hàng',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '17',
              name: 'OA_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'order_code',
              limit: 11,
              type: '1',
              sampleValue: null,
            },
            {
              znsType: '15',
              name: 'phone_number',
              limit: 11,
              type: '1',
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'price',
              limit: 11,
              type: '1',
              sampleValue: null,
            },
            {
              znsType: '14',
              name: 'status',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Xác nhận đơn hàng","subTitle":"Cảm ơn <name> đã mua hàng tại cửa hàng. Đơn hàng của bạn đã được xác nhận với chi tiết như sau:","rows":[{"key":"Mã đơn","value":"<order_code>","type":"0"},{"key":"Điện thoại","value":"<phone_number>","type":"0"},{"key":"Giá tiền","value":"<price>","type":"0"},{"key":"Trạng thái","value":"<status>","type":"0"},{"key":"Ngày đặt hàng","value":"<date>","type":"0"}],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Xem đơn hàng đã đặt","data":"https://zalo.cloud/"}],"params":[{"name":"name","znsType":"1","type":"0","limit":30},{"name":"OA_name","znsType":"17","type":"0","limit":30},{"name":"order_code","znsType":"11","type":"1","limit":11},{"name":"phone_number","znsType":"15","type":"1","limit":11},{"name":"price","znsType":"18","type":"1","limit":11},{"name":"status","znsType":"14","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10}]}',
        timeout: '7200',
        createdAt: 1676362423,
        appId: 'BAUVVB9',
        notifyMessage:
            'Xác nhận đơn hàng\nCảm ơn <name> đã mua hàng tại <OA_name>. Đơn hàng của bạn đã được xác nhận với chi tiết như sau:\nMã đơn: <order_code>\nĐiện thoại: <phone_number>',
        oaName: 'Zalo Notification Service',
        id: '4563007696273097126',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'OA_name',
            sampleValue: 'Bàn phím Razer',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 15,
            zteType: 'STRING',
            dataType: 'string',
            name: 'phone_number',
            sampleValue: '0969874535',
            maxLength: 50,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'price',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 14,
            zteType: 'STRING',
            dataType: 'string',
            name: 'status',
            sampleValue: 'giao dịch thành công',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"order_code":"order_code","date":"01/08/2020","price":100,"name":"name","phone_number":"phone_number","OA_name":"OA_name","status":"status"}',
        name: 'Xác nhận đơn hàng ',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 246772,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Xem chi tiết đơn hàng',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Xác nhận thanh toán',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Xin chào <name>, cảm ơn bạn đã mua hàng tại cửa hàng. Chúng tôi đã ghi nhận thanh toán của bạn với chi tiết như sau:',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<order_code>',
                      key: 'Mã đơn',
                    },
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Ngày đặt hàng',
                    },
                    {
                      type: '0',
                      value: '<price>',
                      key: 'Giá tiền',
                    },
                    {
                      type: '0',
                      value: '<payment>',
                      key: 'Hình thức thanh toán',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '17',
              name: 'OA_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'order_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'price',
              limit: 11,
              type: '1',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'payment',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Xác nhận thanh toán","subTitle":"Xin chào <name>, cảm ơn bạn đã mua hàng tại cửa hàng. Chúng tôi đã ghi nhận thanh toán của bạn với chi tiết như sau:","rows":[{"key":"Mã đơn","value":"<order_code>","type":"0"},{"key":"Ngày đặt hàng","value":"<date>","type":"0"},{"key":"Giá tiền","value":"<price>","type":"0"},{"key":"Hình thức thanh toán","value":"<payment>","type":"0"}],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Xem chi tiết đơn hàng","data":"https://zalo.cloud/"}],"params":[{"name":"name","znsType":"1","type":"0","limit":30},{"name":"OA_name","znsType":"17","type":"0","limit":30},{"name":"order_code","znsType":"11","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10},{"name":"price","znsType":"18","type":"1","limit":11},{"name":"payment","znsType":"12","type":"0","limit":30}]}',
        timeout: '7200',
        createdAt: 1676537169,
        appId: 'BAUVVB9',
        notifyMessage:
            'Xác nhận thanh toán\nXin chào <name>, cảm ơn bạn đã mua hàng tại <OA_name>. Chúng tôi đã ghi nhận thanh toán của bạn với chi tiết như sau:\nMã đơn: <order_code>\nNgày đặt hàng: <date>',
        oaName: 'Zalo Notification Service',
        id: '200376629443773540',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'OA_name',
            sampleValue: 'Bàn phím Razer',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 10,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'price',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'payment',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"order_code":"order_code","date":"01/08/2020","price":100,"name":"name","payment":"payment","OA_name":"OA_name"}',
        name: 'Xác nhận thanh toán',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272023,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo giao hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Xin chào <name>, đơn hàng <order_code> đặt vào vào ngày <date> đã <status>.\nCám ơn bạn đã quan tâm đến sản phẩm của chúng tôi.\n',
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '14',
              name: 'status',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'order_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Thông báo giao hàng","paragraphs":["Xin chào <name>, đơn hàng <order_code> đặt vào vào ngày <date> đã <status>.\\nCám ơn bạn đã quan tâm đến sản phẩm của chúng tôi.\\n"],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ bộ phận CSKH","data":"********"}],"params":[{"name":"name","znsType":"1","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10},{"name":"status","znsType":"14","type":"0","limit":30},{"name":"order_code","znsType":"1","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689870420,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo giao hàng\nXin chào <name>, đơn hàng <order_code> đặt vào vào ngày <date> đã <status>.\nCám ơn bạn đã quan tâm đến sản phẩm của chúng tôi.\n',
        oaName: 'Zalo Notification Service',
        id: '2294614350786153614',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 6,
            zteType: 'STRING',
            dataType: 'string',
            name: 'status',
            sampleValue: 'giao dịch thành công',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"date":"01/08/2020","order_code":"order_code","name":"name","status":"status"}',
        name: 'Thông báo giao hàng',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 343874,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/2024/06/26/2429bdc7f2d170ce3eea2d5ead9534fb.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/169b0664b8de6b7ecb2c92bd9ab9d4e2.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Đánh giá đơn hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng và mua đơn hàng <order_code> của chúng tôi vào ngày <order_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
                },
              },
              {
                RATING: {
                  ratings: [
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 1,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Rất không hài lòng',
                    },
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 2,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Không hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 3,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Bình thường',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 4,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 5,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Rất hài lòng',
                    },
                  ],
                  header: null,
                  scale: null,
                  responseMessage: null,
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: 'Nguyễn Lê Minh Khoa',
              znsAliasParam: null,
            },
            {
              znsType: '11',
              name: 'order_code',
              limit: null,
              type: null,
              sampleValue: 'ABC123',
              znsAliasParam: null,
            },
            {
              znsType: '19',
              name: 'order_date',
              limit: null,
              type: null,
              sampleValue: '30/05/2024',
              znsAliasParam: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 4,
        amountQuota: '0',
        content: '',
        timeout: '7200',
        createdAt: 1719375590,
        appId: '4KU4PV7',
        notifyMessage:
            'Đánh giá đơn hàng\nCám ơn quý khách <customer_name> đã tin tưởng và mua đơn hàng <order_code> của chúng tôi vào ngày <order_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
        oaName: 'Zalo Notification Service',
        id: '5696783325193060599',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'ABC123',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'order_date',
            sampleValue: '30/05/2024',
            maxLength: 20,
          },
        ],
        productId: '4804105510298600518',
        src: 0,
        appName: 'ZNS Service',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 2,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"order_code":"order_code","order_date":"01/08/2020","$zReqId":"$zReqId","$zReqTime":"$zReqTime","customer_name":"customer_name"}',
        name: 'Chăm sóc, thu thập ý kiến của KH sau khi mua hàng',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272025,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Chi tiết khuyến mãi',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Tri ân khách hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_code>',
                      key: 'Mã khách hàng',
                    },
                    {
                      type: '0',
                      value: '<voucher_code>',
                      key: 'Mã voucher',
                    },
                    {
                      type: '0',
                      value: '<voucher_discount>',
                      key: 'Ưu đãi',
                    },
                    {
                      type: '0',
                      value: '<expire_date>',
                      key: 'Hạn sử dụng',
                    },
                    {
                      type: '0',
                      value: '<voucher_condition>',
                      key: 'Điều kiện sử dụng',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'customer_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_discount',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'expire_date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_condition',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Tri ân khách hàng","subTitle":null,"paragraphs":["Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\\n"],"rows":[{"key":"Mã khách hàng","value":"<customer_code>","type":"0"},{"key":"Mã voucher","value":"<voucher_code>","type":"0"},{"key":"Ưu đãi","value":"<voucher_discount>","type":"0"},{"key":"Hạn sử dụng","value":"<expire_date>","type":"0"},{"key":"Điều kiện sử dụng","value":"<voucher_condition>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Chi tiết khuyến mãi","data":"https://zalo.cloud/"}],"params":[{"name":"customer_name","znsType":"1","type":"0","limit":30},{"name":"customer_code","znsType":"11","type":"0","limit":30},{"name":"voucher_code","znsType":"12","type":"0","limit":30},{"name":"voucher_discount","znsType":"12","type":"0","limit":30},{"name":"expire_date","znsType":"19","type":"2","limit":10},{"name":"voucher_condition","znsType":"12","type":"0","limit":30}]}',
        timeout: '7200',
        createdAt: 1689870897,
        appId: 'BAUVVB9',
        notifyMessage:
            'Tri ân khách hàng\nCám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\n\nMã khách hàng: <customer_code>\nMã voucher: <voucher_code>',
        oaName: 'Zalo Notification Service',
        id: '4857499677858351310',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_code',
            sampleValue: 'SALE50',
            maxLength: 30,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_discount',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'expire_date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_condition',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 3,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"voucher_condition":"voucher_condition","expire_date":"01/08/2020","voucher_code":"voucher_code","customer_name":"customer_name","customer_code":"customer_code","voucher_discount":"voucher_discount"}',
        name: 'Chương trình khuyến mãi ',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272027,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Theo dõi điểm',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo điểm tích luỹ',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cảm ơn quý khách <customer_name> đã sử dụng dịch vụ của chúng tôi. Quý khách được ghi nhận tích lũy điểm thành viên thành công với các thông tin sau\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_code>',
                      key: 'Mã khách hàng',
                    },
                    {
                      type: '0',
                      value: '<order_code>',
                      key: 'Đơn hàng',
                    },
                    {
                      type: '0',
                      value: '<order_date>',
                      key: 'Ngày mua hàng',
                    },
                    {
                      type: '0',
                      value: '<price>',
                      key: 'Giá trị đơn hàng ',
                    },
                    {
                      type: null,
                      value: '<point> ',
                      key: 'Điểm thưởng gia tăng',
                    },
                    {
                      type: '0',
                      value: '<total_point>',
                      key: 'Tổng điểm hiện tại',
                    },
                    {
                      type: '0',
                      value: '<note>',
                      key: 'Ghi chú',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '11',
              name: 'customer_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'order_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'order_date',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'price',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'point',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'total_point',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'note',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Thông báo điểm tích luỹ","subTitle":null,"paragraphs":["Cảm ơn quý khách <customer_name> đã sử dụng dịch vụ của chúng tôi. Quý khách được ghi nhận tích lũy điểm thành viên thành công với các thông tin sau\\n"],"rows":[{"key":"Mã khách hàng","value":"<customer_code>","type":"0"},{"key":"Đơn hàng","value":"<order_code>","type":"0"},{"key":"Ngày mua hàng","value":"<order_date>","type":"0"},{"key":"Giá trị đơn hàng ","value":"<price>","type":"0"},{"key":"Điểm thưởng gia tăng","value":"<point> ","type":null},{"key":"Tổng điểm hiện tại","value":"<total_point>","type":"0"},{"key":"Ghi chú","value":"<note>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Theo dõi điểm","data":"https://zalo.cloud/"}],"params":[{"name":"customer_code","znsType":"11","type":"0","limit":30},{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"order_code","znsType":"12","type":null,"limit":null},{"name":"order_date","znsType":"19","type":null,"limit":null},{"name":"price","znsType":"18","type":null,"limit":null},{"name":"point","znsType":"18","type":null,"limit":null},{"name":"total_point","znsType":"18","type":null,"limit":null},{"name":"note","znsType":"12","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689871461,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo điểm tích luỹ\nCảm ơn quý khách <customer_name> đã sử dụng dịch vụ của chúng tôi. Quý khách được ghi nhận tích lũy điểm thành viên thành công với các thông tin sau\n\nMã khách hàng: <customer_code>\nĐơn hàng: <order_code>',
        oaName: 'Zalo Notification Service',
        id: '3920479522991653395',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'order_date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'price',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'point',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'total_point',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'note',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 2,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"order_code":"order_code","order_date":"01/08/2020","note":"note","price":100,"customer_name":"customer_name","total_point":100,"customer_code":"customer_code","point":100}',
        name: 'Tích lũy điểm thưởng',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
    ],
  },
  {
    industryName: 'Sức khoẻ/y tế',
    industryId: 2,
    presetTemplate: [
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272028,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Xác nhận lịch khám',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cảm ơn quý khách đã đặt lịch khám tại phòng khám. Lịch khám của bạn đã được xác nhận với chi tiết như sau:\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_name>',
                      key: 'Họ và tên',
                    },
                    {
                      type: '0',
                      value: '<booking_code>',
                      key: 'Mã đặt lịch ',
                    },
                    {
                      type: '0',
                      value: '<schedule_time>',
                      key: 'Thời gian khám',
                    },
                    {
                      type: '0',
                      value: '<address>',
                      key: 'Địa chỉ cơ sở khám ',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '19',
              name: 'schedule_time',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Xác nhận lịch khám","subTitle":null,"paragraphs":["Cảm ơn quý khách đã đặt lịch khám tại phòng khám. Lịch khám của bạn đã được xác nhận với chi tiết như sau:\\n"],"rows":[{"key":"Họ và tên","value":"<customer_name>","type":"0"},{"key":"Mã đặt lịch ","value":"<booking_code>","type":"0"},{"key":"Thời gian khám","value":"<schedule_time>","type":"0"},{"key":"Địa chỉ cơ sở khám ","value":"<address>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ bộ phận CSKH","data":"********"}],"params":[{"name":"schedule_time","znsType":"19","type":"2","limit":10},{"name":"address","znsType":"5","type":"0","limit":30},{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689871616,
        appId: 'BAUVVB9',
        notifyMessage:
            'Xác nhận lịch khám\nCảm ơn quý khách đã đặt lịch khám tại phóng khám. Lịch khám của bạn đã được xác nhận với chi tiết như sau:\n\nHọ và tên: <customer_name>\nMã đặt lịch : <booking_code>',
        oaName: 'Zalo Notification Service',
        id: '1783037827799074411',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"01/08/2020","customer_name":"customer_name"}',
        name: 'Xác nhận đặt lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272031,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo lịch khám',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Quý khách <customer_name> có lịch hẹn khám vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>.\n',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\nHẹn gặp quý khách tại phòng khám!\n',
                },
              },
            ],
          },
          params: [
            {
              znsType: '19',
              name: 'schedule_time',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Thông báo lịch khám","paragraphs":["Quý khách <customer_name> có lịch hẹn khám vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>.\\n","Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\\nHẹn gặp quý khách tại phòng khám!\\n"],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ bộ phận CSKH","data":"********"}],"params":[{"name":"schedule_time","znsType":"19","type":"0","limit":30},{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689872032,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo lịch khám\nQuý khách <customer_name> có lịch hẹn khám vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>.\n',
        oaName: 'Zalo Notification Service',
        id: '1146617755403306457',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"01/08/2020","customer_name":"customer_name"}',
        name: 'Thông báo nhắc đến lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272193,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thay đổi lịch khám',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Phòng khám xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch khám mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \n',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\nHẹn gặp quý khách tại phòng khám!\n',
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'schedule_time',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Thay đổi lịch khám","paragraphs":["Phòng khám xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch khám mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \\n","Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\\nHẹn gặp quý khách tại phòng khám!\\n"],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ bộ phận CSKH","data":"********"}],"params":[{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null},{"name":"schedule_time","znsType":"1","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689927713,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thay đổi lịch khám\nPhòng khám xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch khám mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \n',
        oaName: 'Zalo Notification Service',
        id: '5883581370449741948',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"schedule_time","customer_name":"customer_name"}',
        name: 'Thông báo thay đổi lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 343874,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/2024/06/26/2429bdc7f2d170ce3eea2d5ead9534fb.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/169b0664b8de6b7ecb2c92bd9ab9d4e2.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Đánh giá đơn hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng và mua đơn hàng <order_code> của chúng tôi vào ngày <order_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
                },
              },
              {
                RATING: {
                  ratings: [
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 1,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Rất không hài lòng',
                    },
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 2,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Không hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 3,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Bình thường',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 4,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 5,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Đóng gói hàng hóa chắc chắn hơn',
                        'Chất lượng sản phẩm tốt hơn',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Kiểm tra đơn kỹ hơn khi đóng gói',
                        'Chuẩn bị hàng nhanh hơn',
                      ],
                      title: 'Rất hài lòng',
                    },
                  ],
                  header: null,
                  scale: null,
                  responseMessage: null,
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: 'Nguyễn Lê Minh Khoa',
              znsAliasParam: null,
            },
            {
              znsType: '11',
              name: 'order_code',
              limit: null,
              type: null,
              sampleValue: 'ABC123',
              znsAliasParam: null,
            },
            {
              znsType: '19',
              name: 'order_date',
              limit: null,
              type: null,
              sampleValue: '30/05/2024',
              znsAliasParam: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 4,
        amountQuota: '0',
        content: '',
        timeout: '7200',
        createdAt: 1719375590,
        appId: '4KU4PV7',
        notifyMessage:
            'Đánh giá đơn hàng\nCám ơn quý khách <customer_name> đã tin tưởng và mua đơn hàng <order_code> của chúng tôi vào ngày <order_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
        oaName: 'Zalo Notification Service',
        id: '5696783325193060599',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'order_code',
            sampleValue: 'ABC123',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'order_date',
            sampleValue: '30/05/2024',
            maxLength: 20,
          },
        ],
        productId: '4804105510298600518',
        src: 0,
        appName: 'ZNS Service',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 2,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"order_code":"order_code","order_date":"01/08/2020","$zReqId":"$zReqId","$zReqTime":"$zReqTime","customer_name":"customer_name"}',
        name: 'Chăm sóc, thu thập ý kiến của KH sau khi mua hàng',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 343881,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/2024/06/26/747f858e4bad02c1276f1701dbbfdbd2.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/169b0664b8de6b7ecb2c92bd9ab9d4e2.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Đánh giá dịch vụ',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng và sử dụng dịch vụ của phòng khám vào ngày <schedule_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
                },
              },
              {
                RATING: {
                  ratings: [
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 1,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Khâu thủ tục, đăng ký khám',
                        'Khâu thanh toán chi phí khám bệnh',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Chỗ ngồi chờ khám',
                        'Cách hỏi, khám bệnh của bác sĩ',
                      ],
                      title: 'Rất không hài lòng',
                    },
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 2,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Khâu thủ tục, đăng ký khám',
                        'Khâu thanh toán chi phí khám bệnh',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Chỗ ngồi chờ khám',
                        'Cách hỏi, khám bệnh của bác sĩ',
                      ],
                      title: 'Không hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 3,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu thủ tục, đăng ký khám',
                        'Khâu thanh toán chi phí khám bệnh',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Chỗ ngồi chờ khám',
                        'Cách hỏi, khám bệnh của bác sĩ',
                      ],
                      title: 'Bình thường',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 4,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu thủ tục, đăng ký khám',
                        'Khâu thanh toán chi phí khám bệnh',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Chỗ ngồi chờ khám',
                        'Cách hỏi, khám bệnh của bác sĩ',
                      ],
                      title: 'Hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 5,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu thủ tục, đăng ký khám',
                        'Khâu thanh toán chi phí khám bệnh',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Chỗ ngồi chờ khám',
                        'Cách hỏi, khám bệnh của bác sĩ',
                      ],
                      title: 'Rất hài lòng',
                    },
                  ],
                  header: null,
                  scale: null,
                  responseMessage: null,
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: 'Nguyễn Lê Minh Khoa',
              znsAliasParam: null,
            },
            {
              znsType: '19',
              name: 'schedule_date',
              limit: 20,
              type: '2',
              sampleValue: '30/05/2024',
              znsAliasParam: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 4,
        amountQuota: '0',
        content: '',
        timeout: '7200',
        createdAt: 1719375901,
        appId: '4KU4PV7',
        notifyMessage:
            'Đánh giá dịch vụ\nCám ơn quý khách <customer_name> đã tin tưởng và sử dụng dịch vụ của phòng khám vào ngày <schedule_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
        oaName: 'Zalo Notification Service',
        id: '7535524981228588561',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_date',
            sampleValue: '30/05/2024',
            maxLength: 20,
          },
        ],
        productId: '4804105510298600518',
        src: 0,
        appName: 'ZNS Service',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 2,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"$zReqId":"$zReqId","$zReqTime":"$zReqTime","customer_name":"customer_name","schedule_date":"01/08/2020"}',
        name: 'Chăm sóc, thu thập ý kiến của KH sau khi khám',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272033,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Chi tiết khuyến mãi',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Tri ân khách hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_code>',
                      key: 'Mã khách hàng',
                    },
                    {
                      type: '0',
                      value: '<voucher_code>',
                      key: 'Mã voucher',
                    },
                    {
                      type: '0',
                      value: '<voucher_discount>',
                      key: 'Ưu đãi',
                    },
                    {
                      type: '0',
                      value: '<expire_date>',
                      key: 'Hạn sử dụng',
                    },
                    {
                      type: '0',
                      value: '<voucher_condition>',
                      key: 'Điều kiện sử dụng',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'customer_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'voucher_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_discount',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'expire_date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_condition',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Tri ân khách hàng","subTitle":null,"paragraphs":["Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\\n"],"rows":[{"key":"Mã khách hàng","value":"<customer_code>","type":"0"},{"key":"Mã voucher","value":"<voucher_code>","type":"0"},{"key":"Ưu đãi","value":"<voucher_discount>","type":"0"},{"key":"Hạn sử dụng","value":"<expire_date>","type":"0"},{"key":"Điều kiện sử dụng","value":"<voucher_condition>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Chi tiết khuyến mãi","data":"https://zalo.cloud/"}],"params":[{"name":"customer_name","znsType":"1","type":"0","limit":30},{"name":"customer_code","znsType":"11","type":"0","limit":30},{"name":"voucher_code","znsType":"11","type":"0","limit":30},{"name":"voucher_discount","znsType":"12","type":"0","limit":30},{"name":"expire_date","znsType":"19","type":"2","limit":10},{"name":"voucher_condition","znsType":"12","type":"0","limit":30}]}',
        timeout: '7200',
        createdAt: 1689872190,
        appId: 'BAUVVB9',
        notifyMessage:
            'Tri ân khách hàng\nCám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\n\nMã khách hàng: <customer_code>\nMã voucher: <voucher_code>',
        oaName: 'Zalo Notification Service',
        id: '8822076870551945489',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_code',
            sampleValue: 'SALE50',
            maxLength: 30,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_discount',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'expire_date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_condition',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 3,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"voucher_condition":"voucher_condition","expire_date":"01/08/2020","voucher_code":"voucher_code","customer_name":"customer_name","customer_code":"customer_code","voucher_discount":"voucher_discount"}',
        name: 'Chương trình khuyến mãi',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
    ],
  },
  {
    industryName: 'Giáo dục',
    industryId: 3,
    presetTemplate: [
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272039,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Hotline hỗ trợ',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo học phí',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Trung tâm xin thông báo học viên <student_name> - Mã học viên <student_code> kết thúc khóa học vào ngày <course_end_date>. Vui lòng thanh toán học phí khóa tiếp theo theo thông tin sau:',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<acc_number>',
                      key: 'Số tài khoản',
                    },
                    {
                      type: '0',
                      value: '<bank_name>',
                      key: 'Ngân hàng',
                    },
                    {
                      type: '0',
                      value: '<company_name>',
                      key: 'Tên tài khoản',
                    },
                    {
                      type: null,
                      value: '[Mã học viên] và [Số điện thoại của phụ huynh]',
                      key: 'Nội dung chuyển khoản',
                    },
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Học phí cần được thanh toán trước',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'student_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '4', //11
              name: 'student_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'course_end_date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'acc_number',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '5', //17
              name: 'bank_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'company_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Thông báo học phí","subTitle":null,"paragraphs":["Trung tâm xin thông báo học viên <student_name> - Mã học viên <student_code> kết thúc khóa học vào ngày <course_end_date>. Vui lòng thanh toán học phí khóa tiếp theo theo thông tin sau:"],"rows":[{"key":"Số tài khoản","value":"<acc_number>","type":"0"},{"key":"Ngân hàng","value":"<bank_name>","type":"0"},{"key":"Tên tài khoản","value":"<company_name>","type":"0"},{"key":"Nội dung chuyển khoản","value":"[Mã học viên] và [Số điện thoại của phụ huynh]","type":null},{"key":"Học phí cần được thanh toán trước","value":"<date>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Hotline hỗ trợ","data":"********"}],"params":[{"name":"student_name","znsType":"1","type":"0","limit":30},{"name":"student_code","znsType":"11","type":"0","limit":30},{"name":"course_end_date","znsType":"19","type":"2","limit":10},{"name":"acc_number","znsType":"11","type":"0","limit":30},{"name":"bank_name","znsType":"17","type":"0","limit":30},{"name":"company_name","znsType":"17","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10}]}',
        timeout: '7200',
        createdAt: **********,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo học phí\nTrung tâm xin thông báo học viên <student_name> - Mã học viên <student_code> kết thúc khóa học vào ngày <course_end_date>. Vui lòng thanh toán học phí khóa tiếp theo theo thông tin sau:\nSố tài khoản: <acc_number>\nNgân hàng: <bank_name>',
        oaName: 'Zalo Notification Service',
        id: '3884607221929363391',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'course_end_date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'acc_number',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
             znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'bank_name',
            sampleValue: 'Bàn phím Razer',
            maxLength: 200,
          },
          {
             znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'company_name',
            sampleValue: 'Bàn phím Razer',
            maxLength: 200,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"acc_number":"acc_number","date":"01/08/2020","student_name":"student_name","company_name":"company_name","bank_name":"bank_name","course_end_date":"01/08/2020","student_code":"student_code"}',
        name: 'Thông báo học phí',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272063,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Đăng ký học thành công',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Xin chào học viên <student_name>, cảm ơn bạn đã đăng ký khoá học với chúng tôi và chọn chúng tôi nơi đồng hành. Trung tâm xin gửi đến bạn thông tin đăng ký:',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Ngày đăng ký',
                    },
                    {
                      type: '0',
                      value: '<student_id>',
                      key: 'Mã học viên',
                    },
                    {
                      type: '0',
                      value: '<phone>',
                      key: 'Điện thoại',
                    },
                    {
                      type: '0',
                      value: '<course>',
                      key: 'Tên khoá học',
                    },
                    {
                      type: '0',
                      value: '<total_course_fee>',
                      key: 'Tổng học phí',
                    },
                    {
                      type: '0',
                      value: '<paid_fee>',
                      key: 'Học phí đã đóng',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'student_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'student_id',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '15',
              name: 'phone',
              limit: 11,
              type: '1',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'course',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'total_course_fee',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '18',
              name: 'paid_fee',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Đăng ký học thành công","subTitle":null,"paragraphs":["Xin chào học viên <student_name>, cảm ơn bạn đã đăng ký khoá học với chúng tôi và chọn chúng tôi nơi đồng hành. Trung tâm xin gửi đến bạn thông tin đăng ký:"],"rows":[{"key":"Ngày đăng ký","value":"<date>","type":"0"},{"key":"Mã học viên","value":"<student_id>","type":"0"},{"key":"Điện thoại","value":"<phone>","type":"0"},{"key":"Tên khoá học","value":"<course>","type":"0"},{"key":"Tổng học phí","value":"<total_course_fee>","type":"0"},{"key":"Học phí đã đóng","value":"<paid_fee>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ CSKH","data":"********"}],"params":[{"name":"student_name","znsType":"1","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10},{"name":"student_id","znsType":"11","type":"0","limit":30},{"name":"phone","znsType":"15","type":"1","limit":11},{"name":"course","znsType":"12","type":"0","limit":30},{"name":"total_course_fee","znsType":"18","type":"0","limit":30},{"name":"paid_fee","znsType":"18","type":"0","limit":30}]}',
        timeout: '7200',
        createdAt: 1689910190,
        appId: 'BAUVVB9',
        notifyMessage:
            'Đăng ký học thành công\nXin chào học viên <student_name>, cảm ơn bạn đã đăng ký khoá học với chúng tôi và chọn chúng tôi nơi đồng hành. Trung tâm xin gửi đến bạn thông tin đăng ký:\nNgày đăng ký: <date>\nMã học viên: <student_id>',
        oaName: 'Zalo Notification Service',
        id: '1539057090966930744',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_id',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 15,
            zteType: 'STRING',
            dataType: 'string',
            name: 'phone',
            sampleValue: '0969874535',
            maxLength: 50,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'course',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'total_course_fee',
            sampleValue: '32',
            maxLength: 20,
          },
          {
            znsType: 18,
            zteType: 'NUMBER',
            dataType: 'number',
            name: 'paid_fee',
            sampleValue: '32',
            maxLength: 20,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"date":"01/08/2020","student_name":"student_name","total_course_fee":100,"phone":"phone","student_id":"student_id","course":"course","paid_fee":100}',
        name: 'Xác nhận đóng học phí',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272066,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Thông tin lớp học',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Lịch học bù',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Trung tâm xin thông báo học viên <student_name> có lịch học bù/phụ đạo vào lúc <time> tại địa chỉ <address>. Học viên vui lòng tham gia đúng giờ và hoàn thành bài tập đầy đủ.',
                },
              },
            ],
          },
          params: [
            {
              znsType: '19',
              name: 'time',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'student_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Lịch học bù","paragraphs":["Trung tâm xin thông báo học viên <student_name> có lịch học bù/phụ đạo vào lúc <time> tại địa chỉ <address>. Học viên vui lòng tham gia đúng giờ và hoàn thành bài tập đầy đủ."],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Thông tin lớp học","data":"https://zalo.cloud/"}],"params":[{"name":"time","znsType":"19","type":"2","limit":10},{"name":"address","znsType":"5","type":"0","limit":30},{"name":"student_name","znsType":"1","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689910286,
        appId: 'BAUVVB9',
        notifyMessage:
            'Lịch học bù\nTrung tâm xin thông báo học viên <student_name> có lịch học bù/phụ đạo vào lúc <time> tại địa chỉ <address>. Học viên vui lòng tham gia đúng giờ và hoàn thành bài tập đầy đủ.',
        oaName: 'Zalo Notification Service',
        id: '6840071416248878517',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"student_name":"student_name","address":"address","time":"01/08/2020"}',
        name: 'Thông báo học bù',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272070,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Thông tin lớp học',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Lịch học chính thức',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Xin chào học viên <student_name>, cám ơn bạn đã tin tưởng và đăng ký khoá học với chúng tôi. Trung tâm xin gửi đến bạn lịch học chính thức với thông tin như sau:',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<student_id>',
                      key: 'Mã học viên',
                    },
                    {
                      type: '0',
                      value: '<course>',
                      key: 'Tên khóa học ',
                    },
                    {
                      type: '0',
                      value: '<address>',
                      key: 'Địa điểm học',
                    },
                    {
                      type: '0',
                      value: '<time>',
                      key: 'Thời gian học',
                    },
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Ngày bắt đầu học',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'student_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'time',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'course',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'student_id',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '1583',
        content:
            '{"logo":null,"title":"Lịch học chính thức","subTitle":null,"paragraphs":["Xin chào học viên <student_name>, cám ơn bạn đã tin tưởng và đăng ký khoá học với chúng tôi. Trung tâm xin gửi đến bạn lịch học chính thức với thông tin như sau:"],"rows":[{"key":"Mã học viên","value":"<student_id>","type":"0"},{"key":"Tên khóa học ","value":"<course>","type":"0"},{"key":"Địa điểm học","value":"<address>","type":"0"},{"key":"Thời gian học","value":"<time>","type":"0"},{"key":"Ngày bắt đầu học","value":"<date>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Thông tin lớp học","data":"https://zalo.cloud/"}],"params":[{"name":"student_name","znsType":"1","type":"0","limit":30},{"name":"date","znsType":"19","type":null,"limit":null},{"name":"time","znsType":"19","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null},{"name":"course","znsType":"12","type":null,"limit":null},{"name":"student_id","znsType":"11","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689911736,
        appId: 'BAUVVB9',
        notifyMessage:
            'Lịch học chính thức\nXin chào học viên <student_name>, cám ơn bạn đã tin tưởng và đăng ký khoá học với chúng tôi. Trung tâm xin gửi đến bạn lịch học chính thức với thông tin như sau:\nMã học viên: <student_id>\nTên khóa học : <course>',
        oaName: 'Zalo Notification Service',
        id: '2480583141069374185',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'course',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_id',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"date":"01/08/2020","student_name":"student_name","address":"address","course":"course","student_id":"student_id","time":"01/08/2020"}',
        name: 'Thông tin lớp học',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272071,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo nghỉ học',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Xin chào học viên <student_name>, chúng tôi xin thông báo lớp học <course> có lịch nghỉ với thông tin như sau:\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<student_id>',
                      key: 'Mã học viên',
                    },
                    {
                      type: '0',
                      value: '<date>',
                      key: 'Nghỉ học ngày',
                    },
                    {
                      type: '0',
                      value: '<time>',
                      key: 'Giờ học',
                    },
                    {
                      type: '0',
                      value: '<address>',
                      key: 'Cơ sở học',
                    },
                    {
                      type: '0',
                      value: '<note>',
                      key: 'Lý do',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'student_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'time',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'note',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'course',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'student_id',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Thông báo nghỉ học","subTitle":null,"paragraphs":["Xin chào học viên <student_name>, chúng tôi xin thông báo lớp học <course> có lịch nghỉ với thông tin như sau:\\n"],"rows":[{"key":"Mã học viên","value":"<student_id>","type":"0"},{"key":"Nghỉ học ngày","value":"<date>","type":"0"},{"key":"Giờ học","value":"<time>","type":"0"},{"key":"Cơ sở học","value":"<address>","type":"0"},{"key":"Lý do","value":"<note>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ CSKH","data":"********"}],"params":[{"name":"student_name","znsType":"1","type":"0","limit":30},{"name":"date","znsType":"19","type":"2","limit":10},{"name":"time","znsType":"19","type":"0","limit":30},{"name":"address","znsType":"5","type":"0","limit":30},{"name":"note","znsType":"12","type":"0","limit":30},{"name":"course","znsType":"12","type":null,"limit":null},{"name":"student_id","znsType":"11","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689911838,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo nghỉ học\nXin chào học viên <student_name>, chúng tôi xin thông báo lớp học <course> có lịch nghỉ với thông tin như sau:\n\nMã học viên: <student_id>\nNghỉ học ngày: <date>',
        oaName: 'Zalo Notification Service',
        id: '2427239624240876580',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'note',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'course',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'STRING',
            dataType: 'string',
            name: 'student_id',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"date":"01/08/2020","student_name":"student_name","note":"note","address":"address","course":"course","student_id":"student_id","time":"01/08/2020"}',
        name: 'Thông báo tạm nghỉ',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
    ],
  },
  {
    industryName: 'Dịch vụ làm đẹp',
    industryId: 4,
    presetTemplate: [
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272034,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Xác nhận lịch hẹn',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cảm ơn quý khách đã đặt lịch sử dụng dịch vụ của chúng tôi. Lịch hẹn của bạn đã được xác nhận với chi tiết như sau:\n',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_name>',
                      key: 'Họ và tên',
                    },
                    {
                      type: null,
                      value: ' <booking_code>',
                      key: 'Mã đặt lịch',
                    },
                    {
                      type: null,
                      value: '<schedule_time> ',
                      key: 'Ngày đặt ',
                    },
                    {
                      type: null,
                      value: '<address> ',
                      key: 'Địa chỉ cơ sở ',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'schedule_time',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Xác nhận lịch hẹn","subTitle":null,"paragraphs":["Cảm ơn quý khách đã đặt lịch sử dụng dịch vụ của chúng tôi. Lịch hẹn của bạn đã được xác nhận với chi tiết như sau:\\n"],"rows":[{"key":"Họ và tên","value":"<customer_name>","type":"0"},{"key":"Mã đặt lịch","value":" <booking_code>","type":null},{"key":"Ngày đặt ","value":"<schedule_time> ","type":null},{"key":"Địa chỉ cơ sở ","value":"<address> ","type":null}],"postRowParagraphs":[],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ CSKH","data":"********"}],"params":[{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null},{"name":"schedule_time","znsType":"19","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689872304,
        appId: 'BAUVVB9',
        notifyMessage:
            'Xác nhận lịch hẹn\nCảm ơn quý khách đã đặt lịch sử dụng dịch vụ của chúng tôi. Lịch hẹn của bạn đã được xác nhận với chi tiết như sau:\n\nHọ và tên: <customer_name>\nMã đặt lịch:  <booking_code>',
        oaName: 'Zalo Notification Service',
        id: '4465548247969058609',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"01/08/2020","customer_name":"customer_name"}',
        name: 'Xác nhận đặt lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272035,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thông báo lịch hẹn',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Quý khách <customer_name> có lịch hẹn vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>\n',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\nHẹn gặp quý khách tại cơ sở của chúng tôi!\n',
                },
              },
            ],
          },
          params: [
            {
              znsType: '19',
              name: 'schedule_time',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Thông báo lịch hẹn","paragraphs":["Quý khách <customer_name> có lịch hẹn vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>\\n","Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\\nHẹn gặp quý khách tại cơ sở của chúng tôi!\\n"],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ CSKH","data":"********"}],"params":[{"name":"schedule_time","znsType":"19","type":"0","limit":30},{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689872470,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thông báo lịch hẹn\nQuý khách <customer_name> có lịch hẹn vào lúc <schedule_time> tại <address> với mã đặt lịch <booking_code>\n',
        oaName: 'Zalo Notification Service',
        id: '1285929674808444970',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"01/08/2020","customer_name":"customer_name"}',
        name: 'Thông báo nhắc đến lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272195,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Thay đổi lịch hẹn',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Chúng tôi xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch hẹn mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \n',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\nHẹn gặp quý khách!\n',
                },
              },
            ],
          },
          params: [
            {
              znsType: '19',
              name: 'schedule_time',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '5',
              name: 'address',
              limit: null,
              type: null,
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'booking_code',
              limit: null,
              type: null,
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"imageSlider":null,"logo":{"light":"https://stc-oa.zdn.vn/uploads/8b1aff7a1d1c6cec480b077e015648f8.png","dark":"https://stc-oa.zdn.vn/uploads/20ad808338d4b2a3654fc104ed65fad7.png"},"title":"Thay đổi lịch hẹn","paragraphs":["Chúng tôi xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch hẹn mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \\n","Vui lòng đến đúng giờ hẹn để được phục vụ tốt nhất.\\nHẹn gặp quý khách!\\n"],"buttons":[{"buttonId":"2742805079633053054","tag":1,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"1","text":"Liên hệ CSKH","data":"********"}],"params":[{"name":"schedule_time","znsType":"19","type":"0","limit":30},{"name":"customer_name","znsType":"1","type":null,"limit":null},{"name":"address","znsType":"5","type":null,"limit":null},{"name":"booking_code","znsType":"12","type":null,"limit":null}]}',
        timeout: '7200',
        createdAt: 1689927860,
        appId: 'BAUVVB9',
        notifyMessage:
            'Thay đổi lịch hẹn\nChúng tôi xin thông báo lịch hẹn của quý khách <customer_name> với mã đặt lịch <booking_code> đã có thay đổi. Lịch hẹn mới của quý khách sẽ diễn ra vào lúc <schedule_time> tại <address>. \n',
        oaName: 'Zalo Notification Service',
        id: '524914853835654556',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_time',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 5,
            zteType: 'STRING',
            dataType: 'string',
            name: 'address',
            sampleValue: '1004 Tạ Quang Bửu, p6, q8, TPHCM',
            maxLength: 200,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'booking_code',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 1,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"booking_code":"booking_code","address":"address","schedule_time":"01/08/2020","customer_name":"customer_name"}',
        name: 'Thông báo thay đổi lịch hẹn',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 343887,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '1',
                      data: '********',
                      price: null,
                      isPrimary: null,
                      buttonId: '2742805079633053054',
                      tag: 1,
                      isSecondary: null,
                      text: 'Liên hệ bộ phận CSKH',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [
              {
                LOGO: {
                  urlDark:
                      'https://stc-oa.zdn.vn/uploads/2024/06/26/488b12d4d5bda5f5a3c8999d2db189ae.png',
                  urlLight:
                      'https://stc-oa.zdn.vn/uploads/169b0664b8de6b7ecb2c92bd9ab9d4e2.png',
                },
              },
            ],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Đánh giá sản phẩm',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng và sử dụng dịch vụ của chúng tôi vào ngày <schedule_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
                },
              },
              {
                RATING: {
                  ratings: [
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 1,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Khâu đăng ký lich hẹn',
                        'Khâu thanh toán chi phí',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Không gian spa',
                        'Thao tác nhân viên',
                      ],
                      title: 'Rất không hài lòng',
                    },
                    {
                      thankDescription:
                          'Rất xin lỗi bạn vì trải nghiệm chưa tốt vừa qua. Chúng tôi sẽ nghiêm túc xem xét để thay đổi và phục vụ bạn tốt hơn nữa.',
                      star: 2,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn đã góp ý!',
                      answers: [
                        'Khâu đăng ký lich hẹn',
                        'Khâu thanh toán chi phí',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Không gian spa',
                        'Thao tác nhân viên',
                      ],
                      title: 'Không hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 3,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu đăng ký lich hẹn',
                        'Khâu thanh toán chi phí',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Không gian spa',
                        'Thao tác nhân viên',
                      ],
                      title: 'Bình thường',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 4,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu đăng ký lich hẹn',
                        'Khâu thanh toán chi phí',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Không gian spa',
                        'Thao tác nhân viên',
                      ],
                      title: 'Hài lòng',
                    },
                    {
                      thankDescription:
                          'Mọi góp ý của bạn đều rất giá trị. Chúng tôi sẽ tiếp tục nỗ lực để phục vụ bạn tốt hơn nữa.',
                      star: 5,
                      question: 'Chúng tôi có thể cải thiện điều gì?',
                      thankTitle: 'Cảm ơn bạn!',
                      answers: [
                        'Khâu đăng ký lich hẹn',
                        'Khâu thanh toán chi phí',
                        'Nhân viên chăm sóc nhiệt tình hơn',
                        'Không gian spa',
                        'Thao tác nhân viên',
                      ],
                      title: 'Rất hài lòng',
                    },
                  ],
                  header: null,
                  scale: null,
                  responseMessage: null,
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: null,
              type: null,
              sampleValue: 'Nguyễn Lê Minh Khoa',
              znsAliasParam: null,
            },
            {
              znsType: '19',
              name: 'schedule_date',
              limit: 20,
              type: '2',
              sampleValue: '30/05/2024',
              znsAliasParam: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 4,
        amountQuota: '0',
        content: '',
        timeout: '7200',
        createdAt: 1719376191,
        appId: '4KU4PV7',
        notifyMessage:
            'Đánh giá sản phẩm\nCám ơn quý khách <customer_name> đã tin tưởng và sử dụng dịch vụ của chúng tôi vào ngày <schedule_date>. Quý khách vui lòng đánh giá độ hài lòng sau khi mua hàng để chúng tôi có thể cải thiện chất lượng dịch vụ.',
        oaName: 'Zalo Notification Service',
        id: '7450235435461264733',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'schedule_date',
            sampleValue: '30/05/2024',
            maxLength: 20,
          },
        ],
        productId: '4804105510298600518',
        src: 0,
        appName: 'ZNS Service',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 2,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"$zReqId":"$zReqId","$zReqTime":"$zReqTime","customer_name":"customer_name","schedule_date":"01/08/2020"}',
        name: 'Chăm sóc, thu thập ý kiến của KH sau khi trải nghiệm dịch vụ',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
      {
        conditionActive: '',
        note: '',
        brandTemplateId: 272038,
        section: {
          footer: {
            components: [
              {
                BUTTON: {
                  buttons: [
                    {
                      actionType: '2',
                      data: 'https://zalo.cloud/',
                      price: null,
                      isPrimary: null,
                      buttonId: '7163931297476724607',
                      tag: 0,
                      isSecondary: null,
                      text: 'Chi tiết khuyến mãi',
                    },
                  ],
                },
              },
            ],
          },
          header: {
            components: [],
          },
          type: 'NORMAL',
          body: {
            components: [
              {
                TITLE: {
                  value: 'Tri ân khách hàng',
                },
              },
              {
                PARAGRAPH: {
                  value:
                      'Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.',
                },
              },
              {
                TABLE: {
                  rows: [
                    {
                      type: '0',
                      value: '<customer_code>',
                      key: 'Mã khách hàng',
                    },
                    {
                      type: '0',
                      value: '<voucher_code>',
                      key: 'Mã voucher',
                    },
                    {
                      type: '0',
                      value: '<voucher_discount>',
                      key: 'Ưu đãi',
                    },
                    {
                      type: '0',
                      value: '<expire_date>',
                      key: 'Hạn sử dụng',
                    },
                    {
                      type: '0',
                      value: '<voucher_condition>',
                      key: 'Điều kiện sử dụng',
                    },
                  ],
                },
              },
            ],
          },
          params: [
            {
              znsType: '1',
              name: 'customer_name',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'customer_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '11',
              name: 'voucher_code',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_discount',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
            {
              znsType: '19',
              name: 'expire_date',
              limit: 10,
              type: '2',
              sampleValue: null,
            },
            {
              znsType: '12',
              name: 'voucher_condition',
              limit: 30,
              type: '0',
              sampleValue: null,
            },
          ],
        },
        requiredOptIn: false,
        type: 7,
        amountQuota: '0',
        content:
            '{"logo":null,"title":"Tri ân khách hàng","subTitle":null,"paragraphs":["Cám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi."],"rows":[{"key":"Mã khách hàng","value":"<customer_code>","type":"0"},{"key":"Mã voucher","value":"<voucher_code>","type":"0"},{"key":"Ưu đãi","value":"<voucher_discount>","type":"0"},{"key":"Hạn sử dụng","value":"<expire_date>","type":"0"},{"key":"Điều kiện sử dụng","value":"<voucher_condition>","type":"0"}],"postRowParagraphs":[],"buttons":[{"buttonId":"7163931297476724607","tag":0,"price":null,"isPrimary":null,"isSecondary":null,"actionType":"2","text":"Chi tiết khuyến mãi","data":"https://zalo.cloud/"}],"params":[{"name":"customer_name","znsType":"1","type":"0","limit":30},{"name":"customer_code","znsType":"11","type":"0","limit":30},{"name":"voucher_code","znsType":"11","type":"0","limit":30},{"name":"voucher_discount","znsType":"12","type":"0","limit":30},{"name":"expire_date","znsType":"19","type":"2","limit":10},{"name":"voucher_condition","znsType":"12","type":"0","limit":30}]}',
        timeout: '7200',
        createdAt: 1689873086,
        appId: 'BAUVVB9',
        notifyMessage:
            'Tri ân khách hàng\nCám ơn quý khách <customer_name> đã tin tưởng theo dõi và đồng hành cùng chúng tôi.\nMã khách hàng: <customer_code>\nMã voucher: <voucher_code>',
        oaName: 'Zalo Notification Service',
        id: '5367484843375215147',
        unitPrice: '300',
        technicalParams: [
          {
            znsType: 1,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_name',
            sampleValue: 'Nguyễn Lê Minh Khoa',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'customer_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 4,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_code',
            sampleValue: 'TP-34512',
            maxLength: 30,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_discount',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
          {
            znsType: 11,
            zteType: 'DATE',
            dataType: 'date',
            name: 'expire_date',
            sampleValue: '13:00:00 14/02/2021',
            maxLength: 20,
          },
          {
            znsType: 12,
            zteType: 'STRING',
            dataType: 'string',
            name: 'voucher_condition',
            sampleValue: 'mẫu nội dung tuỳ chỉnh',
            maxLength: 30,
          },
        ],
        productId: '0',
        src: 0,
        appName: 'Test ZNS',
        znsSendReason: 'sử dụng dịch vụ',
        disableReasons: null,
        reasonTag: 3,
        journeyTemplate: false,
        rejectedReason: [],
        sampleCode:
            '{"voucher_condition":"voucher_condition","expire_date":"01/08/2020","voucher_code":"voucher_code","customer_name":"customer_name","customer_code":"customer_code","voucher_discount":"voucher_discount"}',
        name: 'Chương trình khuyến mãi',
        oaAvatar:
            'https://s160-ava-talk.zadn.vn/b/4/9/0/3/160/28b2b4341a1b9c8c125b7834536c49c5.jpg',
        oaId: 'Z4UANMWGBR',
        oaIdLong: '1088627745287650843',
        status: 4,
      },
    ],
  },
];
