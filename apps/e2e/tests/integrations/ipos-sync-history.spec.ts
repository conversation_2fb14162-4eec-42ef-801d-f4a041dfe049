import { Page, expect, test } from '@playwright/test';
import { IPOSPageObject } from './ipos.po';

test.describe('IPOS Integration Sync History', () => {
  let page: Page;
  let po: IPOSPageObject;
  let slug: string;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    po = new IPOSPageObject(page);
    const result = await po.setup();
    slug = result.slug;
  });

  test.beforeEach(async () => {
    // Navigate to the IPOS sync history page before each test
    await po.goToSyncHistory(slug);
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("iPOS Sync History")', { timeout: 10000 });
  });

  test('should display the sync history page with filters', async () => {
    // Check if the page title is visible
    await expect(page.locator('h1:has-text("iPOS Sync History")')).toBeVisible();

    // Check if the resource filter is visible
    const resourceFilter = page.getByLabel('Resource Type');
    await expect(resourceFilter).toBeVisible();

    // Check if the status filter is visible
    const statusFilter = page.getByLabel('Status');
    await expect(statusFilter).toBeVisible();

    // Check if the search input is visible
    const searchInput = page.getByPlaceholder('Search by ID or description');
    await expect(searchInput).toBeVisible();
  });

  test('should filter sync history by resource type', async () => {
    // Filter sync history by resource type
    const result = await po.filterSyncHistory('products');

    // If filtering was successful, we should see the URL updated
    if (result) {
      await expect(page).toHaveURL(/resource=products/);
    }

    // Reset the filter
    await po.filterSyncHistory();

    // Check if the URL has been updated without the filter parameter
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync-history(?!\?resource=)/);
  });

  test('should filter sync history by status', async () => {
    // Filter sync history by status
    const result = await po.filterSyncHistory(undefined, 'Success');

    // If filtering was successful, we should see the URL updated
    if (result) {
      await expect(page).toHaveURL(/status=success/);
    }

    // Reset the filter
    await po.filterSyncHistory();

    // Check if the URL has been updated without the filter parameter
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync-history(?!\?status=)/);
  });

  test('should search sync history', async () => {
    // Search for a specific term
    const result = await po.searchSyncHistory('test');

    // If search was successful, we should see the URL updated
    if (result) {
      await expect(page).toHaveURL(/search=test/);
    }

    // Clear the search
    await po.searchSyncHistory('');

    // Check if the URL has been updated without the search parameter
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync-history(?!\?search=)/);
  });

  test('should navigate to sync log details when a log is clicked', async () => {
    // Check if there are any sync logs
    const syncLogs = page.locator('table tbody tr');
    const count = await syncLogs.count();

    // If there are sync logs, click on the first one
    if (count > 0) {
      await syncLogs.first().click();

      // Check if we've navigated to the sync log details page
      await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync-history\/[a-zA-Z0-9-]+/);

      // Check if the details page has loaded
      await expect(page.getByText('Sync Log Details')).toBeVisible({ timeout: 5000 });
    } else {
      console.log('No sync logs found, skipping test');
    }
  });

  test('should navigate to sync page when start new sync button is clicked', async () => {
    // Click the Start New Sync button
    const startNewSyncButton = page.getByRole('button', { name: 'Start New Sync' });
    await expect(startNewSyncButton).toBeVisible();
    await startNewSyncButton.click();

    // Check if we've navigated to the sync page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/sync/);
  });

  test('should navigate back to dashboard when back button is clicked', async () => {
    // Click the Back to Dashboard button
    const backButton = page.getByRole('button', { name: 'Back to Dashboard' });
    await expect(backButton).toBeVisible();
    await backButton.click();

    // Check if we've navigated to the dashboard page
    await expect(page).toHaveURL(/\/home\/<USER>\/integrations\/ipos\/dashboard/);
  });

  test('should display empty state when no sync logs are found', async () => {
    // Enter a search term that is unlikely to match any logs
    const result = await po.searchSyncHistory('nonexistentlog123456789');

    // If search was successful, we should see the empty state
    if (result) {
      // Check if the empty state message is displayed
      await expect(page.getByText('No sync logs found')).toBeVisible({ timeout: 5000 });
    }
  });
});
