import { Page } from '@playwright/test';

import { TeamAccountsPageObject } from '../team-accounts/team-accounts.po';

export class IPOSPageObject {
  public page: Page;
  public teamAccounts: TeamAccountsPageObject;

  constructor(page: Page) {
    this.page = page;
    this.teamAccounts = new TeamAccountsPageObject(page);
  }

  async setup() {
    const { email, slug } = await this.teamAccounts.setup();
    await this.goToIPOSIntegration(slug);
    return { email, slug };
  }

  async goToIPOSIntegration(slug: string) {
    try {
      // Navigate to the integrations page
      await this.page.goto(`/home/<USER>/integrations`);
      await this.page.waitForURL(`**/home/<USER>/integrations`, {
        timeout: 10000,
      });

      // Find and click on the IPOS integration card
      const iposCard = this.page.getByTestId('integration-card-ipos');
      await iposCard.waitFor({ state: 'visible', timeout: 10000 });

      // Check if the integration is already connected
      const connectButton = iposCard.getByRole('button', { name: 'Connect' });
      const configureButton = iposCard.getByRole('button', { name: 'Configure' });

      if (await connectButton.isVisible()) {
        // If not connected, click connect and set up the integration
        await connectButton.click();
        await this.page.waitForURL(`**/home/<USER>/integrations/ipos/connect`);
        await this.connectIPOS();
      } else if (await configureButton.isVisible()) {
        // If already connected, click configure
        await configureButton.click();
        await this.page.waitForURL(`**/home/<USER>/integrations/ipos/dashboard`);
      }
    } catch (error) {
      console.error(`Failed to navigate to IPOS integration: ${error}`);
      throw error;
    }
  }

  async connectIPOS() {
    try {
      // Wait for the connect form to be visible
      await this.page.getByTestId('ipos-connect-form').waitFor({ state: 'visible', timeout: 10000 });

      // Fill in the form with default test credentials
      await this.page.getByTestId('ipos-access-token-input').fill('JHTHWPCE6OCZBW0PBH9XRRBC6JTR1UWQ');
      await this.page.getByTestId('ipos-pos-parent-input').fill('SAOBANG');
      await this.page.getByTestId('ipos-pos-id-input').fill('3160');
      await this.page.getByTestId('ipos-base-url-input').fill('https://api.foodbook.vn');

      // Test the connection first
      await this.page.getByTestId('ipos-test-connection-button').click();

      // Wait for the test to complete (either success or error)
      await this.page.waitForTimeout(5000);

      // Connect regardless of test result
      await this.page.getByTestId('ipos-connect-button').click();

      // Wait for redirect after connection
      await this.page.waitForURL('**/integrations/ipos/dashboard', { timeout: 15000 });
    } catch (error) {
      console.error(`Failed to connect IPOS: ${error}`);
      throw error;
    }
  }

  async goToDashboard(slug: string) {
    await this.page.goto(`/home/<USER>/integrations/ipos/dashboard`);
    await this.page.waitForURL(`**/home/<USER>/integrations/ipos/dashboard`, {
      timeout: 10000,
    });
  }

  async goToMapping(slug: string, resourceType?: 'products' | 'orders' | 'customers') {
    let url = `/home/<USER>/integrations/ipos/mapping`;
    if (resourceType) {
      url += `?resource=${resourceType}`;
    }
    await this.page.goto(url);
    await this.page.waitForURL(`**/home/<USER>/integrations/ipos/mapping`, {
      timeout: 10000,
    });
  }

  async goToSync(slug: string) {
    await this.page.goto(`/home/<USER>/integrations/ipos/sync`);
    await this.page.waitForURL(`**/home/<USER>/integrations/ipos/sync`, {
      timeout: 10000,
    });
  }

  async goToSyncHistory(slug: string) {
    await this.page.goto(`/home/<USER>/integrations/ipos/sync-history`);
    await this.page.waitForURL(`**/home/<USER>/integrations/ipos/sync-history`, {
      timeout: 10000,
    });
  }

  async testConnection() {
    try {
      // Find and click the Test button
      const testButton = this.page.getByRole('button', { name: 'Test' });
      
      // Check if the button is visible and not disabled
      if (await testButton.isEnabled()) {
        await testButton.click();
        
        // Wait for the toast notification
        await this.page.waitForTimeout(5000);
        return true;
      } else {
        console.log('Test button is disabled, skipping test');
        return false;
      }
    } catch (error) {
      console.error(`Failed to test connection: ${error}`);
      throw error;
    }
  }

  async configureMapping(resourceType: 'products' | 'orders' | 'customers') {
    try {
      // Select the resource type
      await this.page.getByTestId('resource-type-select').click();
      await this.page.getByRole('option', { name: resourceType.charAt(0).toUpperCase() + resourceType.slice(1) }).click();
      
      // Wait for the fields to load
      await this.page.waitForTimeout(2000);
      
      // Map some fields if available
      const sourceFields = this.page.locator('[data-testid="source-field-item"]');
      const targetFields = this.page.locator('[data-testid="target-field-item"]');
      
      if (await sourceFields.count() > 0 && await targetFields.count() > 0) {
        // Map the first source field to the first target field
        await sourceFields.first().click();
        await targetFields.first().click();
        
        // Wait for the mapping to be created
        await this.page.waitForTimeout(1000);
        
        // Save the mappings
        await this.page.getByRole('button', { name: 'Save Mappings' }).click();
        
        // Wait for the save to complete
        await this.page.waitForTimeout(3000);
        
        return true;
      } else {
        console.log('No fields available for mapping');
        return false;
      }
    } catch (error) {
      console.error(`Failed to configure mapping: ${error}`);
      throw error;
    }
  }

  async startSync(resourceType: 'products' | 'orders' | 'customers') {
    try {
      // Select the resource type
      await this.page.getByLabel(resourceType.charAt(0).toUpperCase() + resourceType.slice(1)).check();
      
      // Wait for the sync options to load
      await this.page.waitForTimeout(1000);
      
      // Click the Start Sync button
      await this.page.getByRole('button', { name: 'Start Sync' }).click();
      
      // Wait for the confirmation dialog
      await this.page.waitForTimeout(1000);
      
      // Confirm the sync
      await this.page.getByRole('button', { name: 'Confirm' }).click();
      
      // Wait for the sync to start
      await this.page.waitForTimeout(5000);
      
      return true;
    } catch (error) {
      console.error(`Failed to start sync: ${error}`);
      throw error;
    }
  }

  async filterSyncHistory(resourceType?: 'products' | 'orders' | 'customers', status?: string) {
    try {
      if (resourceType) {
        // Select the resource type filter
        await this.page.getByLabel('Resource Type').click();
        await this.page.getByRole('option', { name: resourceType.charAt(0).toUpperCase() + resourceType.slice(1) }).click();
        
        // Wait for the filter to apply
        await this.page.waitForTimeout(1000);
      }
      
      if (status) {
        // Select the status filter
        await this.page.getByLabel('Status').click();
        await this.page.getByRole('option', { name: status }).click();
        
        // Wait for the filter to apply
        await this.page.waitForTimeout(1000);
      }
      
      return true;
    } catch (error) {
      console.error(`Failed to filter sync history: ${error}`);
      throw error;
    }
  }

  async searchSyncHistory(query: string) {
    try {
      // Enter the search query
      await this.page.getByPlaceholder('Search by ID or description').fill(query);
      
      // Press Enter to submit the search
      await this.page.keyboard.press('Enter');
      
      // Wait for the search results to load
      await this.page.waitForTimeout(2000);
      
      return true;
    } catch (error) {
      console.error(`Failed to search sync history: ${error}`);
      throw error;
    }
  }
}
