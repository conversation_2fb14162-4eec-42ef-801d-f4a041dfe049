import { Page, expect, test } from '@playwright/test';

import { TeamBillingPageObject } from './team-billing.po';

test.describe('Team Billing', () => {
  let page: Page;
  let po: TeamBillingPageObject;

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    po = new TeamBillingPageObject(page);
  });

  test('a team can subscribe to a plan', async () => {
    await po.setup();
    await po.teamAccounts.goToBilling();

    // Sử dụng hàm completeSubscription để hoàn tất quá trình đăng ký
    const subscriptionSuccess = await po.billing.completeSubscription(0);
    expect(subscriptionSuccess).toBeTruthy();

    // Kiểm tra lại trạng thái đăng ký
    await expect(po.billing.getStatus()).toBeVisible();
    await expect(po.billing.manageBillingButton()).toBeVisible();
  });
});
