import { test, expect } from '@playwright/test';
import { OrdersPageObject } from './orders.po';

// Skip tests that require authentication
const conditionalTest = test.skip;

test.describe.skip('Orders Page', () => {
  let po: OrdersPageObject;

  test.beforeEach(async ({ page }) => {
    po = new OrdersPageObject(page);
  });

  test('should display orders page', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Check if the page title is correct
    await expect(po.page.locator('h1')).toContainText('Orders');

    // Check if the search input is visible
    await expect(po.page.locator('[data-testid="order-search-input"]')).toBeVisible();
  });

  test('should display order table', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Check if the table is visible
    await expect(po.page.locator('table')).toBeVisible();

    // Check if the table headers are correct
    const headers = po.page.locator('table thead th');
    await expect(headers.nth(0)).toContainText('ID');
    await expect(headers.nth(1)).toContainText('Customer');
    await expect(headers.nth(2)).toContainText('Product');
    await expect(headers.nth(3)).toContainText('Quantity');
    await expect(headers.nth(4)).toContainText('Total');
  });

  test('should search for orders', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Get initial count of orders
    const initialCount = await po.getOrdersCount();

    // Search for an order
    await po.searchOrder('test');

    // Check if the search results are displayed
    const searchCount = await po.getOrdersCount();
    
    // Note: This test might need adjustment based on your test data
    console.log(`Initial count: ${initialCount}, Search count: ${searchCount}`);
  });

  test('should navigate to order details', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Check if there are any orders
    const ordersCount = await po.getOrdersCount();
    
    if (ordersCount > 0) {
      // View the first order's details
      await po.viewOrderDetails(0);
      
      // Check if we're on the order details page
      await expect(po.page.url()).toMatch(/\/orders\/[^/]+$/);
    } else {
      console.log('No orders available to test details view');
    }
  });

  test('should handle pagination if available', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Check if pagination is visible
    const hasPagination = await po.hasPagination();
    
    if (hasPagination) {
      // Go to page 2
      await po.goToPage(2);
      
      // Check if the URL contains page=2
      await expect(po.page.url()).toContain('page=2');
    } else {
      console.log('Pagination not available, skipping test');
    }
  });

  test('should filter orders by status', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Filter by status
    await po.filterByStatus('completed');
    
    // Check if the URL contains status=completed
    await expect(po.page.url()).toContain('status=completed');
  });

  test('should filter orders by date range', async () => {
    // Setup and navigate to orders page
    const { slug } = await po.setup();

    // Filter by date range
    const startDate = '2023-01-01';
    const endDate = '2023-12-31';
    await po.filterByDateRange(startDate, endDate);
    
    // Check if the URL contains startDate and endDate
    await expect(po.page.url()).toContain(`startDate=${startDate}`);
    await expect(po.page.url()).toContain(`endDate=${endDate}`);
  });
});
