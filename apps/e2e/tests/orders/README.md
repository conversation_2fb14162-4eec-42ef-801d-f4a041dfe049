# Orders Page E2E Tests

This directory contains E2E tests for the Orders page and Order Detail page.

## Test Files

### Orders Page
- `orders.po.ts`: Page Object for the Orders page
- `orders.spec.ts`: Full E2E tests for the Orders page (skipped because they require authentication)
- `orders-auth.spec.ts`: Tests for the Orders page assuming the user is already authenticated (skipped because they require manual authentication)
- `orders-unauthenticated.spec.ts`: Tests for the Orders page when not authenticated (redirects to login page)

### Order Detail Page
- `order-detail.po.ts`: Page Object for the Order Detail page
- `order-detail-auth.spec.ts`: Tests for the Order Detail page assuming the user is already authenticated (skipped because they require manual authentication)
- `order-detail-unauthenticated.spec.ts`: Tests for the Order Detail page when not authenticated (redirects to login page)

## Running the Tests

### Unauthenticated Tests

#### Orders Page
```bash
cd apps/e2e
npx playwright test tests/orders/orders-unauthenticated.spec.ts
```

#### Order Detail Page
```bash
cd apps/e2e
npx playwright test tests/orders/order-detail-unauthenticated.spec.ts
```

These tests verify that unauthenticated users are redirected to the login page.

### Manual Tests (Requires Pre-authentication)

#### Orders Page
1. Start the application
2. Log in manually
3. Navigate to the orders page
4. Run the tests:

```bash
cd apps/e2e
npx playwright test tests/orders/orders-auth.spec.ts
```

#### Order Detail Page
1. Start the application
2. Log in manually
3. Navigate to the order detail page
4. Run the tests:

```bash
cd apps/e2e
npx playwright test tests/orders/order-detail-auth.spec.ts
```

## Test Coverage

### Orders Page
- Basic page structure
- Search functionality
- Table headers
- Pagination (if available)
- Order details view
- Order filtering by status
- Order filtering by date range

### Order Detail Page
- Basic page structure
- Invoice header information
- Invoice items table
- Functional buttons (Print, Approve, Cancel, PDF)

## Notes

- The full E2E tests require authentication, which is handled automatically by the test framework
- The UI tests are skipped because they require authentication
- The manual tests assume the user is already authenticated and on the respective pages
