# API Testing Framework

## Tổng quan

Thư mục này chứa framework test API cho dự án. Framework này được thiết kế để test các API thật của ứng dụng, đảm bảo chúng hoạt động đúng như mong đợi trong môi trường thực tế.

## Cấu trúc thư mục

```
test-api/
├── __tests__/            # Chứa các test case cho từng API
│   └── api/              # Test cho các API endpoint
│       ├── auth/         # Test cho API xác thực
│       ├── products/     # Test cho API sản phẩm
│       ├── orders/       # Test cho API đơn hàng
│       └── categories/   # Test cho API danh mục
├── scripts/              # Script hỗ trợ
│   └── auth/             # Script xác thực
│       ├── get-zalo-token.js       # Lấy token Zalo
│       ├── get-team-info.js        # Lấy thông tin team
│       └── get-account-theme.js    # Lấy thông tin theme
├── utils/                # Utility functions
│   ├── api-client.ts     # Client gọi API
│   ├── auth.ts           # Xử lý xác thực
│   └── test-utils.ts     # Các utility function cho test
├── jest.config.js        # Cấu hình Jest
└── setup-after-env.ts    # Setup môi trường test
```

## Quy trình phát triển API và viết test

### 1. Hiểu yêu cầu và thiết kế API

- Xác định rõ mục đích và chức năng của API
- Xác định các endpoint, method, request/response format
- Xác định các trường hợp sử dụng và edge case

### 2. Phát triển API

- Tạo file route.ts trong thư mục app/api/[feature]
- Implement các handler cho các method (GET, POST, PUT, DELETE)
- Xử lý validation, authentication, authorization
- Kết nối với database và xử lý dữ liệu

### 3. Viết test API

- Tạo file test trong thư mục __tests__/api/[feature]
- Implement các test case cho từng endpoint
- Test happy path và các edge case
- Đảm bảo test có thể chạy trong môi trường không có dữ liệu

### 4. Chạy và debug test

- Chạy test bằng lệnh `pnpm --filter web-e2e test:api:[feature]`
- Debug lỗi nếu có
- Fix lỗi và chạy lại test đến khi pass

## Cách chạy test

### Chạy tất cả các test API

```bash
pnpm --filter web-e2e test:api
```

### Chạy test cho từng module

Bạn có thể chạy test cho từng module bằng cách sử dụng các script đã được định nghĩa:

```bash
pnpm --filter web-e2e test:api:auth      # Chạy test xác thực
pnpm --filter web-e2e test:api:products  # Chạy test API products
pnpm --filter web-e2e test:api:orders    # Chạy test API orders
pnpm --filter web-e2e test:api:categories # Chạy test API categories
```

## Luồng xử lý test API

1. **Setup môi trường test**:
   - Kiểm tra xem có theme ID không
   - Nếu không có theme ID, kiểm tra xem có team slug không
   - Nếu không có team slug, tự động chạy `test:sample-data` để tạo dữ liệu mẫu
   - Sau khi có team slug, tự động chạy `api:get-team-info` để lấy team ID và theme ID
   - Sau khi có theme ID, tiếp tục quá trình xác thực với Zalo

2. **Xác thực**:
   - Sử dụng token Zalo để lấy NEXT_AUTH_TOKEN
   - Sử dụng NEXT_AUTH_TOKEN để gọi API

3. **Chạy test**:
   - Chạy test auth/zalo.spec.ts trước để đảm bảo có NEXT_AUTH_TOKEN
   - Sau đó chạy các test khác

4. **Xử lý kết quả**:
   - Nếu test pass, hiển thị thông báo thành công
   - Nếu test fail, hiển thị thông báo lỗi và debug

## Làm mới token Zalo Mini App

Khi token Zalo Mini App hết hạn, bạn cần làm mới token để tiếp tục chạy test. Có hai cách để làm mới token:

### Cách 1: Sử dụng script làm mới token

```bash
pnpm --filter web-e2e api:get-zalo-token
```

Script sẽ sử dụng token Zalo Mini App từ biến môi trường hoặc file `.env.test` để xác thực và lấy token NextJS mới.

### Cách 2: Cung cấp token Zalo Mini App mới

```bash
ZALO_MINI_APP_ACCESS_TOKEN=your_new_token pnpm --filter web-e2e api:get-zalo-token
```

Hoặc bạn có thể cập nhật token trong file `.env.test`:

```
ZALO_MINI_APP_ACCESS_TOKEN=your_new_token
```

## Tạo dữ liệu mẫu và chạy toàn bộ quy trình test

### Tạo dữ liệu mẫu

```bash
pnpm --filter web-e2e test:sample-data
```

Lệnh này sẽ chạy test E2E để tạo team, sản phẩm, danh mục, v.v.

### Chạy toàn bộ quy trình test

```bash
pnpm --filter web-e2e test:full-flow
```

Lệnh này sẽ tạo dữ liệu mẫu và chạy test API.

### Chạy script tự động hóa toàn bộ quy trình

```bash
pnpm --filter web-e2e test:full
```

Lệnh này sẽ chạy script tự động hóa toàn bộ quy trình, bao gồm:

1. Kiểm tra và khởi động server nếu cần
2. Tạo dữ liệu mẫu
3. Làm mới token
4. Chạy test API
5. Dọn dẹp

## Các lưu ý quan trọng

1. **Xử lý dữ liệu không tồn tại**:
   - Test API phải xử lý trường hợp không có dữ liệu
   - Hiển thị thông báo thông tin thay vì cảnh báo
   - Không fail test khi không có dữ liệu

2. **Xử lý lỗi**:
   - Bắt và xử lý lỗi một cách graceful
   - Hiển thị thông báo lỗi rõ ràng
   - Đề xuất cách fix lỗi

3. **Tối ưu hóa**:
   - Tránh trùng lặp code
   - Sử dụng utility function
   - Đảm bảo test chạy nhanh và ổn định

## Quy trình tự động hóa cho AI

1. **Hiểu yêu cầu**:
   - Phân tích yêu cầu và xác định các endpoint cần test
   - Xác định các test case cần thiết

2. **Tạo file test**:
   - Tạo file test trong thư mục __tests__/api/[feature]
   - Sử dụng template từ file spec mẫu

3. **Implement test**:
   - Implement các test case
   - Đảm bảo xử lý các trường hợp không có dữ liệu

4. **Chạy và debug**:
   - Chạy test và xem kết quả
   - Debug lỗi nếu có
   - Fix lỗi và chạy lại test đến khi pass

5. **Tối ưu hóa**:
   - Refactor code để tối ưu
   - Đảm bảo test chạy nhanh và ổn định
