import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse the request body
    const { oaConfigId } = await req.json();

    if (!oaConfigId) {
      return new Response(
        JSON.stringify({ error: 'OA Config ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    // Get OA configuration
    const { data: oaConfig, error: oaConfigError } = await supabase
      .from('oa_configurations')
      .select('*')
      .eq('id', oaConfigId)
      .single();

    if (oaConfigError || !oaConfig) {
      return new Response(
        JSON.stringify({ error: 'OA configuration not found', details: oaConfigError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      );
    }

    // Check if we have a valid access token
    if (!oaConfig.access_token) {
      return new Response(
        JSON.stringify({ error: 'No access token available' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    // Refresh token if needed
    let accessToken = oaConfig.access_token;
    const now = Math.floor(Date.now() / 1000);
    
    if (oaConfig.expires_at && now >= oaConfig.expires_at) {
      if (!oaConfig.refresh_token) {
        return new Response(
          JSON.stringify({ error: 'No refresh token available' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          }
        );
      }

      // Refresh the token
      const refreshResponse = await fetch('https://oauth.zaloapp.com/v4/oa/access_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          refresh_token: oaConfig.refresh_token,
          app_id: oaConfig.app_id,
          grant_type: 'refresh_token',
        }),
      });

      const refreshData = await refreshResponse.json();

      if (refreshData.error !== 0) {
        return new Response(
          JSON.stringify({ error: 'Failed to refresh token', details: refreshData }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 401,
          }
        );
      }

      // Update the access token
      accessToken = refreshData.access_token;

      // Update the OA configuration with the new tokens
      const expiresIn = refreshData.expires_in || 86400; // Default to 24 hours
      const expiresAt = now + expiresIn;

      await supabase
        .from('oa_configurations')
        .update({
          access_token: refreshData.access_token,
          refresh_token: refreshData.refresh_token,
          expires_in: expiresIn,
          expires_at: expiresAt,
        })
        .eq('id', oaConfigId);
    }

    // Get templates from Zalo API
    const response = await fetch('https://business.openapi.zalo.me/template/all', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'access_token': accessToken,
      },
    });

    const data = await response.json();

    if (data.error !== 0) {
      return new Response(
        JSON.stringify({ error: 'Failed to get templates', details: data }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    // Return the templates
    return new Response(
      JSON.stringify({ data: data.data.templates || [] }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
