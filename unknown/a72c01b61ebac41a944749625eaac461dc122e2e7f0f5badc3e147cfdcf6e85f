import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from '@/lib/integrations/connectors/ipos/ipos-connector';

const logger = getLogger({ service: 'api-ipos-orders-estimate-ship-fee' });

/**
 * API bắc cầu để ước tính phí vận chuyển từ iPOS
 * @param request Request
 */
export async function POST(request: NextRequest) {
  try {
    // Lấy dữ liệu từ body
    const body = await request.json();
    const { accountId, posId, address, items } = body;

    // Kiểm tra tham số bắt buộc
    if (!accountId || !posId || !address || !items) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // L<PERSON><PERSON> thông tin tích hợp iPOS
    const integration = await prisma.integrations.findFirst({
      where: {
        account_id: accountId,
        platform: 'ipos',
        status: 'active'
      }
    });

    if (!integration) {
      return NextResponse.json(
        { success: false, message: 'No active iPOS integration found' },
        { status: 404 }
      );
    }

    // Lấy cấu hình từ integration
    const config = integration.config as any;
    const credentials = {
      access_token: config.access_token,
      pos_parent: config.pos_parent,
      pos_id: config.pos_id,
      baseUrl: config.baseUrl || 'https://api.foodbook.vn'
    };

    // Khởi tạo connector
    const connector = new IPOSConnector(credentials, logger);

    // Gọi API iPOS để ước tính phí vận chuyển
    const response = await connector.estimateShipFee({
      pos_id: posId,
      address: address,
      items: items
    });

    // Trả về kết quả
    return NextResponse.json({
      success: true,
      data: response.data
    });
  } catch (error) {
    logger.error({ error }, 'Error estimating shipping fee from iPOS');
    return NextResponse.json(
      { success: false, message: (error as Error).message },
      { status: 500 }
    );
  }
}
